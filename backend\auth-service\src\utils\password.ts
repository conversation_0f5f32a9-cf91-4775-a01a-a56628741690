import argon2 from 'argon2';
import zxcvbn from 'zxcvbn';
import crypto from 'crypto';

// Argon2id configuration as per requirements
const ARGON2_CONFIG = {
  type: argon2.argon2id,
  memoryCost: 65536, // 64MB in KB
  timeCost: 3, // 3 iterations
  parallelism: 4, // 4 parallel threads
  hashLength: 32, // 32 bytes output
  saltLength: 16, // 16 bytes salt
};

// Password requirements
export const PASSWORD_REQUIREMENTS = {
  minLength: 12,
  requireUppercase: true,
  requireLowercase: true,
  requireNumbers: true,
  requireSpecialChars: true,
  maxLength: 128,
  commonPasswords: [
    'password123', 'admin123', 'qwerty123', 'password1',
    '123456789', 'welcome123', 'letmein123', 'monkey123'
  ]
};

export interface PasswordStrength {
  score: number; // 0-4 (zxcvbn score)
  feedback: {
    warning: string;
    suggestions: string[];
  };
  isValid: boolean;
  requirements: {
    minLength: boolean;
    hasUppercase: boolean;
    hasLowercase: boolean;
    hasNumbers: boolean;
    hasSpecialChars: boolean;
    notCommon: boolean;
  };
}

/**
 * Hash password using Argon2id with specified configuration
 */
export async function hashPassword(password: string): Promise<string> {
  try {
    const hash = await argon2.hash(password, ARGON2_CONFIG);
    return hash;
  } catch (error) {
    throw new Error('Failed to hash password');
  }
}

/**
 * Verify password against hash using Argon2id
 */
export async function verifyPassword(password: string, hash: string): Promise<boolean> {
  try {
    return await argon2.verify(hash, password);
  } catch (error) {
    return false;
  }
}

/**
 * Validate password against requirements
 */
export function validatePassword(password: string): PasswordStrength {
  const requirements = {
    minLength: password.length >= PASSWORD_REQUIREMENTS.minLength,
    hasUppercase: /[A-Z]/.test(password),
    hasLowercase: /[a-z]/.test(password),
    hasNumbers: /\d/.test(password),
    hasSpecialChars: /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password),
    notCommon: !PASSWORD_REQUIREMENTS.commonPasswords.includes(password.toLowerCase())
  };

  // Use zxcvbn for advanced password strength analysis
  const zxcvbnResult = zxcvbn(password);

  const isValid = Object.values(requirements).every(req => req) && 
                  password.length <= PASSWORD_REQUIREMENTS.maxLength &&
                  zxcvbnResult.score >= 2; // Minimum score of 2 (fair)

  return {
    score: zxcvbnResult.score,
    feedback: {
      warning: zxcvbnResult.feedback.warning || '',
      suggestions: zxcvbnResult.feedback.suggestions || []
    },
    isValid,
    requirements
  };
}

/**
 * Generate secure random password
 */
export function generateSecurePassword(length: number = 16): string {
  const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
  const lowercase = 'abcdefghijklmnopqrstuvwxyz';
  const numbers = '0123456789';
  const specialChars = '!@#$%^&*()_+-=[]{}|;:,.<>?';
  
  const allChars = uppercase + lowercase + numbers + specialChars;
  
  let password = '';
  
  // Ensure at least one character from each category
  password += uppercase[crypto.randomInt(0, uppercase.length)];
  password += lowercase[crypto.randomInt(0, lowercase.length)];
  password += numbers[crypto.randomInt(0, numbers.length)];
  password += specialChars[crypto.randomInt(0, specialChars.length)];
  
  // Fill the rest randomly
  for (let i = 4; i < length; i++) {
    password += allChars[crypto.randomInt(0, allChars.length)];
  }
  
  // Shuffle the password
  return password.split('').sort(() => Math.random() - 0.5).join('');
}

/**
 * Generate password reset token
 */
export function generateResetToken(): { token: string; hash: string } {
  const token = crypto.randomBytes(32).toString('hex');
  const hash = crypto.createHash('sha256').update(token).digest('hex');
  
  return { token, hash };
}

/**
 * Generate email verification token
 */
export function generateVerificationToken(): string {
  return crypto.randomBytes(32).toString('hex');
}

/**
 * Check if password has been compromised (basic implementation)
 * In production, you might want to integrate with HaveIBeenPwned API
 */
export function isPasswordCompromised(password: string): boolean {
  // Basic check against common passwords
  const commonPasswords = [
    'password', '123456', '123456789', 'qwerty', 'abc123',
    'password123', 'admin', 'letmein', 'welcome', 'monkey',
    '1234567890', 'dragon', 'sunshine', 'princess', 'football'
  ];
  
  return commonPasswords.includes(password.toLowerCase());
}

/**
 * Calculate password entropy
 */
export function calculatePasswordEntropy(password: string): number {
  let charsetSize = 0;
  
  if (/[a-z]/.test(password)) charsetSize += 26;
  if (/[A-Z]/.test(password)) charsetSize += 26;
  if (/\d/.test(password)) charsetSize += 10;
  if (/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) charsetSize += 32;
  
  return Math.log2(Math.pow(charsetSize, password.length));
}

/**
 * Generate password strength meter data for frontend
 */
export function getPasswordStrengthMeter(password: string): {
  score: number;
  label: string;
  color: string;
  percentage: number;
  feedback: string[];
} {
  const strength = validatePassword(password);
  const entropy = calculatePasswordEntropy(password);
  
  const labels = ['Very Weak', 'Weak', 'Fair', 'Good', 'Strong'];
  const colors = ['#ff4757', '#ff6b7a', '#ffa502', '#2ed573', '#20bf6b'];
  
  let feedback: string[] = [];
  
  if (!strength.requirements.minLength) {
    feedback.push(`Password must be at least ${PASSWORD_REQUIREMENTS.minLength} characters long`);
  }
  if (!strength.requirements.hasUppercase) {
    feedback.push('Add uppercase letters');
  }
  if (!strength.requirements.hasLowercase) {
    feedback.push('Add lowercase letters');
  }
  if (!strength.requirements.hasNumbers) {
    feedback.push('Add numbers');
  }
  if (!strength.requirements.hasSpecialChars) {
    feedback.push('Add special characters');
  }
  if (!strength.requirements.notCommon) {
    feedback.push('Avoid common passwords');
  }
  
  // Add zxcvbn suggestions
  feedback.push(...strength.feedback.suggestions);
  
  return {
    score: strength.score,
    label: labels[strength.score] || 'Very Weak',
    color: colors[strength.score] || '#ff4757',
    percentage: (strength.score / 4) * 100,
    feedback: feedback.slice(0, 3) // Limit to 3 suggestions
  };
}
