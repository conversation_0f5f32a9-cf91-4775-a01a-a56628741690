import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Alert,
} from 'react-native';
import {
  Bell,
  Settings,
  TestTube,
  Trophy,
  MapPin,
  Camera,
  User,
} from 'lucide-react-native';

// Import notification components
import NotificationManager, { useNotificationManager } from '../components/NotificationManager';
import NotificationSettings from '../components/NotificationSettings';
import NotificationTestingPanel from '../components/NotificationTestingPanel';
import { NotificationTriggerService } from '../services/NotificationTriggerService';

/**
 * Complete example of how to integrate the Push Notification System
 * into your Bioscan+ app. This shows all the key integration points.
 */

// Main App Component with Notification Integration
export const AppWithNotifications: React.FC = () => {
  return (
    <NotificationManager
      onNotificationReceived={(notification) => {
        console.log('📱 App received notification:', notification.request.content.title);
        // Handle foreground notifications here
      }}
      onNotificationTapped={(response) => {
        console.log('👆 User tapped notification:', response.notification.request.content.title);
        // Handle navigation based on notification data
        handleNotificationNavigation(response);
      }}
    >
      <MainAppContent />
    </NotificationManager>
  );
};

// Main app content that uses notifications
const MainAppContent: React.FC = () => {
  const [showSettings, setShowSettings] = useState(false);
  const [showTesting, setShowTesting] = useState(false);
  const [userStats, setUserStats] = useState({
    totalIdentifications: 0,
    currentStreak: 0,
    newSpeciesCount: 0,
  });

  const notificationManager = useNotificationManager();
  const triggerService = NotificationTriggerService.getInstance();

  useEffect(() => {
    // Load user stats on app start
    loadUserStats();
  }, []);

  const loadUserStats = async () => {
    // This would load from your database
    setUserStats({
      totalIdentifications: 42,
      currentStreak: 7,
      newSpeciesCount: 15,
    });
  };

  // Example: Trigger notification when user identifies a species
  const handleSpeciesIdentification = async (speciesData: any) => {
    try {
      // Update local stats
      const newStats = {
        ...userStats,
        totalIdentifications: userStats.totalIdentifications + 1,
        newSpeciesCount: speciesData.isFirstTime ? userStats.newSpeciesCount + 1 : userStats.newSpeciesCount,
        currentStreak: userStats.currentStreak + 1,
      };
      setUserStats(newStats);

      // Trigger achievement notifications
      await triggerService.onNewSpeciesIdentified({
        speciesName: speciesData.scientificName,
        commonName: speciesData.commonName,
        isFirstTime: speciesData.isFirstTime,
        totalIdentifications: newStats.totalIdentifications,
        userId: 'current-user-id', // Get from auth context
      });

      // Trigger streak notifications
      await triggerService.onIdentificationStreak({
        streakCount: newStats.currentStreak,
        userId: 'current-user-id',
        lastIdentificationDate: new Date().toISOString(),
      });

      // Show success message
      Alert.alert(
        'Species Identified! 🎉',
        `You've identified ${speciesData.commonName}${speciesData.isFirstTime ? ' for the first time!' : '!'}`
      );

    } catch (error) {
      console.error('Error handling species identification:', error);
    }
  };

  // Example: Trigger location-based notifications
  const handleLocationUpdate = async (location: { latitude: number; longitude: number }) => {
    try {
      await triggerService.onLocationUpdate({
        latitude: location.latitude,
        longitude: location.longitude,
        userId: 'current-user-id',
      });
    } catch (error) {
      console.error('Error handling location update:', error);
    }
  };

  // Example: Send achievement notification
  const simulateAchievement = async () => {
    await notificationManager.sendAchievement({
      species_name: 'Red Cardinal',
      achievement_type: 'first_identification',
      total_count: userStats.totalIdentifications,
    });
  };

  // Example: Send reminder notification
  const simulateReminder = async () => {
    await notificationManager.sendReminder({
      species_count: 5,
      location_name: 'Central Park',
      reminder_type: 'nearby_species',
    });
  };

  return (
    <ScrollView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.title}>Bioscan+ Notifications</Text>
        <Text style={styles.subtitle}>Comprehensive Push Notification System</Text>
      </View>

      {/* User Stats */}
      <View style={styles.statsContainer}>
        <View style={styles.statCard}>
          <Camera size={24} color="#3B82F6" />
          <Text style={styles.statNumber}>{userStats.totalIdentifications}</Text>
          <Text style={styles.statLabel}>Total IDs</Text>
        </View>
        
        <View style={styles.statCard}>
          <Trophy size={24} color="#F59E0B" />
          <Text style={styles.statNumber}>{userStats.currentStreak}</Text>
          <Text style={styles.statLabel}>Day Streak</Text>
        </View>
        
        <View style={styles.statCard}>
          <User size={24} color="#10B981" />
          <Text style={styles.statNumber}>{userStats.newSpeciesCount}</Text>
          <Text style={styles.statLabel}>New Species</Text>
        </View>
      </View>

      {/* Notification Controls */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Notification Controls</Text>
        
        <TouchableOpacity
          style={styles.actionButton}
          onPress={() => setShowSettings(true)}
        >
          <Settings size={20} color="#3B82F6" />
          <Text style={styles.actionButtonText}>Notification Settings</Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={styles.actionButton}
          onPress={() => setShowTesting(true)}
        >
          <TestTube size={20} color="#8B5CF6" />
          <Text style={styles.actionButtonText}>Testing Panel</Text>
        </TouchableOpacity>
      </View>

      {/* Simulation Actions */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Simulate User Actions</Text>
        
        <TouchableOpacity
          style={styles.simulationButton}
          onPress={() => handleSpeciesIdentification({
            scientificName: 'Cardinalis cardinalis',
            commonName: 'Northern Cardinal',
            isFirstTime: Math.random() > 0.5,
          })}
        >
          <Camera size={20} color="white" />
          <Text style={styles.simulationButtonText}>Identify Species</Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={styles.simulationButton}
          onPress={() => handleLocationUpdate({
            latitude: 40.7128 + (Math.random() - 0.5) * 0.01,
            longitude: -74.0060 + (Math.random() - 0.5) * 0.01,
          })}
        >
          <MapPin size={20} color="white" />
          <Text style={styles.simulationButtonText}>Update Location</Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={styles.simulationButton}
          onPress={simulateAchievement}
        >
          <Trophy size={20} color="white" />
          <Text style={styles.simulationButtonText}>Send Achievement</Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={styles.simulationButton}
          onPress={simulateReminder}
        >
          <Bell size={20} color="white" />
          <Text style={styles.simulationButtonText}>Send Reminder</Text>
        </TouchableOpacity>
      </View>

      {/* Integration Examples */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Integration Examples</Text>
        
        <View style={styles.exampleCard}>
          <Text style={styles.exampleTitle}>Species Identification Flow</Text>
          <Text style={styles.exampleDescription}>
            When a user identifies a species, the app automatically:
            {'\n'}• Checks if it's a new species for the user
            {'\n'}• Sends achievement notification for first-time discoveries
            {'\n'}• Updates identification streak
            {'\n'}• Triggers milestone notifications (10, 50, 100+ species)
          </Text>
        </View>
        
        <View style={styles.exampleCard}>
          <Text style={styles.exampleTitle}>Location-Based Notifications</Text>
          <Text style={styles.exampleDescription}>
            When user location changes, the app:
            {'\n'}• Finds nearby species they haven't identified
            {'\n'}• Sends location-based reminders (max 1 per day)
            {'\n'}• Suggests exploration opportunities
            {'\n'}• Respects user privacy preferences
          </Text>
        </View>
        
        <View style={styles.exampleCard}>
          <Text style={styles.exampleTitle}>Daily Engagement</Text>
          <Text style={styles.exampleDescription}>
            The notification system maintains engagement by:
            {'\n'}• Sending daily nature tips at user's preferred time
            {'\n'}• Tracking identification streaks
            {'\n'}• Sending re-engagement notifications for inactive users
            {'\n'}• Personalizing content based on user interests
          </Text>
        </View>
      </View>

      {/* Modals */}
      {showSettings && (
        <View style={styles.modal}>
          <NotificationSettings
            onPreferencesChange={(preferences) => {
              console.log('Preferences updated:', preferences);
            }}
          />
          <TouchableOpacity
            style={styles.closeButton}
            onPress={() => setShowSettings(false)}
          >
            <Text style={styles.closeButtonText}>Close Settings</Text>
          </TouchableOpacity>
        </View>
      )}

      <NotificationTestingPanel
        isVisible={showTesting}
        onClose={() => setShowTesting(false)}
      />
    </ScrollView>
  );
};

// Helper function to handle notification navigation
const handleNotificationNavigation = (response: any) => {
  const { data } = response.notification.request.content;
  
  if (data?.actionUrl) {
    // Handle different types of navigation
    switch (data.actionUrl) {
      case '/explore':
        // Navigate to explore screen
        console.log('Navigating to explore screen');
        break;
      case '/achievements':
        // Navigate to achievements screen
        console.log('Navigating to achievements screen');
        break;
      case '/profile':
        // Navigate to profile screen
        console.log('Navigating to profile screen');
        break;
      default:
        console.log('Unknown navigation action:', data.actionUrl);
    }
  }
  
  // Track notification interaction
  if (data?.notificationId) {
    console.log('Tracking notification interaction:', data.notificationId);
  }
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  header: {
    backgroundColor: '#3B82F6',
    padding: 24,
    paddingTop: 60,
    alignItems: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: 'white',
  },
  subtitle: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.8)',
    marginTop: 4,
  },
  statsContainer: {
    flexDirection: 'row',
    padding: 20,
    gap: 12,
  },
  statCard: {
    flex: 1,
    backgroundColor: 'white',
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  statNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#111827',
    marginTop: 8,
  },
  statLabel: {
    fontSize: 12,
    color: '#6B7280',
    marginTop: 4,
  },
  section: {
    margin: 20,
    marginTop: 0,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 16,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'white',
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    gap: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  actionButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#374151',
  },
  simulationButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#10B981',
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    gap: 8,
  },
  simulationButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: 'white',
  },
  exampleCard: {
    backgroundColor: 'white',
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  exampleTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 8,
  },
  exampleDescription: {
    fontSize: 14,
    color: '#6B7280',
    lineHeight: 20,
  },
  modal: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'white',
    zIndex: 1000,
  },
  closeButton: {
    backgroundColor: '#EF4444',
    margin: 20,
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  closeButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default AppWithNotifications;
