import AsyncStorage from '@react-native-async-storage/async-storage';
import { tursoService } from '../lib/turso';

/**
 * Storage adapter that provides a unified interface for both AsyncStorage and Turso
 * This allows for gradual migration and fallback mechanisms
 */
export class StorageAdapter {
  private static instance: StorageAdapter;
  private useTurso = false;
  private userId: string | null = null;

  private constructor() {}

  public static getInstance(): StorageAdapter {
    if (!StorageAdapter.instance) {
      StorageAdapter.instance = new StorageAdapter();
    }
    return StorageAdapter.instance;
  }

  /**
   * Initialize the storage adapter
   */
  public async initialize(userId?: string, forceTurso: boolean = false): Promise<void> {
    this.userId = userId || null;
    
    try {
      // Check if Turso is available and migration is completed
      const migrationCompleted = await tursoService.getAppSetting('migration_completed');
      this.useTurso = forceTurso || (migrationCompleted === true);
      
      console.log(`Storage adapter initialized - Using ${this.useTurso ? 'Turso' : 'AsyncStorage'}`);
    } catch (error) {
      console.warn('Failed to initialize Turso, falling back to AsyncStorage:', error);
      this.useTurso = false;
    }
  }

  /**
   * Set a value in storage
   */
  public async setItem(key: string, value: string): Promise<void> {
    if (this.useTurso && this.userId) {
      try {
        await tursoService.setUserPreference(this.userId, key, value);
        return;
      } catch (error) {
        console.warn('Turso setItem failed, falling back to AsyncStorage:', error);
      }
    }
    
    // Fallback to AsyncStorage
    await AsyncStorage.setItem(key, value);
  }

  /**
   * Get a value from storage
   */
  public async getItem(key: string): Promise<string | null> {
    if (this.useTurso && this.userId) {
      try {
        const value = await tursoService.getUserPreference(this.userId, key);
        if (value !== null) {
          return typeof value === 'string' ? value : JSON.stringify(value);
        }
      } catch (error) {
        console.warn('Turso getItem failed, falling back to AsyncStorage:', error);
      }
    }
    
    // Fallback to AsyncStorage
    return await AsyncStorage.getItem(key);
  }

  /**
   * Remove a value from storage
   */
  public async removeItem(key: string): Promise<void> {
    if (this.useTurso && this.userId) {
      try {
        await tursoService.setUserPreference(this.userId, key, null);
      } catch (error) {
        console.warn('Turso removeItem failed, falling back to AsyncStorage:', error);
      }
    }
    
    // Also remove from AsyncStorage
    await AsyncStorage.removeItem(key);
  }

  /**
   * Get all keys from storage
   */
  public async getAllKeys(): Promise<string[]> {
    if (this.useTurso && this.userId) {
      try {
        const preferences = await tursoService.getAllUserPreferences(this.userId);
        return Object.keys(preferences);
      } catch (error) {
        console.warn('Turso getAllKeys failed, falling back to AsyncStorage:', error);
      }
    }
    
    // Fallback to AsyncStorage
    return await AsyncStorage.getAllKeys();
  }

  /**
   * Get multiple values from storage
   */
  public async multiGet(keys: string[]): Promise<Array<[string, string | null]>> {
    if (this.useTurso && this.userId) {
      try {
        const preferences = await tursoService.getAllUserPreferences(this.userId);
        return keys.map(key => [key, preferences[key] ? JSON.stringify(preferences[key]) : null]);
      } catch (error) {
        console.warn('Turso multiGet failed, falling back to AsyncStorage:', error);
      }
    }
    
    // Fallback to AsyncStorage
    return await AsyncStorage.multiGet(keys);
  }

  /**
   * Set multiple values in storage
   */
  public async multiSet(keyValuePairs: Array<[string, string]>): Promise<void> {
    if (this.useTurso && this.userId) {
      try {
        for (const [key, value] of keyValuePairs) {
          await tursoService.setUserPreference(this.userId, key, value);
        }
        return;
      } catch (error) {
        console.warn('Turso multiSet failed, falling back to AsyncStorage:', error);
      }
    }
    
    // Fallback to AsyncStorage
    await AsyncStorage.multiSet(keyValuePairs);
  }

  /**
   * Remove multiple values from storage
   */
  public async multiRemove(keys: string[]): Promise<void> {
    if (this.useTurso && this.userId) {
      try {
        for (const key of keys) {
          await tursoService.setUserPreference(this.userId, key, null);
        }
      } catch (error) {
        console.warn('Turso multiRemove failed, falling back to AsyncStorage:', error);
      }
    }
    
    // Also remove from AsyncStorage
    await AsyncStorage.multiRemove(keys);
  }

  /**
   * Clear all storage
   */
  public async clear(): Promise<void> {
    if (this.useTurso && this.userId) {
      try {
        // This would require a method to clear all user preferences
        // For now, we'll just clear AsyncStorage
        console.warn('Turso clear not implemented, clearing AsyncStorage only');
      } catch (error) {
        console.warn('Turso clear failed:', error);
      }
    }
    
    await AsyncStorage.clear();
  }

  /**
   * Force switch to Turso storage
   */
  public enableTurso(userId: string): void {
    this.userId = userId;
    this.useTurso = true;
    console.log('Storage adapter switched to Turso mode');
  }

  /**
   * Force switch to AsyncStorage
   */
  public disableTurso(): void {
    this.useTurso = false;
    console.log('Storage adapter switched to AsyncStorage mode');
  }

  /**
   * Check if currently using Turso
   */
  public isUsingTurso(): boolean {
    return this.useTurso;
  }

  /**
   * Sync data between AsyncStorage and Turso
   */
  public async syncStorages(): Promise<void> {
    if (!this.userId) {
      throw new Error('User ID required for storage sync');
    }

    try {
      // Get all AsyncStorage data
      const asyncKeys = await AsyncStorage.getAllKeys();
      const asyncData = await AsyncStorage.multiGet(asyncKeys);
      
      // Sync to Turso
      for (const [key, value] of asyncData) {
        if (value !== null) {
          await tursoService.setUserPreference(this.userId, key, value);
        }
      }
      
      console.log(`Synced ${asyncData.length} items from AsyncStorage to Turso`);
    } catch (error) {
      console.error('Storage sync failed:', error);
      throw error;
    }
  }

  /**
   * Get storage statistics
   */
  public async getStorageStats(): Promise<{
    asyncStorageKeys: number;
    tursoKeys: number;
    usingTurso: boolean;
  }> {
    const asyncKeys = await AsyncStorage.getAllKeys();
    let tursoKeys = 0;
    
    if (this.userId) {
      try {
        const preferences = await tursoService.getAllUserPreferences(this.userId);
        tursoKeys = Object.keys(preferences).length;
      } catch (error) {
        console.warn('Failed to get Turso stats:', error);
      }
    }
    
    return {
      asyncStorageKeys: asyncKeys.length,
      tursoKeys,
      usingTurso: this.useTurso
    };
  }
}

export const storageAdapter = StorageAdapter.getInstance();
