import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  RefreshControl,
  ActivityIndicator,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import {
  Crown,
  Calendar,
  CreditCard,
  Settings,
  TrendingUp,
  AlertCircle,
  CheckCircle,
  XCircle,
  RefreshCw,
  Download,
  Shield,
  BarChart3,
} from 'lucide-react-native';
import { SubscriptionManagementService, UserSubscription, SubscriptionUsage } from '../services/SubscriptionManagementService';
import { PaymentAnalyticsService } from '../services/PaymentAnalyticsService';
import PaymentSecurityIndicator from './PaymentSecurityIndicator';

interface SubscriptionManagementProps {
  onPlanChange?: () => void;
  onCancel?: () => void;
}

export const SubscriptionManagement: React.FC<SubscriptionManagementProps> = ({
  onPlanChange,
  onCancel,
}) => {
  const [subscription, setSubscription] = useState<UserSubscription | null>(null);
  const [usage, setUsage] = useState<SubscriptionUsage[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [actionLoading, setActionLoading] = useState<string | null>(null);

  useEffect(() => {
    loadSubscriptionData();
  }, []);

  const loadSubscriptionData = async () => {
    try {
      const [subData, usageData] = await Promise.all([
        SubscriptionManagementService.getCurrentSubscription(),
        SubscriptionManagementService.getUsageStats()
      ]);

      setSubscription(subData);
      setUsage(usageData);
    } catch (error) {
      console.error('Error loading subscription data:', error);
      Alert.alert('Error', 'Failed to load subscription information');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const onRefresh = () => {
    setRefreshing(true);
    loadSubscriptionData();
  };

  const handleCancelSubscription = async () => {
    Alert.alert(
      'Cancel Subscription',
      'Are you sure you want to cancel your subscription? You will continue to have access until the end of your current billing period.',
      [
        { text: 'Keep Subscription', style: 'cancel' },
        {
          text: 'Cancel',
          style: 'destructive',
          onPress: async () => {
            try {
              setActionLoading('cancel');
              const result = await SubscriptionManagementService.cancelSubscription(true);
              
              if (result.success) {
                Alert.alert('Success', result.message);
                loadSubscriptionData();
                if (onCancel) onCancel();
              } else {
                Alert.alert('Error', result.message);
              }
            } catch (error) {
              Alert.alert('Error', 'Failed to cancel subscription');
            } finally {
              setActionLoading(null);
            }
          }
        }
      ]
    );
  };

  const handleReactivateSubscription = async () => {
    try {
      setActionLoading('reactivate');
      const result = await SubscriptionManagementService.reactivateSubscription();
      
      if (result.success) {
        Alert.alert('Success', result.message);
        loadSubscriptionData();
      } else {
        Alert.alert('Error', result.message);
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to reactivate subscription');
    } finally {
      setActionLoading(null);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return '#10B981';
      case 'trialing': return '#3B82F6';
      case 'past_due': return '#F59E0B';
      case 'canceled': return '#6B7280';
      default: return '#EF4444';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active': return CheckCircle;
      case 'trialing': return Crown;
      case 'past_due': return AlertCircle;
      case 'canceled': return XCircle;
      default: return AlertCircle;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const getUsagePercentage = (current: number, limit: number) => {
    if (limit === -1) return 0; // Unlimited
    return Math.min((current / limit) * 100, 100);
  };

  const getUsageColor = (percentage: number) => {
    if (percentage < 50) return '#10B981';
    if (percentage < 80) return '#F59E0B';
    return '#EF4444';
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#3B82F6" />
        <Text style={styles.loadingText}>Loading subscription...</Text>
      </View>
    );
  }

  if (!subscription) {
    return (
      <View style={styles.noSubscriptionContainer}>
        <Crown size={48} color="#6B7280" />
        <Text style={styles.noSubscriptionTitle}>No Active Subscription</Text>
        <Text style={styles.noSubscriptionText}>
          Subscribe to unlock premium features and unlimited access.
        </Text>
        <TouchableOpacity style={styles.subscribeButton} onPress={onPlanChange}>
          <Text style={styles.subscribeButtonText}>View Plans</Text>
        </TouchableOpacity>
      </View>
    );
  }

  const StatusIcon = getStatusIcon(subscription.status);
  const statusColor = getStatusColor(subscription.status);

  return (
    <ScrollView
      style={styles.container}
      refreshControl={
        <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
      }
    >
      {/* Security Indicator */}
      <PaymentSecurityIndicator />

      {/* Subscription Status Card */}
      <View style={styles.statusCard}>
        <LinearGradient
          colors={[statusColor, `${statusColor}CC`]}
          style={styles.statusGradient}
        >
          <View style={styles.statusHeader}>
            <StatusIcon size={24} color="white" />
            <View style={styles.statusText}>
              <Text style={styles.planName}>{subscription.plan?.name}</Text>
              <Text style={styles.statusLabel}>
                {subscription.status.charAt(0).toUpperCase() + subscription.status.slice(1)}
              </Text>
            </View>
          </View>
          
          <View style={styles.statusDetails}>
            <View style={styles.statusItem}>
              <Calendar size={16} color="rgba(255, 255, 255, 0.8)" />
              <Text style={styles.statusItemText}>
                {subscription.cancel_at_period_end 
                  ? `Ends ${formatDate(subscription.current_period_end)}`
                  : `Renews ${formatDate(subscription.current_period_end)}`
                }
              </Text>
            </View>
            
            {subscription.trial_end && new Date(subscription.trial_end) > new Date() && (
              <View style={styles.statusItem}>
                <Crown size={16} color="rgba(255, 255, 255, 0.8)" />
                <Text style={styles.statusItemText}>
                  Trial ends {formatDate(subscription.trial_end)}
                </Text>
              </View>
            )}
          </View>
        </LinearGradient>
      </View>

      {/* Usage Statistics */}
      <View style={styles.usageCard}>
        <Text style={styles.cardTitle}>Usage Statistics</Text>
        {usage.map((item, index) => {
          const percentage = getUsagePercentage(item.current_usage, item.usage_limit);
          const usageColor = getUsageColor(percentage);
          
          return (
            <View key={index} style={styles.usageItem}>
              <View style={styles.usageHeader}>
                <Text style={styles.usageLabel}>
                  {item.feature_type.replace('_', ' ').toUpperCase()}
                </Text>
                <Text style={styles.usageValue}>
                  {item.current_usage} / {item.usage_limit === -1 ? '∞' : item.usage_limit}
                </Text>
              </View>
              
              <View style={styles.usageBarContainer}>
                <View style={styles.usageBarBackground}>
                  <View 
                    style={[
                      styles.usageBarFill, 
                      { 
                        width: `${percentage}%`, 
                        backgroundColor: usageColor 
                      }
                    ]} 
                  />
                </View>
                <Text style={[styles.usagePercentage, { color: usageColor }]}>
                  {item.usage_limit === -1 ? 'Unlimited' : `${percentage.toFixed(0)}%`}
                </Text>
              </View>
            </View>
          );
        })}
      </View>

      {/* Actions */}
      <View style={styles.actionsCard}>
        <Text style={styles.cardTitle}>Manage Subscription</Text>
        
        <TouchableOpacity 
          style={styles.actionButton}
          onPress={onPlanChange}
          disabled={!!actionLoading}
        >
          <TrendingUp size={20} color="#3B82F6" />
          <Text style={styles.actionButtonText}>Change Plan</Text>
        </TouchableOpacity>

        {subscription.cancel_at_period_end ? (
          <TouchableOpacity 
            style={[styles.actionButton, styles.reactivateButton]}
            onPress={handleReactivateSubscription}
            disabled={!!actionLoading}
          >
            {actionLoading === 'reactivate' ? (
              <ActivityIndicator size="small" color="#10B981" />
            ) : (
              <RefreshCw size={20} color="#10B981" />
            )}
            <Text style={[styles.actionButtonText, { color: '#10B981' }]}>
              Reactivate Subscription
            </Text>
          </TouchableOpacity>
        ) : (
          <TouchableOpacity 
            style={[styles.actionButton, styles.cancelButton]}
            onPress={handleCancelSubscription}
            disabled={!!actionLoading}
          >
            {actionLoading === 'cancel' ? (
              <ActivityIndicator size="small" color="#EF4444" />
            ) : (
              <XCircle size={20} color="#EF4444" />
            )}
            <Text style={[styles.actionButtonText, { color: '#EF4444' }]}>
              Cancel Subscription
            </Text>
          </TouchableOpacity>
        )}

        <TouchableOpacity style={styles.actionButton}>
          <CreditCard size={20} color="#6B7280" />
          <Text style={styles.actionButtonText}>Payment Methods</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.actionButton}>
          <Download size={20} color="#6B7280" />
          <Text style={styles.actionButtonText}>Download Invoice</Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#F9FAFB',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#6B7280',
  },
  noSubscriptionContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
    backgroundColor: '#F9FAFB',
  },
  noSubscriptionTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#111827',
    marginTop: 16,
    marginBottom: 8,
  },
  noSubscriptionText: {
    fontSize: 16,
    color: '#6B7280',
    textAlign: 'center',
    marginBottom: 24,
  },
  subscribeButton: {
    backgroundColor: '#3B82F6',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  subscribeButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  statusCard: {
    margin: 20,
    borderRadius: 16,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  statusGradient: {
    padding: 20,
  },
  statusHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  statusText: {
    marginLeft: 12,
    flex: 1,
  },
  planName: {
    fontSize: 20,
    fontWeight: 'bold',
    color: 'white',
  },
  statusLabel: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.8)',
    marginTop: 2,
  },
  statusDetails: {
    gap: 8,
  },
  statusItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  statusItemText: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.9)',
  },
  usageCard: {
    backgroundColor: 'white',
    margin: 20,
    marginTop: 0,
    borderRadius: 12,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 16,
  },
  usageItem: {
    marginBottom: 16,
  },
  usageHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  usageLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#374151',
  },
  usageValue: {
    fontSize: 14,
    color: '#6B7280',
  },
  usageBarContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  usageBarBackground: {
    flex: 1,
    height: 8,
    backgroundColor: '#E5E7EB',
    borderRadius: 4,
    overflow: 'hidden',
  },
  usageBarFill: {
    height: '100%',
    borderRadius: 4,
  },
  usagePercentage: {
    fontSize: 12,
    fontWeight: '600',
    minWidth: 60,
    textAlign: 'right',
  },
  actionsCard: {
    backgroundColor: 'white',
    margin: 20,
    marginTop: 0,
    borderRadius: 12,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 16,
    borderRadius: 8,
    backgroundColor: '#F9FAFB',
    marginBottom: 12,
    gap: 12,
  },
  actionButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#374151',
    flex: 1,
  },
  reactivateButton: {
    backgroundColor: '#F0FDF4',
  },
  cancelButton: {
    backgroundColor: '#FEF2F2',
  },
});

export default SubscriptionManagement;
