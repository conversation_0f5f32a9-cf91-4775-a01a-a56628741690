# Payment System API Documentation

## Overview

The Bioscan+ payment system provides comprehensive subscription management, fraud detection, and analytics capabilities. This document outlines all available API endpoints, their usage, and security considerations.

## Base URL

All API endpoints are served through Supabase Edge Functions:
```
https://[your-project-id].supabase.co/functions/v1/
```

## Authentication

All endpoints require authentication via J<PERSON><PERSON> token in the Authorization header:
```
Authorization: Bearer <jwt_token>
```

## Endpoints

### 1. Create Checkout Session

Creates a Stripe checkout session for subscription purchase.

**Endpoint:** `POST /create-checkout-session`

**Request Body:**
```json
{
  "planId": "string",
  "billingPeriod": "monthly" | "yearly",
  "userId": "string",
  "userEmail": "string",
  "promoCode": "string" // optional
}
```

**Response:**
```json
{
  "url": "https://checkout.stripe.com/session_id"
}
```

**Security Features:**
- Rate limiting (10 requests/hour, 50 requests/day)
- IP-based fraud detection
- User authentication validation
- Promotional code validation

**Example:**
```javascript
const response = await supabase.functions.invoke('create-checkout-session', {
  body: {
    planId: 'plan_123',
    billingPeriod: 'monthly',
    userId: 'user_456',
    userEmail: '<EMAIL>',
    promoCode: 'WELCOME20'
  }
});
```

### 2. Validate Payment

Performs fraud detection and payment validation before processing.

**Endpoint:** `POST /validate-payment`

**Request Body:**
```json
{
  "paymentMethodId": "string",
  "amount": "number",
  "currency": "string",
  "deviceFingerprint": "string",
  "networkInfo": {
    "type": "string",
    "isConnected": "boolean",
    "isInternetReachable": "boolean"
  }
}
```

**Response:**
```json
{
  "valid": "boolean",
  "riskScore": "number", // 0-100
  "requiresVerification": "boolean",
  "action": "allow" | "review" | "block",
  "riskFactors": {
    "high_amount": "boolean",
    "suspicious_country": "boolean",
    "multiple_recent_failures": "number",
    // ... other risk factors
  }
}
```

**Risk Factors:**
- `high_amount`: Payment amount exceeds threshold
- `suspicious_country`: Payment from high-risk country
- `prepaid_card`: Using prepaid payment method
- `new_user`: No previous payment history
- `multiple_recent_failures`: Recent failed payments
- `high_velocity`: Too many transactions in short time
- `suspicious_ip_activity`: Unusual IP activity
- `bot_user_agent`: Automated/bot traffic detected
- `unusual_hour`: Transaction at unusual time

### 3. Change Subscription Plan

Updates user's subscription to a different plan with proration.

**Endpoint:** `POST /change-subscription-plan`

**Request Body:**
```json
{
  "userId": "string",
  "newPlanId": "string",
  "billingPeriod": "monthly" | "yearly",
  "prorationBehavior": "create_prorations" | "none"
}
```

**Response:**
```json
{
  "success": "boolean",
  "subscription": {
    "id": "string",
    "status": "string",
    "current_period_end": "timestamp",
    "plan_name": "string"
  }
}
```

### 4. Preview Plan Change

Calculates proration and shows preview of plan change.

**Endpoint:** `POST /preview-plan-change`

**Request Body:**
```json
{
  "userId": "string",
  "newPlanId": "string",
  "billingPeriod": "monthly" | "yearly"
}
```

**Response:**
```json
{
  "current_plan": {
    "id": "string",
    "name": "string",
    "price": "number",
    "features": ["string"],
    "max_identifications": "number"
  },
  "new_plan": {
    "id": "string",
    "name": "string",
    "price": "number",
    "features": ["string"],
    "max_identifications": "number"
  },
  "billing_period": "string",
  "proration_amount": "number",
  "price_difference": "number",
  "effective_date": "timestamp",
  "next_billing_date": "timestamp",
  "feature_comparison": {
    "current_features": ["string"],
    "new_features": ["string"],
    "added_features": ["string"],
    "removed_features": ["string"]
  },
  "usage_limit_change": {
    "current_limit": "number",
    "new_limit": "number",
    "is_upgrade": "boolean"
  },
  "is_upgrade": "boolean",
  "is_downgrade": "boolean",
  "immediate_charge": "boolean",
  "immediate_credit": "boolean"
}
```

### 5. Cancel Subscription

Cancels user's subscription with options for immediate or end-of-period cancellation.

**Endpoint:** `POST /cancel-subscription`

**Request Body:**
```json
{
  "subscriptionId": "string",
  "cancelAtPeriodEnd": "boolean",
  "cancellationReason": "string" // optional
}
```

**Response:**
```json
{
  "success": "boolean",
  "message": "string"
}
```

### 6. Reactivate Subscription

Reactivates a cancelled subscription before the period ends.

**Endpoint:** `POST /reactivate-subscription`

**Request Body:**
```json
{
  "userId": "string"
}
```

**Response:**
```json
{
  "success": "boolean",
  "message": "string",
  "subscription": {
    "id": "string",
    "status": "string",
    "cancel_at_period_end": "boolean",
    "current_period_end": "timestamp",
    "plan_name": "string",
    "days_remaining": "number"
  }
}
```

### 7. Create Customer Portal Session

Creates a Stripe customer portal session for subscription management.

**Endpoint:** `POST /create-customer-portal-session`

**Request Body:**
```json
{
  "userId": "string"
}
```

**Response:**
```json
{
  "url": "https://billing.stripe.com/session_id"
}
```

## Database Functions

### Analytics Functions

#### get_revenue_metrics(period_type)
Returns revenue metrics for specified period.

**Parameters:**
- `period_type`: 'day' | 'week' | 'month' | 'year'

**Returns:**
```json
{
  "mrr": "number",
  "arr": "number",
  "totalRevenue": "number",
  "revenueGrowth": "number",
  "period": "string"
}
```

#### get_subscription_metrics(period_type)
Returns subscription analytics.

**Returns:**
```json
{
  "totalSubscriptions": "number",
  "activeSubscriptions": "number",
  "trialSubscriptions": "number",
  "canceledSubscriptions": "number",
  "churnRate": "number",
  "conversionRate": "number",
  "averageRevenuePerUser": "number"
}
```

#### get_user_usage_stats(user_uuid)
Returns usage statistics for a specific user.

**Returns:**
```json
[
  {
    "feature_type": "string",
    "current_usage": "number",
    "usage_limit": "number",
    "usage_percentage": "number",
    "period_start": "timestamp",
    "period_end": "timestamp"
  }
]
```

### Utility Functions

#### check_usage_limit(user_uuid, feature_name)
Checks if user can use a specific feature.

**Returns:** `boolean`

#### track_feature_usage(user_uuid, feature_name, usage_amount)
Records feature usage for a user.

**Returns:** `boolean`

## Error Handling

All endpoints return consistent error responses:

```json
{
  "error": "string",
  "code": "string", // optional
  "details": "object" // optional
}
```

**Common Error Codes:**
- `UNAUTHORIZED`: Invalid or missing authentication
- `RATE_LIMITED`: Too many requests
- `PAYMENT_BLOCKED`: Payment blocked by fraud detection
- `SUBSCRIPTION_NOT_FOUND`: Subscription doesn't exist
- `PLAN_NOT_FOUND`: Subscription plan doesn't exist
- `VALIDATION_ERROR`: Invalid request parameters

## Security Considerations

### Fraud Detection
- Device fingerprinting
- IP-based risk assessment
- Velocity checking
- Geographic risk analysis
- Payment method validation
- User behavior analysis

### Rate Limiting
- 10 checkout sessions per hour per user
- 50 checkout sessions per day per user
- IP-based rate limiting for additional protection

### Data Protection
- All payment data encrypted in transit and at rest
- PCI DSS compliance through Stripe
- Row Level Security (RLS) on all database tables
- JWT token validation on all endpoints

## Webhooks

The system handles the following Stripe webhooks:

- `checkout.session.completed`
- `customer.subscription.created`
- `customer.subscription.updated`
- `customer.subscription.deleted`
- `invoice.payment_succeeded`
- `invoice.payment_failed`
- `payment_intent.succeeded`
- `payment_intent.payment_failed`
- `radar.early_fraud_warning.created`

## Testing

Use Stripe's test mode for development:
- Test card numbers: `****************` (Visa), `****************` (declined)
- Test webhook events can be triggered via Stripe CLI
- All fraud detection features work in test mode

## Support

For technical support or questions about the payment API:
- Email: <EMAIL>
- Documentation: https://docs.bioscanplus.com
- Status page: https://status.bioscanplus.com
