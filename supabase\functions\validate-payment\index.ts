import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import Stripe from 'https://esm.sh/stripe@12.0.0?target=deno'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

const stripe = new Stripe(Deno.env.get('STRIPE_SECRET_KEY') || '', {
  apiVersion: '2023-10-16',
})

const supabaseClient = createClient(
  Deno.env.get('SUPABASE_URL') ?? '',
  Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
)

// Fraud detection thresholds
const FRAUD_THRESHOLDS = {
  HIGH_RISK: 70,
  MEDIUM_RISK: 40,
  LOW_RISK: 20
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const authHeader = req.headers.get('Authorization')!
    const token = authHeader.replace('Bearer ', '')

    const { data: { user }, error: authError } = await supabaseClient.auth.getUser(token)
    if (authError || !user) {
      throw new Error('Unauthorized')
    }

    const { paymentMethodId, amount, currency = 'USD' } = await req.json()
    const clientIP = req.headers.get('CF-Connecting-IP') || req.headers.get('X-Forwarded-For') || 'unknown'
    const userAgent = req.headers.get('User-Agent') || 'unknown'

    // Validate payment method
    const paymentMethod = await stripe.paymentMethods.retrieve(paymentMethodId)
    
    // Perform fraud detection analysis
    const riskAnalysis = await performRiskAnalysis({
      userId: user.id,
      paymentMethod,
      amount,
      currency,
      clientIP,
      userAgent
    })

    // Determine action based on risk score
    let action = 'allow'
    let requiresVerification = false

    if (riskAnalysis.riskScore >= FRAUD_THRESHOLDS.HIGH_RISK) {
      action = 'block'
    } else if (riskAnalysis.riskScore >= FRAUD_THRESHOLDS.MEDIUM_RISK) {
      action = 'review'
      requiresVerification = true
    }

    // Log fraud detection result
    await supabaseClient
      .from('fraud_detection_logs')
      .insert({
        user_id: user.id,
        ip_address: clientIP,
        user_agent: userAgent,
        risk_factors: riskAnalysis.riskFactors,
        risk_score: riskAnalysis.riskScore,
        action_taken: action,
        notes: `Payment validation for amount ${amount} ${currency}`
      })

    // If blocked, return error
    if (action === 'block') {
      return new Response(
        JSON.stringify({ 
          error: 'Payment blocked due to security concerns. Please contact support.',
          blocked: true 
        }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 403,
        }
      )
    }

    return new Response(
      JSON.stringify({
        valid: true,
        riskScore: riskAnalysis.riskScore,
        requiresVerification,
        action,
        riskFactors: riskAnalysis.riskFactors
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      }
    )

  } catch (error: any) {
    console.error('Error validating payment:', error)
    return new Response(
      JSON.stringify({ error: error.message }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400,
      }
    )
  }
})

async function performRiskAnalysis(params: {
  userId: string
  paymentMethod: any
  amount: number
  currency: string
  clientIP: string
  userAgent: string
}): Promise<{ riskScore: number; riskFactors: any }> {
  
  let riskScore = 0
  const riskFactors: any = {}

  try {
    // 1. Amount-based risk
    if (params.amount > 100) {
      riskScore += 15
      riskFactors.high_amount = true
    }
    if (params.amount > 500) {
      riskScore += 25
      riskFactors.very_high_amount = true
    }

    // 2. Geographic risk
    const cardCountry = params.paymentMethod.card?.country
    const suspiciousCountries = ['CN', 'RU', 'NG', 'PK', 'BD']
    if (cardCountry && suspiciousCountries.includes(cardCountry)) {
      riskScore += 30
      riskFactors.suspicious_country = cardCountry
    }

    // 3. Payment method risk
    if (params.paymentMethod.card?.funding === 'prepaid') {
      riskScore += 20
      riskFactors.prepaid_card = true
    }

    // 4. User history analysis
    const { data: userHistory } = await supabaseClient
      .from('payment_transactions')
      .select('*')
      .eq('user_id', params.userId)
      .order('created_at', { ascending: false })
      .limit(10)

    if (!userHistory || userHistory.length === 0) {
      riskScore += 25
      riskFactors.new_user = true
    } else {
      // Check for recent failed payments
      const recentFailed = userHistory.filter(t => 
        t.status === 'failed' && 
        new Date(t.created_at) > new Date(Date.now() - 24 * 60 * 60 * 1000)
      )
      
      if (recentFailed.length >= 3) {
        riskScore += 40
        riskFactors.multiple_recent_failures = recentFailed.length
      }
    }

    // 5. Velocity checks
    const { data: recentTransactions } = await supabaseClient
      .from('payment_transactions')
      .select('*')
      .eq('user_id', params.userId)
      .gte('created_at', new Date(Date.now() - 60 * 60 * 1000).toISOString()) // Last hour

    if (recentTransactions && recentTransactions.length > 5) {
      riskScore += 35
      riskFactors.high_velocity = recentTransactions.length
    }

    // 6. IP-based risk
    const { data: ipHistory } = await supabaseClient
      .from('fraud_detection_logs')
      .select('*')
      .eq('ip_address', params.clientIP)
      .gte('created_at', new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString())

    if (ipHistory && ipHistory.length > 20) {
      riskScore += 30
      riskFactors.suspicious_ip_activity = ipHistory.length
    }

    // 7. Device fingerprinting (basic user agent analysis)
    if (params.userAgent.includes('bot') || params.userAgent.includes('crawler')) {
      riskScore += 50
      riskFactors.bot_user_agent = true
    }

    // 8. Time-based risk (unusual hours)
    const hour = new Date().getHours()
    if (hour < 6 || hour > 23) {
      riskScore += 10
      riskFactors.unusual_hour = hour
    }

  } catch (error) {
    console.error('Error in risk analysis:', error)
    riskScore += 20 // Add penalty for analysis failure
    riskFactors.analysis_error = true
  }

  return {
    riskScore: Math.min(riskScore, 100), // Cap at 100
    riskFactors
  }
}
