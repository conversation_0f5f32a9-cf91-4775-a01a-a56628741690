import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Switch,
  Alert,
  RefreshControl,
  Modal,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import {
  Shield,
  Eye,
  EyeOff,
  Lock,
  Unlock,
  MapPin,
  Users,
  BarChart3,
  FileText,
  Settings,
  AlertTriangle,
  CheckCircle,
  Clock,
  Database,
  Fingerprint,
  Brain,
  Share2,
  Download,
  Trash2,
  Activity,
  Info,
} from 'lucide-react-native';
import {
  AdvancedPrivacyService,
  AdvancedPrivacyPreferences,
  PrivacyConsent,
} from '../services/AdvancedPrivacyService';

interface PrivacyControlCenterProps {
  onPreferencesChange?: (preferences: AdvancedPrivacyPreferences) => void;
}

export const PrivacyControlCenter: React.FC<PrivacyControlCenterProps> = ({
  onPreferencesChange,
}) => {
  const [preferences, setPreferences] = useState<AdvancedPrivacyPreferences | null>(null);
  const [consents, setConsents] = useState<PrivacyConsent[]>([]);
  const [accessHistory, setAccessHistory] = useState<any[]>([]);
  const [processingActivities, setProcessingActivities] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [activeTab, setActiveTab] = useState<'controls' | 'consents' | 'activity' | 'analytics'>('controls');
  const [showRiskAssessment, setShowRiskAssessment] = useState(false);

  const privacyService = AdvancedPrivacyService.getInstance();

  useEffect(() => {
    loadPrivacyData();
  }, []);

  const loadPrivacyData = async () => {
    try {
      setLoading(true);
      
      const [prefs, consentData, history, activities] = await Promise.all([
        privacyService.getAdvancedPrivacyPreferences(),
        privacyService.getPrivacyConsents(),
        privacyService.getDataAccessHistory(50),
        privacyService.getDataProcessingActivities(),
      ]);

      setPreferences(prefs);
      setConsents(consentData);
      setAccessHistory(history);
      setProcessingActivities(activities);
    } catch (error) {
      console.error('Error loading privacy data:', error);
      Alert.alert('Error', 'Failed to load privacy settings');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const onRefresh = () => {
    setRefreshing(true);
    loadPrivacyData();
  };

  const updatePreference = async (key: keyof AdvancedPrivacyPreferences, value: any) => {
    if (!preferences) return;

    const updatedPreferences = { ...preferences, [key]: value };
    setPreferences(updatedPreferences);

    const success = await privacyService.updateAdvancedPrivacyPreferences({ [key]: value });
    
    if (success) {
      if (onPreferencesChange) {
        onPreferencesChange(updatedPreferences);
      }
    } else {
      // Revert on failure
      setPreferences(preferences);
      Alert.alert('Error', 'Failed to update privacy setting');
    }
  };

  const updateConsent = async (consentType: string, status: boolean) => {
    const success = await privacyService.updatePrivacyConsent(consentType, status);
    
    if (success) {
      await loadPrivacyData(); // Reload to get updated consents
    } else {
      Alert.alert('Error', 'Failed to update consent');
    }
  };

  const performRiskAssessment = async () => {
    try {
      const assessment = await privacyService.performRiskAssessment(
        'data_collection',
        ['identification_data', 'location_data', 'behavioral_data'],
        'Species identification and user analytics'
      );

      Alert.alert(
        'Privacy Risk Assessment',
        `Risk Level: ${assessment.risk_level.toUpperCase()}\n` +
        `Risk Score: ${(assessment.risk_score * 100).toFixed(1)}%\n\n` +
        `Mitigation Measures:\n${assessment.mitigation_measures.join('\n• ')}`
      );
    } catch (error) {
      Alert.alert('Error', 'Failed to perform risk assessment');
    }
  };

  const renderPrivacyControls = () => (
    <ScrollView style={styles.tabContent}>
      {/* Data Minimization */}
      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <Database size={20} color="#3B82F6" />
          <Text style={styles.sectionTitle}>Data Minimization</Text>
        </View>

        <SettingRow
          icon={Shield}
          title="Data Minimization"
          description="Automatically reduce data collection to minimum necessary"
          value={preferences?.data_minimization_enabled ?? true}
          onValueChange={(value) => updatePreference('data_minimization_enabled', value)}
        />

        <SettingRow
          icon={Trash2}
          title="Auto-Delete Old Data"
          description="Automatically delete data after retention period"
          value={preferences?.auto_delete_enabled ?? false}
          onValueChange={(value) => updatePreference('auto_delete_enabled', value)}
        />
      </View>

      {/* Behavioral Analytics */}
      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <BarChart3 size={20} color="#3B82F6" />
          <Text style={styles.sectionTitle}>Behavioral Analytics</Text>
        </View>

        <SettingRow
          icon={Activity}
          title="Behavioral Analytics"
          description="Allow analysis of app usage patterns"
          value={preferences?.allow_behavioral_analytics ?? false}
          onValueChange={(value) => updatePreference('allow_behavioral_analytics', value)}
        />

        <SettingRow
          icon={Fingerprint}
          title="Device Fingerprinting"
          description="Allow device identification for security"
          value={preferences?.allow_fingerprinting ?? false}
          onValueChange={(value) => updatePreference('allow_fingerprinting', value)}
        />

        <SettingRow
          icon={Brain}
          title="User Profiling"
          description="Create personalized user profiles"
          value={preferences?.allow_profiling ?? false}
          onValueChange={(value) => updatePreference('allow_profiling', value)}
        />

        <SettingRow
          icon={Users}
          title="Cross-Device Tracking"
          description="Link activity across your devices"
          value={preferences?.allow_cross_device_tracking ?? false}
          onValueChange={(value) => updatePreference('allow_cross_device_tracking', value)}
        />
      </View>

      {/* Location Privacy */}
      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <MapPin size={20} color="#3B82F6" />
          <Text style={styles.sectionTitle}>Location Privacy</Text>
        </View>

        <View style={styles.settingRow}>
          <View style={styles.settingContent}>
            <Text style={styles.settingTitle}>Location Precision</Text>
            <Text style={styles.settingDescription}>
              Control how precise your location data is
            </Text>
          </View>
          <Text style={styles.settingValue}>
            {preferences?.location_precision_level || 'City'}
          </Text>
        </View>

        <SettingRow
          icon={MapPin}
          title="Location Inference"
          description="Allow inferring locations from other data"
          value={preferences?.allow_location_inference ?? false}
          onValueChange={(value) => updatePreference('allow_location_inference', value)}
        />
      </View>

      {/* Data Sharing */}
      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <Share2 size={20} color="#3B82F6" />
          <Text style={styles.sectionTitle}>Data Sharing</Text>
        </View>

        <SettingRow
          icon={Users}
          title="Third-Party Sharing"
          description="Allow sharing data with trusted partners"
          value={preferences?.allow_third_party_sharing ?? false}
          onValueChange={(value) => updatePreference('allow_third_party_sharing', value)}
        />
      </View>

      {/* Biometric Data */}
      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <Fingerprint size={20} color="#3B82F6" />
          <Text style={styles.sectionTitle}>Biometric Data</Text>
        </View>

        <SettingRow
          icon={Fingerprint}
          title="Biometric Processing"
          description="Allow processing of biometric identifiers"
          value={preferences?.allow_biometric_processing ?? false}
          onValueChange={(value) => updatePreference('allow_biometric_processing', value)}
        />

        <SettingRow
          icon={Activity}
          title="Health Data Inference"
          description="Allow inferring health information"
          value={preferences?.allow_health_data_inference ?? false}
          onValueChange={(value) => updatePreference('allow_health_data_inference', value)}
        />
      </View>

      {/* Security Settings */}
      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <Lock size={20} color="#3B82F6" />
          <Text style={styles.sectionTitle}>Security Settings</Text>
        </View>

        <SettingRow
          icon={Lock}
          title="2FA for Sensitive Actions"
          description="Require two-factor authentication for sensitive operations"
          value={preferences?.require_2fa_for_sensitive ?? true}
          onValueChange={(value) => updatePreference('require_2fa_for_sensitive', value)}
        />

        <SettingRow
          icon={Shield}
          title="Re-auth for Deletion"
          description="Require re-authentication before data deletion"
          value={preferences?.require_reauth_for_deletion ?? true}
          onValueChange={(value) => updatePreference('require_reauth_for_deletion', value)}
        />
      </View>

      {/* Auto Export */}
      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <Download size={20} color="#3B82F6" />
          <Text style={styles.sectionTitle}>Data Portability</Text>
        </View>

        <SettingRow
          icon={Download}
          title="Auto Export"
          description="Automatically export your data periodically"
          value={preferences?.auto_export_enabled ?? false}
          onValueChange={(value) => updatePreference('auto_export_enabled', value)}
        />
      </View>
    </ScrollView>
  );

  const renderConsents = () => (
    <ScrollView style={styles.tabContent}>
      <Text style={styles.sectionDescription}>
        Manage your consent for different types of data processing
      </Text>

      {consents.map((consent) => (
        <View key={consent.id} style={styles.consentCard}>
          <View style={styles.consentHeader}>
            <Text style={styles.consentType}>
              {consent.consent_type.replace('_', ' ').toUpperCase()}
            </Text>
            <Switch
              value={consent.consent_status}
              onValueChange={(value) => updateConsent(consent.consent_type, value)}
              trackColor={{ false: '#E5E7EB', true: '#3B82F6' }}
              thumbColor={consent.consent_status ? '#FFFFFF' : '#F3F4F6'}
            />
          </View>
          
          <Text style={styles.consentDetails}>
            Legal Basis: {consent.legal_basis}
          </Text>
          <Text style={styles.consentDetails}>
            Source: {consent.consent_source}
          </Text>
          <Text style={styles.consentDetails}>
            Date: {new Date(consent.created_at).toLocaleDateString()}
          </Text>
          
          {consent.expires_at && (
            <Text style={styles.consentExpiry}>
              Expires: {new Date(consent.expires_at).toLocaleDateString()}
            </Text>
          )}
        </View>
      ))}
    </ScrollView>
  );

  const renderActivity = () => (
    <ScrollView style={styles.tabContent}>
      <Text style={styles.sectionDescription}>
        View your data access history and processing activities
      </Text>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Recent Data Access</Text>
        
        {accessHistory.slice(0, 10).map((access, index) => (
          <View key={index} style={styles.activityCard}>
            <View style={styles.activityHeader}>
              <Text style={styles.activityType}>
                {access.access_type.toUpperCase()}
              </Text>
              <Text style={styles.activityDate}>
                {new Date(access.created_at).toLocaleDateString()}
              </Text>
            </View>
            
            <Text style={styles.activityCategory}>
              Category: {access.data_category}
            </Text>
            
            {access.access_reason && (
              <Text style={styles.activityReason}>
                Reason: {access.access_reason}
              </Text>
            )}
          </View>
        ))}
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Processing Activities</Text>
        
        {processingActivities.slice(0, 5).map((activity, index) => (
          <View key={index} style={styles.activityCard}>
            <View style={styles.activityHeader}>
              <Text style={styles.activityType}>
                {activity.activity_type.replace('_', ' ').toUpperCase()}
              </Text>
              <Text style={styles.activityStatus}>
                {activity.status.toUpperCase()}
              </Text>
            </View>
            
            <Text style={styles.activityCategory}>
              Purpose: {activity.processing_purpose}
            </Text>
            <Text style={styles.activityCategory}>
              Legal Basis: {activity.legal_basis}
            </Text>
          </View>
        ))}
      </View>
    </ScrollView>
  );

  const renderAnalytics = () => (
    <ScrollView style={styles.tabContent}>
      <Text style={styles.sectionDescription}>
        Privacy analytics and risk assessment
      </Text>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Privacy Metrics</Text>
        
        <View style={styles.metricsGrid}>
          <View style={styles.metricCard}>
            <CheckCircle size={24} color="#10B981" />
            <Text style={styles.metricValue}>{consents.filter(c => c.consent_status).length}</Text>
            <Text style={styles.metricLabel}>Active Consents</Text>
          </View>
          
          <View style={styles.metricCard}>
            <Activity size={24} color="#3B82F6" />
            <Text style={styles.metricValue}>{accessHistory.length}</Text>
            <Text style={styles.metricLabel}>Data Accesses</Text>
          </View>
          
          <View style={styles.metricCard}>
            <Database size={24} color="#F59E0B" />
            <Text style={styles.metricValue}>{processingActivities.length}</Text>
            <Text style={styles.metricLabel}>Processing Activities</Text>
          </View>
        </View>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Risk Assessment</Text>
        
        <TouchableOpacity
          style={styles.actionButton}
          onPress={performRiskAssessment}
        >
          <AlertTriangle size={20} color="#F59E0B" />
          <Text style={styles.actionButtonText}>Perform Risk Assessment</Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <Shield size={32} color="#3B82F6" />
        <Text style={styles.loadingText}>Loading privacy controls...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Header */}
      <LinearGradient
        colors={['#3B82F6', '#1D4ED8']}
        style={styles.header}
      >
        <Shield size={32} color="white" />
        <Text style={styles.headerTitle}>Privacy Control Center</Text>
        <Text style={styles.headerSubtitle}>
          Advanced privacy controls and data management
        </Text>
      </LinearGradient>

      {/* Tab Navigation */}
      <View style={styles.tabContainer}>
        {[
          { key: 'controls', label: 'Controls', icon: Settings },
          { key: 'consents', label: 'Consents', icon: FileText },
          { key: 'activity', label: 'Activity', icon: Activity },
          { key: 'analytics', label: 'Analytics', icon: BarChart3 },
        ].map(({ key, label, icon: Icon }) => (
          <TouchableOpacity
            key={key}
            style={[styles.tab, activeTab === key && styles.activeTab]}
            onPress={() => setActiveTab(key as any)}
          >
            <Icon size={18} color={activeTab === key ? '#3B82F6' : '#6B7280'} />
            <Text style={[styles.tabText, activeTab === key && styles.activeTabText]}>
              {label}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      {/* Content */}
      <View style={styles.content}>
        {activeTab === 'controls' && renderPrivacyControls()}
        {activeTab === 'consents' && renderConsents()}
        {activeTab === 'activity' && renderActivity()}
        {activeTab === 'analytics' && renderAnalytics()}
      </View>
    </View>
  );
};

interface SettingRowProps {
  icon: any;
  title: string;
  description: string;
  value: boolean;
  onValueChange: (value: boolean) => void;
}

const SettingRow: React.FC<SettingRowProps> = ({
  icon: Icon,
  title,
  description,
  value,
  onValueChange,
}) => (
  <View style={styles.settingRow}>
    <Icon size={20} color="#3B82F6" />
    <View style={styles.settingContent}>
      <Text style={styles.settingTitle}>{title}</Text>
      <Text style={styles.settingDescription}>{description}</Text>
    </View>
    <Switch
      value={value}
      onValueChange={onValueChange}
      trackColor={{ false: '#E5E7EB', true: '#3B82F6' }}
      thumbColor={value ? '#FFFFFF' : '#F3F4F6'}
    />
  </View>
);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#F9FAFB',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#6B7280',
  },
  header: {
    padding: 24,
    paddingTop: 60,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: 'white',
    marginTop: 8,
  },
  headerSubtitle: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.8)',
    marginTop: 4,
    textAlign: 'center',
  },
  tabContainer: {
    flexDirection: 'row',
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  tab: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    gap: 6,
  },
  activeTab: {
    borderBottomWidth: 2,
    borderBottomColor: '#3B82F6',
  },
  tabText: {
    fontSize: 12,
    fontWeight: '500',
    color: '#6B7280',
  },
  activeTabText: {
    color: '#3B82F6',
  },
  content: {
    flex: 1,
  },
  tabContent: {
    flex: 1,
    padding: 20,
  },
  sectionDescription: {
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 20,
    lineHeight: 20,
  },
  section: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
    gap: 8,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827',
  },
  settingRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
    gap: 12,
  },
  settingContent: {
    flex: 1,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#111827',
    marginBottom: 2,
  },
  settingDescription: {
    fontSize: 14,
    color: '#6B7280',
    lineHeight: 18,
  },
  settingValue: {
    fontSize: 14,
    color: '#3B82F6',
    fontWeight: '500',
  },
  consentCard: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  consentHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  consentType: {
    fontSize: 16,
    fontWeight: '600',
    color: '#111827',
  },
  consentDetails: {
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 4,
  },
  consentExpiry: {
    fontSize: 14,
    color: '#F59E0B',
    fontWeight: '500',
  },
  activityCard: {
    backgroundColor: '#F9FAFB',
    borderRadius: 8,
    padding: 12,
    marginBottom: 8,
  },
  activityHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  activityType: {
    fontSize: 14,
    fontWeight: '600',
    color: '#111827',
  },
  activityDate: {
    fontSize: 12,
    color: '#6B7280',
  },
  activityStatus: {
    fontSize: 12,
    color: '#10B981',
    fontWeight: '500',
  },
  activityCategory: {
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 2,
  },
  activityReason: {
    fontSize: 14,
    color: '#6B7280',
    fontStyle: 'italic',
  },
  metricsGrid: {
    flexDirection: 'row',
    gap: 12,
  },
  metricCard: {
    flex: 1,
    backgroundColor: '#F9FAFB',
    borderRadius: 8,
    padding: 16,
    alignItems: 'center',
  },
  metricValue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#111827',
    marginTop: 8,
  },
  metricLabel: {
    fontSize: 12,
    color: '#6B7280',
    marginTop: 4,
    textAlign: 'center',
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FEF3C7',
    padding: 16,
    borderRadius: 8,
    gap: 12,
  },
  actionButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#92400E',
  },
});

export default PrivacyControlCenter;
