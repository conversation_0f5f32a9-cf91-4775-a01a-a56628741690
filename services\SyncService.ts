import { tursoService } from '../lib/turso';
import { supabase } from '../lib/supabase';
import { firebaseAuthService } from '../lib/firebase';
import NetInfo from '@react-native-community/netinfo';

export interface SyncOperation {
  id: string;
  type: 'create' | 'update' | 'delete';
  table: string;
  data: any;
  timestamp: string;
  userId: string;
  status: 'pending' | 'syncing' | 'completed' | 'failed';
  retryCount: number;
  lastError?: string;
}

export interface SyncResult {
  success: boolean;
  syncedOperations: number;
  failedOperations: number;
  errors: Array<{ operation: SyncOperation; error: string }>;
}

export interface ConflictResolution {
  strategy: 'local_wins' | 'remote_wins' | 'merge' | 'user_choice';
  resolvedData?: any;
}

export class SyncService {
  private static instance: SyncService;
  private isOnline = true;
  private isSyncing = false;
  private syncInterval: NodeJS.Timeout | null = null;
  private readonly SYNC_INTERVAL_MS = 30000; // 30 seconds
  private readonly MAX_RETRY_COUNT = 3;

  private constructor() {
    this.setupNetworkListener();
    this.startPeriodicSync();
  }

  public static getInstance(): SyncService {
    if (!SyncService.instance) {
      SyncService.instance = new SyncService();
    }
    return SyncService.instance;
  }

  private setupNetworkListener(): void {
    NetInfo.addEventListener(state => {
      const wasOnline = this.isOnline;
      this.isOnline = state.isConnected ?? false;
      
      // Trigger sync when coming back online
      if (!wasOnline && this.isOnline) {
        console.log('Network restored, triggering sync...');
        this.syncPendingOperations();
      }
      
      // Update Turso service online status
      tursoService.setOnlineStatus(this.isOnline);
    });
  }

  private startPeriodicSync(): void {
    this.syncInterval = setInterval(() => {
      if (this.isOnline && !this.isSyncing) {
        this.syncPendingOperations();
      }
    }, this.SYNC_INTERVAL_MS);
  }

  public stopPeriodicSync(): void {
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
      this.syncInterval = null;
    }
  }

  /**
   * Queue an operation for synchronization
   */
  public async queueOperation(
    type: SyncOperation['type'],
    table: string,
    data: any,
    userId?: string
  ): Promise<void> {
    const currentUser = firebaseAuthService.getCurrentUser();
    const operationUserId = userId || currentUser?.uid;
    
    if (!operationUserId) {
      throw new Error('User ID required for sync operation');
    }

    const operation: SyncOperation = {
      id: `sync_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type,
      table,
      data,
      timestamp: new Date().toISOString(),
      userId: operationUserId,
      status: 'pending',
      retryCount: 0
    };

    // Store in local sync queue
    await this.storeSyncOperation(operation);

    // Try immediate sync if online
    if (this.isOnline && !this.isSyncing) {
      this.syncPendingOperations();
    }
  }

  /**
   * Sync all pending operations
   */
  public async syncPendingOperations(): Promise<SyncResult> {
    if (this.isSyncing) {
      console.log('Sync already in progress, skipping...');
      return { success: true, syncedOperations: 0, failedOperations: 0, errors: [] };
    }

    if (!this.isOnline) {
      console.log('Offline, skipping sync...');
      return { success: false, syncedOperations: 0, failedOperations: 0, errors: [] };
    }

    this.isSyncing = true;
    const result: SyncResult = {
      success: true,
      syncedOperations: 0,
      failedOperations: 0,
      errors: []
    };

    try {
      // Get pending operations from local storage
      const pendingOperations = await this.getPendingOperations();
      console.log(`Syncing ${pendingOperations.length} pending operations...`);

      for (const operation of pendingOperations) {
        try {
          await this.syncOperation(operation);
          await this.markOperationCompleted(operation.id);
          result.syncedOperations++;
        } catch (error) {
          console.error(`Failed to sync operation ${operation.id}:`, error);
          
          const errorMessage = error instanceof Error ? error.message : 'Unknown error';
          await this.handleSyncError(operation, errorMessage);
          
          result.failedOperations++;
          result.errors.push({ operation, error: errorMessage });
          result.success = false;
        }
      }

      // Sync data from remote to local
      await this.syncFromRemote();

    } catch (error) {
      console.error('Sync process failed:', error);
      result.success = false;
    } finally {
      this.isSyncing = false;
    }

    return result;
  }

  /**
   * Sync a single operation to remote
   */
  private async syncOperation(operation: SyncOperation): Promise<void> {
    const { type, table, data, userId } = operation;

    // Update operation status
    await this.updateOperationStatus(operation.id, 'syncing');

    switch (table) {
      case 'user_preferences':
        await this.syncUserPreferences(type, data, userId);
        break;
      case 'species_identifications':
        await this.syncIdentifications(type, data, userId);
        break;
      case 'user_discoveries':
        await this.syncDiscoveries(type, data, userId);
        break;
      case 'media_uploads':
        await this.syncMediaUploads(type, data, userId);
        break;
      default:
        throw new Error(`Unsupported sync table: ${table}`);
    }
  }

  /**
   * Sync user preferences to Supabase
   */
  private async syncUserPreferences(
    type: SyncOperation['type'],
    data: any,
    userId: string
  ): Promise<void> {
    // For user preferences, we'll store them in the user_profiles table
    const { error } = await supabase
      .from('user_profiles')
      .upsert({
        id: userId,
        preferences: data,
        updated_at: new Date().toISOString()
      });

    if (error) {
      throw new Error(`Failed to sync user preferences: ${error.message}`);
    }
  }

  /**
   * Sync identifications to Supabase
   */
  private async syncIdentifications(
    type: SyncOperation['type'],
    data: any,
    userId: string
  ): Promise<void> {
    switch (type) {
      case 'create':
        const { error: createError } = await supabase
          .from('species_identifications')
          .insert({
            ...data,
            user_id: userId,
            created_at: new Date().toISOString()
          });
        
        if (createError) {
          throw new Error(`Failed to create identification: ${createError.message}`);
        }
        break;

      case 'update':
        const { error: updateError } = await supabase
          .from('species_identifications')
          .update({
            ...data,
            updated_at: new Date().toISOString()
          })
          .eq('id', data.id)
          .eq('user_id', userId);
        
        if (updateError) {
          throw new Error(`Failed to update identification: ${updateError.message}`);
        }
        break;

      case 'delete':
        const { error: deleteError } = await supabase
          .from('species_identifications')
          .delete()
          .eq('id', data.id)
          .eq('user_id', userId);
        
        if (deleteError) {
          throw new Error(`Failed to delete identification: ${deleteError.message}`);
        }
        break;
    }
  }

  /**
   * Sync discoveries to Supabase
   */
  private async syncDiscoveries(
    type: SyncOperation['type'],
    data: any,
    userId: string
  ): Promise<void> {
    switch (type) {
      case 'create':
        const { error: createError } = await supabase
          .from('user_discoveries')
          .insert({
            ...data,
            user_id: userId,
            created_at: new Date().toISOString()
          });
        
        if (createError) {
          throw new Error(`Failed to create discovery: ${createError.message}`);
        }
        break;

      case 'update':
        const { error: updateError } = await supabase
          .from('user_discoveries')
          .update(data)
          .eq('id', data.id)
          .eq('user_id', userId);
        
        if (updateError) {
          throw new Error(`Failed to update discovery: ${updateError.message}`);
        }
        break;

      case 'delete':
        const { error: deleteError } = await supabase
          .from('user_discoveries')
          .delete()
          .eq('id', data.id)
          .eq('user_id', userId);
        
        if (deleteError) {
          throw new Error(`Failed to delete discovery: ${deleteError.message}`);
        }
        break;
    }
  }

  /**
   * Sync media uploads to Supabase
   */
  private async syncMediaUploads(
    type: SyncOperation['type'],
    data: any,
    userId: string
  ): Promise<void> {
    // Media uploads require special handling for file uploads
    // This is a simplified version - full implementation would handle file uploads
    switch (type) {
      case 'create':
        const { error } = await supabase
          .from('media_uploads')
          .insert({
            ...data,
            user_id: userId,
            created_at: new Date().toISOString()
          });
        
        if (error) {
          throw new Error(`Failed to create media upload: ${error.message}`);
        }
        break;
    }
  }

  /**
   * Sync data from remote to local
   */
  private async syncFromRemote(): Promise<void> {
    const currentUser = firebaseAuthService.getCurrentUser();
    if (!currentUser) return;

    try {
      // Get last sync timestamp
      const lastSync = await tursoService.getAppSetting('last_remote_sync') || '1970-01-01T00:00:00Z';

      // Fetch updated data from Supabase
      const { data: identifications, error: idError } = await supabase
        .from('species_identifications')
        .select('*')
        .eq('user_id', currentUser.uid)
        .gt('updated_at', lastSync);

      if (idError) {
        throw new Error(`Failed to fetch identifications: ${idError.message}`);
      }

      // Update local cache with remote data
      if (identifications && identifications.length > 0) {
        for (const identification of identifications) {
          await tursoService.cacheIdentification(
            currentUser.uid,
            identification.media_id,
            identification
          );
        }
      }

      // Update last sync timestamp
      await tursoService.setAppSetting('last_remote_sync', new Date().toISOString());

    } catch (error) {
      console.error('Failed to sync from remote:', error);
    }
  }

  /**
   * Handle sync conflicts
   */
  private async resolveConflict(
    localData: any,
    remoteData: any,
    strategy: ConflictResolution['strategy'] = 'remote_wins'
  ): Promise<any> {
    switch (strategy) {
      case 'local_wins':
        return localData;
      case 'remote_wins':
        return remoteData;
      case 'merge':
        // Simple merge strategy - can be enhanced based on data structure
        return { ...remoteData, ...localData };
      default:
        return remoteData;
    }
  }

  // Helper methods for managing sync operations in local storage
  private async storeSyncOperation(operation: SyncOperation): Promise<void> {
    // Implementation would store in Turso sync_queue table
    // This is handled by the tursoService.addToSyncQueue method
  }

  private async getPendingOperations(): Promise<SyncOperation[]> {
    // Implementation would fetch from Turso sync_queue table
    // Return mock data for now
    return [];
  }

  private async updateOperationStatus(id: string, status: SyncOperation['status']): Promise<void> {
    // Implementation would update status in Turso sync_queue table
  }

  private async markOperationCompleted(id: string): Promise<void> {
    await this.updateOperationStatus(id, 'completed');
  }

  private async handleSyncError(operation: SyncOperation, error: string): Promise<void> {
    const newRetryCount = operation.retryCount + 1;
    
    if (newRetryCount >= this.MAX_RETRY_COUNT) {
      await this.updateOperationStatus(operation.id, 'failed');
    } else {
      // Update retry count and keep as pending
      // Implementation would update in Turso sync_queue table
    }
  }

  /**
   * Force sync all data
   */
  public async forceSyncAll(): Promise<SyncResult> {
    console.log('Starting force sync...');
    return await this.syncPendingOperations();
  }

  /**
   * Get sync status
   */
  public getSyncStatus(): {
    isOnline: boolean;
    isSyncing: boolean;
    lastSync?: string;
  } {
    return {
      isOnline: this.isOnline,
      isSyncing: this.isSyncing,
      // lastSync would be retrieved from local storage
    };
  }
}

export const syncService = SyncService.getInstance();
