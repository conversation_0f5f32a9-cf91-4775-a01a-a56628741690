-- Privacy Management and Data Export Schema
-- This migration creates comprehensive privacy management, data export, and GDPR compliance features

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Data Export Requests Table
CREATE TABLE IF NOT EXISTS data_export_requests (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    request_type VARCHAR(50) NOT NULL CHECK (request_type IN ('full_export', 'partial_export', 'account_data', 'identifications', 'media', 'location_data')),
    status VARCHAR(50) NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed', 'expired')),
    export_format VARCHAR(20) NOT NULL DEFAULT 'json' CHECK (export_format IN ('json', 'csv', 'xml')),
    include_media BOOLEAN NOT NULL DEFAULT true,
    include_location BOOLEAN NOT NULL DEFAULT true,
    include_metadata BOOLEAN NOT NULL DEFAULT true,
    date_range_start TIMESTAMP WITH TIME ZONE,
    date_range_end TIMESTAMP WITH TIME ZONE,
    file_url VARCHAR(500),
    file_size_bytes BIGINT,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT (NOW() + INTERVAL '7 days'),
    completed_at TIMESTAMP WITH TIME ZONE,
    error_message TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Data Deletion Requests Table
CREATE TABLE IF NOT EXISTS data_deletion_requests (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    deletion_type VARCHAR(50) NOT NULL CHECK (deletion_type IN ('account_deletion', 'data_purge', 'selective_deletion')),
    status VARCHAR(50) NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'processing', 'completed', 'rejected', 'cancelled')),
    data_categories JSONB NOT NULL DEFAULT '[]', -- Array of data categories to delete
    retention_period_days INTEGER DEFAULT 30, -- Grace period before permanent deletion
    reason TEXT,
    admin_notes TEXT,
    approved_by UUID REFERENCES auth.users(id),
    approved_at TIMESTAMP WITH TIME ZONE,
    scheduled_deletion_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Privacy Settings Table (Enhanced)
CREATE TABLE IF NOT EXISTS user_privacy_settings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE UNIQUE,
    
    -- Data Collection Preferences
    allow_analytics BOOLEAN NOT NULL DEFAULT true,
    allow_performance_tracking BOOLEAN NOT NULL DEFAULT true,
    allow_crash_reporting BOOLEAN NOT NULL DEFAULT true,
    allow_usage_statistics BOOLEAN NOT NULL DEFAULT true,
    
    -- Location Privacy
    share_location BOOLEAN NOT NULL DEFAULT false,
    location_precision VARCHAR(20) NOT NULL DEFAULT 'city' CHECK (location_precision IN ('exact', 'approximate', 'city', 'region', 'country', 'none')),
    allow_location_history BOOLEAN NOT NULL DEFAULT false,
    
    -- Profile Privacy
    public_profile BOOLEAN NOT NULL DEFAULT false,
    show_real_name BOOLEAN NOT NULL DEFAULT false,
    show_email BOOLEAN NOT NULL DEFAULT false,
    show_statistics BOOLEAN NOT NULL DEFAULT true,
    show_achievements BOOLEAN NOT NULL DEFAULT true,
    show_collection BOOLEAN NOT NULL DEFAULT false,
    
    -- Communication Preferences
    allow_marketing_emails BOOLEAN NOT NULL DEFAULT false,
    allow_research_participation BOOLEAN NOT NULL DEFAULT false,
    allow_community_contact BOOLEAN NOT NULL DEFAULT true,
    
    -- Data Sharing
    allow_scientific_research BOOLEAN NOT NULL DEFAULT false,
    allow_conservation_efforts BOOLEAN NOT NULL DEFAULT false,
    allow_third_party_integrations BOOLEAN NOT NULL DEFAULT false,
    
    -- Media Privacy
    watermark_images BOOLEAN NOT NULL DEFAULT false,
    strip_metadata BOOLEAN NOT NULL DEFAULT true,
    allow_image_analysis BOOLEAN NOT NULL DEFAULT true,
    
    -- Advanced Privacy
    data_retention_period_months INTEGER DEFAULT 24, -- How long to keep user data
    require_explicit_consent BOOLEAN NOT NULL DEFAULT false,
    opt_out_of_ai_training BOOLEAN NOT NULL DEFAULT false,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Data Processing Consent Log
CREATE TABLE IF NOT EXISTS consent_log (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    consent_type VARCHAR(100) NOT NULL, -- e.g., 'data_collection', 'marketing', 'research'
    consent_given BOOLEAN NOT NULL,
    consent_version VARCHAR(20) NOT NULL, -- Version of privacy policy/terms
    ip_address INET,
    user_agent TEXT,
    consent_method VARCHAR(50) NOT NULL, -- 'explicit', 'implicit', 'updated'
    consent_context TEXT, -- Additional context about when/why consent was given
    expires_at TIMESTAMP WITH TIME ZONE, -- For time-limited consents
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Data Access Log (for GDPR compliance)
CREATE TABLE IF NOT EXISTS data_access_log (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    accessed_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    access_type VARCHAR(50) NOT NULL CHECK (access_type IN ('view', 'export', 'modify', 'delete', 'admin_access')),
    data_category VARCHAR(100) NOT NULL, -- e.g., 'profile', 'identifications', 'media'
    ip_address INET,
    user_agent TEXT,
    purpose TEXT, -- Why the data was accessed
    legal_basis VARCHAR(100), -- GDPR legal basis
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Location Data Table (Enhanced for privacy)
CREATE TABLE IF NOT EXISTS user_locations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    identification_id UUID REFERENCES species_identifications(id) ON DELETE CASCADE,
    
    -- Precise location (only stored if user consents)
    latitude DECIMAL(10, 8),
    longitude DECIMAL(11, 8),
    accuracy DECIMAL(8, 2),
    altitude DECIMAL(8, 2),
    
    -- Approximate location (for privacy)
    approximate_latitude DECIMAL(8, 4), -- Reduced precision
    approximate_longitude DECIMAL(9, 4), -- Reduced precision
    city VARCHAR(100),
    region VARCHAR(100),
    country VARCHAR(100),
    
    -- Metadata
    location_source VARCHAR(50) NOT NULL DEFAULT 'gps', -- 'gps', 'network', 'manual'
    is_approximate BOOLEAN NOT NULL DEFAULT false,
    privacy_level VARCHAR(20) NOT NULL DEFAULT 'exact' CHECK (privacy_level IN ('exact', 'approximate', 'city', 'region', 'country', 'hidden')),
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Data Categories for Deletion/Export
CREATE TABLE IF NOT EXISTS data_categories (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT NOT NULL,
    table_names TEXT[] NOT NULL, -- Database tables associated with this category
    is_essential BOOLEAN NOT NULL DEFAULT false, -- Cannot be deleted while account is active
    retention_period_days INTEGER, -- Default retention period
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Insert default data categories
INSERT INTO data_categories (name, description, table_names, is_essential, retention_period_days) VALUES
('profile_data', 'Basic profile information including name, email, and preferences', ARRAY['user_profiles', 'user_privacy_settings'], true, NULL),
('authentication_data', 'Login credentials and security information', ARRAY['auth.users'], true, NULL),
('identification_data', 'Species identification records and results', ARRAY['species_identifications'], false, 730),
('media_data', 'Uploaded photos and videos', ARRAY['media_uploads'], false, 730),
('location_data', 'GPS coordinates and location information', ARRAY['user_locations'], false, 365),
('usage_data', 'App usage statistics and analytics', ARRAY['usage_tracking'], false, 90),
('communication_data', 'Messages, notifications, and support tickets', ARRAY['notifications'], false, 365),
('payment_data', 'Subscription and payment information', ARRAY['user_subscriptions', 'payment_transactions'], false, 2555), -- 7 years for financial records
('social_data', 'Community interactions and shared content', ARRAY['user_collections'], false, 365)
ON CONFLICT (name) DO NOTHING;

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_data_export_requests_user_id ON data_export_requests(user_id);
CREATE INDEX IF NOT EXISTS idx_data_export_requests_status ON data_export_requests(status);
CREATE INDEX IF NOT EXISTS idx_data_export_requests_expires_at ON data_export_requests(expires_at);

CREATE INDEX IF NOT EXISTS idx_data_deletion_requests_user_id ON data_deletion_requests(user_id);
CREATE INDEX IF NOT EXISTS idx_data_deletion_requests_status ON data_deletion_requests(status);
CREATE INDEX IF NOT EXISTS idx_data_deletion_requests_scheduled ON data_deletion_requests(scheduled_deletion_at);

CREATE INDEX IF NOT EXISTS idx_consent_log_user_id ON consent_log(user_id);
CREATE INDEX IF NOT EXISTS idx_consent_log_type ON consent_log(consent_type);
CREATE INDEX IF NOT EXISTS idx_consent_log_created_at ON consent_log(created_at);

CREATE INDEX IF NOT EXISTS idx_data_access_log_user_id ON data_access_log(user_id);
CREATE INDEX IF NOT EXISTS idx_data_access_log_accessed_by ON data_access_log(accessed_by);
CREATE INDEX IF NOT EXISTS idx_data_access_log_created_at ON data_access_log(created_at);

CREATE INDEX IF NOT EXISTS idx_user_locations_user_id ON user_locations(user_id);
CREATE INDEX IF NOT EXISTS idx_user_locations_identification_id ON user_locations(identification_id);
CREATE INDEX IF NOT EXISTS idx_user_locations_privacy_level ON user_locations(privacy_level);

-- Create updated_at triggers
CREATE TRIGGER update_data_export_requests_updated_at BEFORE UPDATE ON data_export_requests FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_data_deletion_requests_updated_at BEFORE UPDATE ON data_deletion_requests FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_user_privacy_settings_updated_at BEFORE UPDATE ON user_privacy_settings FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_user_locations_updated_at BEFORE UPDATE ON user_locations FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
