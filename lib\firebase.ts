import { initializeApp, getApps, FirebaseApp } from 'firebase/app';
import { 
  getAuth, 
  Auth,
  signInWithEmailAndPassword,
  createUserWithEmailAndPassword,
  signOut,
  onAuthStateChanged,
  User,
  updateProfile,
  sendPasswordResetEmail,
  EmailAuthProvider,
  reauthenticateWithCredential,
  updatePassword,
  deleteUser
} from 'firebase/auth';

// Firebase configuration
const firebaseConfig = {
  apiKey: process.env.EXPO_PUBLIC_FIREBASE_API_KEY,
  authDomain: process.env.EXPO_PUBLIC_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.EXPO_PUBLIC_FIREBASE_PROJECT_ID,
  storageBucket: process.env.EXPO_PUBLIC_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.EXPO_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.EXPO_PUBLIC_FIREBASE_APP_ID,
};

// Initialize Firebase
let app: FirebaseApp;
if (getApps().length === 0) {
  app = initializeApp(firebaseConfig);
} else {
  app = getApps()[0];
}

export const auth = getAuth(app);

export interface FirebaseUser {
  uid: string;
  email: string | null;
  displayName: string | null;
  photoURL: string | null;
  emailVerified: boolean;
  createdAt: string;
  lastLoginAt: string;
}

export interface AuthResult {
  success: boolean;
  user?: FirebaseUser;
  error?: string;
}

export class FirebaseAuthService {
  private static instance: FirebaseAuthService;
  private auth: Auth;
  private currentUser: User | null = null;
  private authStateListeners: Array<(user: FirebaseUser | null) => void> = [];

  private constructor() {
    this.auth = auth;
    this.setupAuthStateListener();
  }

  public static getInstance(): FirebaseAuthService {
    if (!FirebaseAuthService.instance) {
      FirebaseAuthService.instance = new FirebaseAuthService();
    }
    return FirebaseAuthService.instance;
  }

  private setupAuthStateListener(): void {
    onAuthStateChanged(this.auth, (user) => {
      this.currentUser = user;
      const firebaseUser = user ? this.mapToFirebaseUser(user) : null;
      
      // Notify all listeners
      this.authStateListeners.forEach(listener => {
        try {
          listener(firebaseUser);
        } catch (error) {
          console.error('Auth state listener error:', error);
        }
      });
    });
  }

  private mapToFirebaseUser(user: User): FirebaseUser {
    return {
      uid: user.uid,
      email: user.email,
      displayName: user.displayName,
      photoURL: user.photoURL,
      emailVerified: user.emailVerified,
      createdAt: user.metadata.creationTime || new Date().toISOString(),
      lastLoginAt: user.metadata.lastSignInTime || new Date().toISOString(),
    };
  }

  /**
   * Sign in with email and password
   */
  public async signInWithEmail(email: string, password: string): Promise<AuthResult> {
    try {
      const userCredential = await signInWithEmailAndPassword(this.auth, email, password);
      const firebaseUser = this.mapToFirebaseUser(userCredential.user);
      
      return {
        success: true,
        user: firebaseUser
      };
    } catch (error: any) {
      console.error('Sign in error:', error);
      return {
        success: false,
        error: this.getErrorMessage(error.code)
      };
    }
  }

  /**
   * Create new user account
   */
  public async createAccount(email: string, password: string, displayName?: string): Promise<AuthResult> {
    try {
      const userCredential = await createUserWithEmailAndPassword(this.auth, email, password);
      
      // Update display name if provided
      if (displayName) {
        await updateProfile(userCredential.user, { displayName });
      }
      
      const firebaseUser = this.mapToFirebaseUser(userCredential.user);
      
      return {
        success: true,
        user: firebaseUser
      };
    } catch (error: any) {
      console.error('Account creation error:', error);
      return {
        success: false,
        error: this.getErrorMessage(error.code)
      };
    }
  }

  /**
   * Sign out current user
   */
  public async signOut(): Promise<AuthResult> {
    try {
      await signOut(this.auth);
      return { success: true };
    } catch (error: any) {
      console.error('Sign out error:', error);
      return {
        success: false,
        error: this.getErrorMessage(error.code)
      };
    }
  }

  /**
   * Send password reset email
   */
  public async sendPasswordReset(email: string): Promise<AuthResult> {
    try {
      await sendPasswordResetEmail(this.auth, email);
      return { success: true };
    } catch (error: any) {
      console.error('Password reset error:', error);
      return {
        success: false,
        error: this.getErrorMessage(error.code)
      };
    }
  }

  /**
   * Update user password
   */
  public async updateUserPassword(currentPassword: string, newPassword: string): Promise<AuthResult> {
    try {
      if (!this.currentUser || !this.currentUser.email) {
        return {
          success: false,
          error: 'No authenticated user found'
        };
      }

      // Re-authenticate user before password change
      const credential = EmailAuthProvider.credential(this.currentUser.email, currentPassword);
      await reauthenticateWithCredential(this.currentUser, credential);
      
      // Update password
      await updatePassword(this.currentUser, newPassword);
      
      return { success: true };
    } catch (error: any) {
      console.error('Password update error:', error);
      return {
        success: false,
        error: this.getErrorMessage(error.code)
      };
    }
  }

  /**
   * Update user profile
   */
  public async updateUserProfile(updates: { displayName?: string; photoURL?: string }): Promise<AuthResult> {
    try {
      if (!this.currentUser) {
        return {
          success: false,
          error: 'No authenticated user found'
        };
      }

      await updateProfile(this.currentUser, updates);
      const firebaseUser = this.mapToFirebaseUser(this.currentUser);
      
      return {
        success: true,
        user: firebaseUser
      };
    } catch (error: any) {
      console.error('Profile update error:', error);
      return {
        success: false,
        error: this.getErrorMessage(error.code)
      };
    }
  }

  /**
   * Delete user account
   */
  public async deleteAccount(password: string): Promise<AuthResult> {
    try {
      if (!this.currentUser || !this.currentUser.email) {
        return {
          success: false,
          error: 'No authenticated user found'
        };
      }

      // Re-authenticate user before deletion
      const credential = EmailAuthProvider.credential(this.currentUser.email, password);
      await reauthenticateWithCredential(this.currentUser, credential);
      
      // Delete user account
      await deleteUser(this.currentUser);
      
      return { success: true };
    } catch (error: any) {
      console.error('Account deletion error:', error);
      return {
        success: false,
        error: this.getErrorMessage(error.code)
      };
    }
  }

  /**
   * Get current user
   */
  public getCurrentUser(): FirebaseUser | null {
    return this.currentUser ? this.mapToFirebaseUser(this.currentUser) : null;
  }

  /**
   * Check if user is authenticated
   */
  public isAuthenticated(): boolean {
    return this.currentUser !== null;
  }

  /**
   * Add auth state change listener
   */
  public addAuthStateListener(listener: (user: FirebaseUser | null) => void): () => void {
    this.authStateListeners.push(listener);
    
    // Return unsubscribe function
    return () => {
      const index = this.authStateListeners.indexOf(listener);
      if (index > -1) {
        this.authStateListeners.splice(index, 1);
      }
    };
  }

  /**
   * Get user ID token for API calls
   */
  public async getIdToken(forceRefresh: boolean = false): Promise<string | null> {
    try {
      if (!this.currentUser) return null;
      return await this.currentUser.getIdToken(forceRefresh);
    } catch (error) {
      console.error('Failed to get ID token:', error);
      return null;
    }
  }

  /**
   * Convert Firebase error codes to user-friendly messages
   */
  private getErrorMessage(errorCode: string): string {
    switch (errorCode) {
      case 'auth/user-not-found':
        return 'No account found with this email address.';
      case 'auth/wrong-password':
        return 'Incorrect password. Please try again.';
      case 'auth/email-already-in-use':
        return 'An account with this email already exists.';
      case 'auth/weak-password':
        return 'Password should be at least 6 characters long.';
      case 'auth/invalid-email':
        return 'Please enter a valid email address.';
      case 'auth/user-disabled':
        return 'This account has been disabled.';
      case 'auth/too-many-requests':
        return 'Too many failed attempts. Please try again later.';
      case 'auth/network-request-failed':
        return 'Network error. Please check your connection.';
      case 'auth/requires-recent-login':
        return 'Please sign in again to complete this action.';
      default:
        return 'An unexpected error occurred. Please try again.';
    }
  }
}

export const firebaseAuthService = FirebaseAuthService.getInstance();
