import { Router } from 'express';
import { body, validationResult } from 'express-validator';
import passport from 'passport';
import rateLimit from 'express-rate-limit';

import { prisma } from '../index';
import { logger } from '../utils/logger';
import { hashPassword, verifyPassword, validatePassword } from '../utils/password';
import { generateTokenPair, refreshAccessToken, blacklistAccessToken, revokeAllUserTokens, revokeRefreshToken } from '../utils/jwt';
import { generateOAuthState, validateOAuthState, handleOAuthSuccess, handleOAuthError } from '../config/passport';
import { sendVerificationEmail, sendPasswordResetEmail } from '../utils/email';
import { businessMetrics } from '../middleware/metrics';

const router = Router();

// Stricter rate limiting for sensitive auth operations
const strictLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 3, // 3 attempts per 15 minutes
  message: 'Too many attempts, please try again later.',
  standardHeaders: true,
  legacyHeaders: false,
});

const loginLimiter = rateLimit({
  windowMs: 60 * 1000, // 1 minute
  max: 5, // 5 attempts per minute
  message: 'Too many login attempts, please try again later.',
  standardHeaders: true,
  legacyHeaders: false,
});

/**
 * @swagger
 * /auth/register:
 *   post:
 *     tags: [Authentication]
 *     summary: Register a new user account
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *               - password
 *               - firstName
 *               - lastName
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *               password:
 *                 type: string
 *                 minLength: 12
 *               firstName:
 *                 type: string
 *               lastName:
 *                 type: string
 *     responses:
 *       201:
 *         description: User registered successfully
 *       400:
 *         description: Validation error
 *       409:
 *         description: Email already exists
 */
router.post('/register', [
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Valid email is required'),
  body('password')
    .isLength({ min: 12, max: 128 })
    .withMessage('Password must be 12-128 characters long'),
  body('firstName')
    .trim()
    .isLength({ min: 1, max: 50 })
    .withMessage('First name is required (max 50 characters)'),
  body('lastName')
    .trim()
    .isLength({ min: 1, max: 50 })
    .withMessage('Last name is required (max 50 characters)')
], async (req, res) => {
  try {
    // Validate input
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      businessMetrics.authAttempts.labels('register', 'validation_error', 'auth-service').inc();
      return res.status(400).json({
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Validation failed',
          details: errors.array()
        }
      });
    }

    const { email, password, firstName, lastName } = req.body;

    // Validate password strength
    const passwordStrength = validatePassword(password);
    if (!passwordStrength.isValid) {
      businessMetrics.authAttempts.labels('register', 'weak_password', 'auth-service').inc();
      return res.status(400).json({
        error: {
          code: 'WEAK_PASSWORD',
          message: 'Password does not meet security requirements',
          details: {
            requirements: passwordStrength.requirements,
            feedback: passwordStrength.feedback
          }
        }
      });
    }

    // Check if user already exists
    const existingUser = await prisma.user.findUnique({
      where: { email }
    });

    if (existingUser) {
      businessMetrics.authAttempts.labels('register', 'email_exists', 'auth-service').inc();
      return res.status(409).json({
        error: {
          code: 'EMAIL_EXISTS',
          message: 'An account with this email already exists'
        }
      });
    }

    // Hash password
    const passwordHash = await hashPassword(password);

    // Create user
    const user = await prisma.user.create({
      data: {
        email,
        passwordHash,
        firstName,
        lastName,
        role: 'USER',
        status: 'ACTIVE'
      },
      select: {
        id: true,
        email: true,
        firstName: true,
        lastName: true,
        role: true,
        emailVerified: true,
        createdAt: true
      }
    });

    // Send verification email
    await sendVerificationEmail(user.email, user.id);

    // Log successful registration
    await prisma.auditLog.create({
      data: {
        userId: user.id,
        action: 'USER_REGISTERED',
        details: { email: user.email },
        ipAddress: req.ip,
        userAgent: req.get('User-Agent')
      }
    });

    businessMetrics.authAttempts.labels('register', 'success', 'auth-service').inc();
    logger.info('User registered successfully', { userId: user.id, email: user.email });

    res.status(201).json({
      message: 'Registration successful. Please check your email to verify your account.',
      user
    });
  } catch (error) {
    businessMetrics.errors.labels('registration_error', 'auth-service', '/auth/register').inc();
    logger.error('Registration error:', error);
    res.status(500).json({
      error: {
        code: 'REGISTRATION_FAILED',
        message: 'Registration failed. Please try again.'
      }
    });
  }
});

/**
 * @swagger
 * /auth/login:
 *   post:
 *     tags: [Authentication]
 *     summary: Login with email and password
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *               - password
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *               password:
 *                 type: string
 *               rememberMe:
 *                 type: boolean
 *     responses:
 *       200:
 *         description: Login successful
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/AuthTokens'
 *       401:
 *         description: Invalid credentials
 *       429:
 *         description: Too many login attempts
 */
router.post('/login', loginLimiter, [
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Valid email is required'),
  body('password')
    .notEmpty()
    .withMessage('Password is required')
], async (req, res) => {
  try {
    // Validate input
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      businessMetrics.authAttempts.labels('login', 'validation_error', 'auth-service').inc();
      return res.status(400).json({
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Validation failed',
          details: errors.array()
        }
      });
    }

    const { email, password } = req.body;

    // Find user
    const user = await prisma.user.findUnique({
      where: { email }
    });

    // Log login attempt
    const loginAttempt = {
      email,
      ipAddress: req.ip,
      userAgent: req.get('User-Agent'),
      success: false,
      failureReason: ''
    };

    if (!user || !user.passwordHash) {
      loginAttempt.failureReason = 'Invalid credentials';
      await prisma.loginAttempt.create({ data: loginAttempt });
      businessMetrics.authAttempts.labels('login', 'invalid_credentials', 'auth-service').inc();
      
      return res.status(401).json({
        error: {
          code: 'INVALID_CREDENTIALS',
          message: 'Invalid email or password'
        }
      });
    }

    // Check if account is blocked
    if (user.isBlocked || user.status !== 'ACTIVE') {
      loginAttempt.failureReason = 'Account blocked or inactive';
      loginAttempt.userId = user.id;
      await prisma.loginAttempt.create({ data: loginAttempt });
      businessMetrics.authAttempts.labels('login', 'account_blocked', 'auth-service').inc();
      
      return res.status(401).json({
        error: {
          code: 'ACCOUNT_BLOCKED',
          message: 'Account is blocked or inactive'
        }
      });
    }

    // Verify password
    const isPasswordValid = await verifyPassword(password, user.passwordHash);
    if (!isPasswordValid) {
      loginAttempt.failureReason = 'Invalid password';
      loginAttempt.userId = user.id;
      await prisma.loginAttempt.create({ data: loginAttempt });
      businessMetrics.authAttempts.labels('login', 'invalid_password', 'auth-service').inc();
      
      return res.status(401).json({
        error: {
          code: 'INVALID_CREDENTIALS',
          message: 'Invalid email or password'
        }
      });
    }

    // Generate token pair
    const tokens = await generateTokenPair(
      user.id,
      user.email,
      user.role,
      {
        userAgent: req.get('User-Agent'),
        ipAddress: req.ip
      }
    );

    // Set refresh token as HTTP-only cookie
    res.cookie('refreshToken', tokens.refreshToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
      path: '/auth/refresh'
    });

    // Update last login
    await prisma.user.update({
      where: { id: user.id },
      data: { lastLoginAt: new Date() }
    });

    // Log successful login
    loginAttempt.success = true;
    loginAttempt.userId = user.id;
    await prisma.loginAttempt.create({ data: loginAttempt });

    await prisma.auditLog.create({
      data: {
        userId: user.id,
        action: 'USER_LOGIN',
        details: { method: 'email_password' },
        ipAddress: req.ip,
        userAgent: req.get('User-Agent')
      }
    });

    businessMetrics.authAttempts.labels('login', 'success', 'auth-service').inc();
    logger.info('User logged in successfully', { userId: user.id, email: user.email });

    res.json({
      accessToken: tokens.accessToken,
      expiresIn: tokens.expiresIn,
      user: {
        id: user.id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        role: user.role,
        emailVerified: user.emailVerified
      }
    });
  } catch (error) {
    businessMetrics.errors.labels('login_error', 'auth-service', '/auth/login').inc();
    logger.error('Login error:', error);
    res.status(500).json({
      error: {
        code: 'LOGIN_FAILED',
        message: 'Login failed. Please try again.'
      }
    });
  }
});

/**
 * @swagger
 * /auth/refresh:
 *   post:
 *     tags: [Authentication]
 *     summary: Refresh access token using refresh token
 *     security:
 *       - refreshToken: []
 *     responses:
 *       200:
 *         description: Token refreshed successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/AuthTokens'
 *       401:
 *         description: Invalid or expired refresh token
 */
router.post('/refresh', async (req, res) => {
  try {
    const refreshToken = req.cookies.refreshToken;

    if (!refreshToken) {
      businessMetrics.authAttempts.labels('refresh', 'no_token', 'auth-service').inc();
      return res.status(401).json({
        error: {
          code: 'NO_REFRESH_TOKEN',
          message: 'Refresh token not provided'
        }
      });
    }

    // Refresh the access token
    const tokens = await refreshAccessToken(refreshToken);

    // Set new refresh token as HTTP-only cookie
    res.cookie('refreshToken', tokens.refreshToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
      path: '/auth/refresh'
    });

    businessMetrics.authAttempts.labels('refresh', 'success', 'auth-service').inc();
    logger.info('Token refreshed successfully');

    res.json({
      accessToken: tokens.accessToken,
      expiresIn: tokens.expiresIn
    });
  } catch (error) {
    businessMetrics.authAttempts.labels('refresh', 'failed', 'auth-service').inc();
    logger.error('Token refresh error:', error);

    // Clear invalid refresh token cookie
    res.clearCookie('refreshToken', { path: '/auth/refresh' });

    res.status(401).json({
      error: {
        code: 'REFRESH_FAILED',
        message: 'Failed to refresh token. Please login again.'
      }
    });
  }
});

/**
 * @swagger
 * /auth/logout:
 *   post:
 *     tags: [Authentication]
 *     summary: Logout user and invalidate tokens
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Logout successful
 *       401:
 *         description: Authentication required
 */
router.post('/logout', passport.authenticate('jwt', { session: false }), async (req, res) => {
  try {
    const user = req.user as any;
    const authHeader = req.headers.authorization;
    const accessToken = authHeader?.substring(7); // Remove 'Bearer ' prefix
    const refreshToken = req.cookies.refreshToken;

    // Blacklist access token
    if (accessToken) {
      await blacklistAccessToken(accessToken);
    }

    // Revoke refresh token
    if (refreshToken) {
      await revokeRefreshToken(refreshToken);
    }

    // Clear refresh token cookie
    res.clearCookie('refreshToken', { path: '/auth/refresh' });

    // Log logout
    await prisma.auditLog.create({
      data: {
        userId: user.id,
        action: 'USER_LOGOUT',
        ipAddress: req.ip,
        userAgent: req.get('User-Agent')
      }
    });

    logger.info('User logged out successfully', { userId: user.id });

    res.json({ message: 'Logout successful' });
  } catch (error) {
    logger.error('Logout error:', error);
    res.status(500).json({
      error: {
        code: 'LOGOUT_FAILED',
        message: 'Logout failed. Please try again.'
      }
    });
  }
});

/**
 * @swagger
 * /auth/google:
 *   get:
 *     tags: [Authentication]
 *     summary: Initiate Google OAuth login
 *     responses:
 *       302:
 *         description: Redirect to Google OAuth
 */
router.get('/google', (req, res, next) => {
  // Generate and store state parameter for CSRF protection
  const state = generateOAuthState();
  req.session.oauthState = state;

  passport.authenticate('google', {
    scope: ['profile', 'email'],
    state
  })(req, res, next);
});

/**
 * @swagger
 * /auth/google/callback:
 *   get:
 *     tags: [Authentication]
 *     summary: Google OAuth callback
 *     responses:
 *       302:
 *         description: Redirect to frontend with tokens
 */
router.get('/google/callback', (req, res, next) => {
  // Validate state parameter
  const sessionState = req.session.oauthState;
  const receivedState = req.query.state as string;

  if (!validateOAuthState(sessionState, receivedState)) {
    return handleOAuthError(new Error('Invalid state parameter'), req, res);
  }

  passport.authenticate('google', { session: false }, (err, user) => {
    if (err) {
      return handleOAuthError(err, req, res);
    }
    if (!user) {
      return handleOAuthError(new Error('Authentication failed'), req, res);
    }

    handleOAuthSuccess(user, req, res);
  })(req, res, next);
});

export { router as authRoutes };
