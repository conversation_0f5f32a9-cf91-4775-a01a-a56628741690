import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  RefreshControl,
  Modal,
  TextInput,
  Switch,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import {
  Settings,
  Users,
  MessageSquare,
  BarChart3,
  Plus,
  Edit,
  Trash2,
  Play,
  Pause,
  CheckCircle,
  Clock,
  AlertTriangle,
  TrendingUp,
  Activity,
  Send,
  Download,
  Upload,
  Target,
  Bell,
  FileText,
} from 'lucide-react-native';

interface BetaProgram {
  id: string;
  program_name: string;
  description: string;
  status: 'draft' | 'recruiting' | 'active' | 'paused' | 'completed' | 'archived';
  current_participants: number;
  max_participants?: number;
  feedback_count: number;
  engagement_score: number;
  created_at: string;
}

interface BetaAdminDashboardProps {
  userRole: 'admin' | 'beta_manager';
}

export const BetaAdminDashboard: React.FC<BetaAdminDashboardProps> = ({
  userRole,
}) => {
  const [programs, setPrograms] = useState<BetaProgram[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [activeTab, setActiveTab] = useState<'programs' | 'participants' | 'feedback' | 'analytics'>('programs');
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [selectedProgram, setSelectedProgram] = useState<BetaProgram | null>(null);

  // Form state for creating/editing programs
  const [programName, setProgramName] = useState('');
  const [programDescription, setProgramDescription] = useState('');
  const [programType, setProgramType] = useState<'feature' | 'app_version' | 'experiment' | 'early_access'>('feature');
  const [maxParticipants, setMaxParticipants] = useState('');
  const [autoApprove, setAutoApprove] = useState(false);
  const [feedbackEnabled, setFeedbackEnabled] = useState(true);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      await loadPrograms();
    } catch (error) {
      console.error('Error loading dashboard data:', error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const loadPrograms = async () => {
    try {
      // Mock data for demonstration
      const mockPrograms: BetaProgram[] = [
        {
          id: '1',
          program_name: 'AI Species Recognition Beta',
          description: 'Testing new AI-powered species identification features',
          status: 'active',
          current_participants: 45,
          max_participants: 100,
          feedback_count: 127,
          engagement_score: 4.2,
          created_at: '2024-01-15T00:00:00Z',
        },
        {
          id: '2',
          program_name: 'Mobile App Redesign',
          description: 'Beta testing the new mobile app interface and user experience',
          status: 'recruiting',
          current_participants: 23,
          max_participants: 50,
          feedback_count: 34,
          engagement_score: 3.8,
          created_at: '2024-02-01T00:00:00Z',
        },
        {
          id: '3',
          program_name: 'Premium Features Preview',
          description: 'Early access to premium features for selected users',
          status: 'paused',
          current_participants: 12,
          max_participants: 25,
          feedback_count: 18,
          engagement_score: 4.5,
          created_at: '2024-01-20T00:00:00Z',
        },
      ];
      
      setPrograms(mockPrograms);
    } catch (error) {
      console.error('Error loading programs:', error);
    }
  };

  const onRefresh = () => {
    setRefreshing(true);
    loadDashboardData();
  };

  const handleCreateProgram = async () => {
    if (!programName.trim() || !programDescription.trim()) {
      Alert.alert('Error', 'Please provide program name and description');
      return;
    }

    try {
      // In a real implementation, this would call the API
      const newProgram: BetaProgram = {
        id: Date.now().toString(),
        program_name: programName.trim(),
        description: programDescription.trim(),
        status: 'draft',
        current_participants: 0,
        max_participants: maxParticipants ? parseInt(maxParticipants) : undefined,
        feedback_count: 0,
        engagement_score: 0,
        created_at: new Date().toISOString(),
      };

      setPrograms(prev => [newProgram, ...prev]);
      setShowCreateModal(false);
      resetForm();
      
      Alert.alert('Success', 'Beta program created successfully');
    } catch (error) {
      Alert.alert('Error', 'Failed to create beta program');
    }
  };

  const handleUpdateProgramStatus = async (programId: string, newStatus: string) => {
    try {
      setPrograms(prev => prev.map(program => 
        program.id === programId 
          ? { ...program, status: newStatus as any }
          : program
      ));
      
      Alert.alert('Success', `Program status updated to ${newStatus}`);
    } catch (error) {
      Alert.alert('Error', 'Failed to update program status');
    }
  };

  const handleDeleteProgram = async (programId: string) => {
    Alert.alert(
      'Delete Program',
      'Are you sure you want to delete this beta program? This action cannot be undone.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              setPrograms(prev => prev.filter(program => program.id !== programId));
              Alert.alert('Success', 'Beta program deleted successfully');
            } catch (error) {
              Alert.alert('Error', 'Failed to delete beta program');
            }
          },
        },
      ]
    );
  };

  const resetForm = () => {
    setProgramName('');
    setProgramDescription('');
    setProgramType('feature');
    setMaxParticipants('');
    setAutoApprove(false);
    setFeedbackEnabled(true);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return '#10B981';
      case 'recruiting': return '#3B82F6';
      case 'paused': return '#F59E0B';
      case 'draft': return '#6B7280';
      case 'completed': return '#8B5CF6';
      case 'archived': return '#6B7280';
      default: return '#6B7280';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active': return Play;
      case 'recruiting': return Users;
      case 'paused': return Pause;
      case 'draft': return FileText;
      case 'completed': return CheckCircle;
      case 'archived': return Download;
      default: return Clock;
    }
  };

  const renderPrograms = () => (
    <View style={styles.section}>
      <View style={styles.sectionHeader}>
        <Text style={styles.sectionTitle}>Beta Programs ({programs.length})</Text>
        <TouchableOpacity
          style={styles.addButton}
          onPress={() => setShowCreateModal(true)}
        >
          <Plus size={20} color="white" />
        </TouchableOpacity>
      </View>

      {programs.map((program) => {
        const StatusIcon = getStatusIcon(program.status);
        
        return (
          <View key={program.id} style={styles.programCard}>
            <View style={styles.programHeader}>
              <View style={styles.programInfo}>
                <Text style={styles.programName}>{program.program_name}</Text>
                <Text style={styles.programDescription} numberOfLines={2}>
                  {program.description}
                </Text>
              </View>
              
              <View style={styles.programStatus}>
                <View style={[styles.statusBadge, { backgroundColor: getStatusColor(program.status) }]}>
                  <StatusIcon size={12} color="white" />
                  <Text style={styles.statusText}>{program.status.toUpperCase()}</Text>
                </View>
              </View>
            </View>

            <View style={styles.programMetrics}>
              <View style={styles.metricItem}>
                <Users size={16} color="#6B7280" />
                <Text style={styles.metricText}>
                  {program.current_participants}
                  {program.max_participants && `/${program.max_participants}`} participants
                </Text>
              </View>
              
              <View style={styles.metricItem}>
                <MessageSquare size={16} color="#6B7280" />
                <Text style={styles.metricText}>{program.feedback_count} feedback</Text>
              </View>
              
              <View style={styles.metricItem}>
                <TrendingUp size={16} color="#10B981" />
                <Text style={styles.metricText}>{program.engagement_score.toFixed(1)} engagement</Text>
              </View>
            </View>

            <View style={styles.programActions}>
              <TouchableOpacity
                style={styles.actionButton}
                onPress={() => setSelectedProgram(program)}
              >
                <Edit size={16} color="#3B82F6" />
                <Text style={styles.actionButtonText}>Edit</Text>
              </TouchableOpacity>
              
              {program.status === 'draft' && (
                <TouchableOpacity
                  style={styles.actionButton}
                  onPress={() => handleUpdateProgramStatus(program.id, 'recruiting')}
                >
                  <Play size={16} color="#10B981" />
                  <Text style={styles.actionButtonText}>Start Recruiting</Text>
                </TouchableOpacity>
              )}
              
              {program.status === 'recruiting' && (
                <TouchableOpacity
                  style={styles.actionButton}
                  onPress={() => handleUpdateProgramStatus(program.id, 'active')}
                >
                  <CheckCircle size={16} color="#10B981" />
                  <Text style={styles.actionButtonText}>Activate</Text>
                </TouchableOpacity>
              )}
              
              {program.status === 'active' && (
                <TouchableOpacity
                  style={styles.actionButton}
                  onPress={() => handleUpdateProgramStatus(program.id, 'paused')}
                >
                  <Pause size={16} color="#F59E0B" />
                  <Text style={styles.actionButtonText}>Pause</Text>
                </TouchableOpacity>
              )}
              
              <TouchableOpacity
                style={styles.actionButton}
                onPress={() => handleDeleteProgram(program.id)}
              >
                <Trash2 size={16} color="#EF4444" />
                <Text style={[styles.actionButtonText, { color: '#EF4444' }]}>Delete</Text>
              </TouchableOpacity>
            </View>
          </View>
        );
      })}
    </View>
  );

  const renderAnalytics = () => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>Beta Testing Analytics</Text>
      
      <View style={styles.analyticsGrid}>
        <View style={styles.analyticsCard}>
          <Users size={24} color="#3B82F6" />
          <Text style={styles.analyticsValue}>
            {programs.reduce((sum, p) => sum + p.current_participants, 0)}
          </Text>
          <Text style={styles.analyticsLabel}>Total Participants</Text>
        </View>
        
        <View style={styles.analyticsCard}>
          <MessageSquare size={24} color="#10B981" />
          <Text style={styles.analyticsValue}>
            {programs.reduce((sum, p) => sum + p.feedback_count, 0)}
          </Text>
          <Text style={styles.analyticsLabel}>Total Feedback</Text>
        </View>
        
        <View style={styles.analyticsCard}>
          <Activity size={24} color="#F59E0B" />
          <Text style={styles.analyticsValue}>
            {programs.filter(p => p.status === 'active').length}
          </Text>
          <Text style={styles.analyticsLabel}>Active Programs</Text>
        </View>
        
        <View style={styles.analyticsCard}>
          <TrendingUp size={24} color="#8B5CF6" />
          <Text style={styles.analyticsValue}>
            {(programs.reduce((sum, p) => sum + p.engagement_score, 0) / programs.length || 0).toFixed(1)}
          </Text>
          <Text style={styles.analyticsLabel}>Avg Engagement</Text>
        </View>
      </View>
    </View>
  );

  const renderCreateModal = () => (
    <Modal
      visible={showCreateModal}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={() => setShowCreateModal(false)}
    >
      <View style={styles.modalContainer}>
        <View style={styles.modalHeader}>
          <Text style={styles.modalTitle}>Create Beta Program</Text>
          <TouchableOpacity onPress={() => setShowCreateModal(false)}>
            <Text style={styles.closeButton}>Cancel</Text>
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.modalContent}>
          <View style={styles.inputSection}>
            <Text style={styles.inputLabel}>Program Name *</Text>
            <TextInput
              style={styles.textInput}
              value={programName}
              onChangeText={setProgramName}
              placeholder="Enter program name"
              placeholderTextColor="#9CA3AF"
            />
          </View>

          <View style={styles.inputSection}>
            <Text style={styles.inputLabel}>Description *</Text>
            <TextInput
              style={[styles.textInput, styles.multilineInput]}
              value={programDescription}
              onChangeText={setProgramDescription}
              placeholder="Describe the beta program"
              placeholderTextColor="#9CA3AF"
              multiline
              numberOfLines={3}
            />
          </View>

          <View style={styles.inputSection}>
            <Text style={styles.inputLabel}>Program Type</Text>
            <View style={styles.typeSelector}>
              {[
                { key: 'feature', label: 'Feature Testing' },
                { key: 'app_version', label: 'App Version' },
                { key: 'experiment', label: 'Experiment' },
                { key: 'early_access', label: 'Early Access' },
              ].map((type) => (
                <TouchableOpacity
                  key={type.key}
                  style={[
                    styles.typeOption,
                    programType === type.key && styles.selectedTypeOption
                  ]}
                  onPress={() => setProgramType(type.key as any)}
                >
                  <Text style={[
                    styles.typeOptionText,
                    programType === type.key && styles.selectedTypeOptionText
                  ]}>
                    {type.label}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>

          <View style={styles.inputSection}>
            <Text style={styles.inputLabel}>Max Participants (Optional)</Text>
            <TextInput
              style={styles.textInput}
              value={maxParticipants}
              onChangeText={setMaxParticipants}
              placeholder="Leave empty for unlimited"
              placeholderTextColor="#9CA3AF"
              keyboardType="numeric"
            />
          </View>

          <View style={styles.switchSection}>
            <Text style={styles.switchLabel}>Auto-approve applications</Text>
            <Switch
              value={autoApprove}
              onValueChange={setAutoApprove}
              trackColor={{ false: '#E5E7EB', true: '#10B981' }}
              thumbColor={autoApprove ? '#FFFFFF' : '#F3F4F6'}
            />
          </View>

          <View style={styles.switchSection}>
            <Text style={styles.switchLabel}>Enable feedback collection</Text>
            <Switch
              value={feedbackEnabled}
              onValueChange={setFeedbackEnabled}
              trackColor={{ false: '#E5E7EB', true: '#10B981' }}
              thumbColor={feedbackEnabled ? '#FFFFFF' : '#F3F4F6'}
            />
          </View>

          <TouchableOpacity
            style={[
              styles.submitButton,
              (!programName.trim() || !programDescription.trim()) && styles.disabledButton
            ]}
            onPress={handleCreateProgram}
            disabled={!programName.trim() || !programDescription.trim()}
          >
            <Plus size={16} color="white" />
            <Text style={styles.submitButtonText}>Create Program</Text>
          </TouchableOpacity>
        </ScrollView>
      </View>
    </Modal>
  );

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <Activity size={32} color="#3B82F6" />
        <Text style={styles.loadingText}>Loading beta admin dashboard...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Header */}
      <LinearGradient
        colors={['#8B5CF6', '#7C3AED']}
        style={styles.header}
      >
        <Settings size={32} color="white" />
        <Text style={styles.headerTitle}>Beta Admin</Text>
        <Text style={styles.headerSubtitle}>
          Manage beta programs and participant feedback
        </Text>
      </LinearGradient>

      {/* Tab Navigation */}
      <View style={styles.tabContainer}>
        {[
          { key: 'programs', label: 'Programs', icon: FileText },
          { key: 'participants', label: 'Participants', icon: Users },
          { key: 'feedback', label: 'Feedback', icon: MessageSquare },
          { key: 'analytics', label: 'Analytics', icon: BarChart3 },
        ].map(({ key, label, icon: Icon }) => (
          <TouchableOpacity
            key={key}
            style={[styles.tab, activeTab === key && styles.activeTab]}
            onPress={() => setActiveTab(key as any)}
          >
            <Icon size={18} color={activeTab === key ? '#8B5CF6' : '#6B7280'} />
            <Text style={[styles.tabText, activeTab === key && styles.activeTabText]}>
              {label}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      {/* Content */}
      <ScrollView
        style={styles.content}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {activeTab === 'programs' && renderPrograms()}
        {activeTab === 'analytics' && renderAnalytics()}
        {/* Other tabs would be implemented similarly */}
      </ScrollView>

      {/* Create Modal */}
      {renderCreateModal()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#F9FAFB',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#6B7280',
  },
  header: {
    padding: 24,
    paddingTop: 60,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: 'white',
    marginTop: 8,
  },
  headerSubtitle: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.8)',
    marginTop: 4,
    textAlign: 'center',
  },
  tabContainer: {
    flexDirection: 'row',
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  tab: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    gap: 6,
  },
  activeTab: {
    borderBottomWidth: 2,
    borderBottomColor: '#8B5CF6',
  },
  tabText: {
    fontSize: 12,
    fontWeight: '500',
    color: '#6B7280',
  },
  activeTabText: {
    color: '#8B5CF6',
  },
  content: {
    flex: 1,
    padding: 20,
  },
  section: {
    marginBottom: 24,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827',
  },
  addButton: {
    backgroundColor: '#8B5CF6',
    borderRadius: 20,
    padding: 8,
  },
  programCard: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  programHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  programInfo: {
    flex: 1,
    marginRight: 12,
  },
  programName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 4,
  },
  programDescription: {
    fontSize: 14,
    color: '#6B7280',
    lineHeight: 18,
  },
  programStatus: {
    alignItems: 'flex-end',
  },
  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 4,
    gap: 4,
  },
  statusText: {
    fontSize: 10,
    fontWeight: '600',
    color: 'white',
  },
  programMetrics: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 16,
    marginBottom: 16,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
  },
  metricItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  metricText: {
    fontSize: 12,
    color: '#6B7280',
  },
  programActions: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
    paddingHorizontal: 8,
    paddingVertical: 4,
  },
  actionButtonText: {
    fontSize: 12,
    color: '#3B82F6',
    fontWeight: '500',
  },
  analyticsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  analyticsCard: {
    flex: 1,
    minWidth: '45%',
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  analyticsValue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#111827',
    marginTop: 8,
  },
  analyticsLabel: {
    fontSize: 12,
    color: '#6B7280',
    marginTop: 4,
    textAlign: 'center',
  },
  modalContainer: {
    flex: 1,
    backgroundColor: 'white',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827',
  },
  closeButton: {
    fontSize: 16,
    color: '#8B5CF6',
    fontWeight: '500',
  },
  modalContent: {
    flex: 1,
    padding: 20,
  },
  inputSection: {
    marginBottom: 20,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: '#111827',
    marginBottom: 8,
  },
  textInput: {
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    color: '#111827',
  },
  multilineInput: {
    textAlignVertical: 'top',
    minHeight: 80,
  },
  typeSelector: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  typeOption: {
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
    backgroundColor: 'white',
  },
  selectedTypeOption: {
    backgroundColor: '#8B5CF6',
    borderColor: '#8B5CF6',
  },
  typeOptionText: {
    fontSize: 14,
    color: '#6B7280',
  },
  selectedTypeOptionText: {
    color: 'white',
    fontWeight: '500',
  },
  switchSection: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  switchLabel: {
    fontSize: 16,
    color: '#111827',
    flex: 1,
  },
  submitButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#8B5CF6',
    borderRadius: 8,
    padding: 16,
    gap: 8,
    marginTop: 20,
  },
  disabledButton: {
    backgroundColor: '#9CA3AF',
  },
  submitButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: 'white',
  },
});

export default BetaAdminDashboard;
