import AsyncStorage from '@react-native-async-storage/async-storage';

export interface ExportData {
  settings: any;
  collections: any[];
  achievements: any[];
  scanHistory: any[];
  exportDate: string;
  version: string;
}

export interface StorageInfo {
  totalSize: number;
  settingsSize: number;
  collectionsSize: number;
  cacheSize: number;
  imagesSize: number;
}

export class DataManager {
  private static instance: DataManager;

  static getInstance(): DataManager {
    if (!DataManager.instance) {
      DataManager.instance = new DataManager();
    }
    return DataManager.instance;
  }

  async exportAllData(): Promise<ExportData> {
    try {
      const [settings, collections, achievements, scanHistory] = await Promise.all([
        AsyncStorage.getItem('userSettings'),
        AsyncStorage.getItem('userCollections'),
        AsyncStorage.getItem('userAchievements'),
        AsyncStorage.getItem('scanHistory'),
      ]);

      const exportData: ExportData = {
        settings: settings ? JSON.parse(settings) : {},
        collections: collections ? JSON.parse(collections) : [],
        achievements: achievements ? JSON.parse(achievements) : [],
        scanHistory: scanHistory ? JSON.parse(scanHistory) : [],
        exportDate: new Date().toISOString(),
        version: '1.0.0',
      };

      return exportData;
    } catch (error) {
      console.error('Failed to export data:', error);
      throw new Error('Failed to export data');
    }
  }

  async importData(data: ExportData): Promise<void> {
    try {
      const operations = [];

      if (data.settings) {
        operations.push(AsyncStorage.setItem('userSettings', JSON.stringify(data.settings)));
      }

      if (data.collections) {
        operations.push(AsyncStorage.setItem('userCollections', JSON.stringify(data.collections)));
      }

      if (data.achievements) {
        operations.push(AsyncStorage.setItem('userAchievements', JSON.stringify(data.achievements)));
      }

      if (data.scanHistory) {
        operations.push(AsyncStorage.setItem('scanHistory', JSON.stringify(data.scanHistory)));
      }

      await Promise.all(operations);
    } catch (error) {
      console.error('Failed to import data:', error);
      throw new Error('Failed to import data');
    }
  }

  async clearCache(): Promise<void> {
    try {
      const cacheKeys = [
        'identificationCache',
        'imageCache',
        'speciesCache',
        'locationCache',
        'tempData',
      ];

      await Promise.all(cacheKeys.map(key => AsyncStorage.removeItem(key)));
    } catch (error) {
      console.error('Failed to clear cache:', error);
      throw new Error('Failed to clear cache');
    }
  }

  async clearAllData(): Promise<void> {
    try {
      await AsyncStorage.clear();
    } catch (error) {
      console.error('Failed to clear all data:', error);
      throw new Error('Failed to clear all data');
    }
  }

  async getStorageInfo(): Promise<StorageInfo> {
    try {
      const keys = await AsyncStorage.getAllKeys();
      const items = await AsyncStorage.multiGet(keys);
      
      let totalSize = 0;
      let settingsSize = 0;
      let collectionsSize = 0;
      let cacheSize = 0;
      let imagesSize = 0;

      items.forEach(([key, value]) => {
        const size = value ? new Blob([value]).size : 0;
        totalSize += size;

        if (key.includes('settings')) {
          settingsSize += size;
        } else if (key.includes('collection')) {
          collectionsSize += size;
        } else if (key.includes('cache')) {
          cacheSize += size;
        } else if (key.includes('image')) {
          imagesSize += size;
        }
      });

      return {
        totalSize,
        settingsSize,
        collectionsSize,
        cacheSize,
        imagesSize,
      };
    } catch (error) {
      console.error('Failed to get storage info:', error);
      return {
        totalSize: 0,
        settingsSize: 0,
        collectionsSize: 0,
        cacheSize: 0,
        imagesSize: 0,
      };
    }
  }

  formatBytes(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  async backupToFile(): Promise<string> {
    try {
      const data = await this.exportAllData();
      const jsonString = JSON.stringify(data, null, 2);
      
      // In a real app, you would use a file picker or share API
      // For web, we can create a download link
      if (typeof window !== 'undefined') {
        const blob = new Blob([jsonString], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `bioscan-backup-${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);
      }
      
      return jsonString;
    } catch (error) {
      console.error('Failed to backup to file:', error);
      throw new Error('Failed to create backup file');
    }
  }
}

export default DataManager;