import { supabase } from '../lib/supabase';
import { Platform } from 'react-native';
import Constants from 'expo-constants';
import { FeatureFlagService, UserContext, ABExperiment, ExperimentVariant } from './FeatureFlagService';

export interface ExperimentAssignment {
  experiment_id: string;
  variant_id: string;
  variant_key: string;
  assignment_method: 'random' | 'deterministic' | 'manual';
  assignment_hash: string;
  assignment_context: any;
  user_segment?: string;
}

export interface ConversionEvent {
  event_name: string;
  event_properties: any;
  conversion_value: number;
  session_id?: string;
}

export interface ExperimentResults {
  experiment_id: string;
  variants: {
    [variant_key: string]: {
      exposure_count: number;
      conversion_count: number;
      conversion_rate: number;
      confidence_interval: [number, number];
      statistical_significance: number;
    };
  };
  winner?: string;
  confidence_level: number;
  is_statistically_significant: boolean;
}

export class ABTestingService {
  private static instance: ABTestingService;
  private featureFlagService: FeatureFlagService;
  private userContext: UserContext | null = null;
  private assignmentCache: Map<string, ExperimentAssignment> = new Map();

  static getInstance(): ABTestingService {
    if (!ABTestingService.instance) {
      ABTestingService.instance = new ABTestingService();
    }
    return ABTestingService.instance;
  }

  constructor() {
    this.featureFlagService = FeatureFlagService.getInstance();
  }

  /**
   * Initialize the A/B testing service
   */
  async initialize(): Promise<void> {
    try {
      await this.loadUserContext();
      await this.loadUserAssignments();
      console.log('ABTestingService initialized successfully');
    } catch (error) {
      console.error('Error initializing ABTestingService:', error);
    }
  }

  /**
   * Get experiment assignment for user
   */
  async getExperimentAssignment(experimentKey: string): Promise<ExperimentAssignment | null> {
    try {
      // Check cache first
      const cached = this.assignmentCache.get(experimentKey);
      if (cached) return cached;

      // Get experiment
      const experiment = await this.getExperiment(experimentKey);
      if (!experiment || experiment.status !== 'running') {
        return null;
      }

      // Check if user is eligible
      if (!this.isUserEligible(experiment)) {
        return null;
      }

      // Get existing assignment
      let assignment = await this.getUserAssignment(experiment.id);
      
      // Create new assignment if none exists
      if (!assignment) {
        assignment = await this.createUserAssignment(experiment);
      }

      // Cache assignment
      if (assignment) {
        this.assignmentCache.set(experimentKey, assignment);
      }

      return assignment;
    } catch (error) {
      console.error(`Error getting experiment assignment for ${experimentKey}:`, error);
      return null;
    }
  }

  /**
   * Get variant for experiment
   */
  async getVariant(experimentKey: string): Promise<ExperimentVariant | null> {
    try {
      const assignment = await this.getExperimentAssignment(experimentKey);
      if (!assignment) return null;

      const { data: variant, error } = await supabase
        .from('experiment_variants')
        .select('*')
        .eq('id', assignment.variant_id)
        .single();

      if (error) throw error;
      return variant;
    } catch (error) {
      console.error(`Error getting variant for ${experimentKey}:`, error);
      return null;
    }
  }

  /**
   * Track experiment exposure
   */
  async trackExposure(experimentKey: string): Promise<void> {
    try {
      const assignment = await this.getExperimentAssignment(experimentKey);
      if (!assignment) return;

      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return;

      // Update exposure tracking
      await supabase
        .from('user_experiment_assignments')
        .update({
          last_exposure_at: new Date().toISOString(),
          exposure_count: supabase.sql`exposure_count + 1`,
        })
        .eq('user_id', user.id)
        .eq('experiment_id', assignment.experiment_id);

      // Log exposure event
      await this.trackExperimentEvent('exposure', assignment.experiment_id, assignment.variant_id);

      // Update variant exposure count
      await supabase
        .from('experiment_variants')
        .update({
          exposure_count: supabase.sql`exposure_count + 1`,
        })
        .eq('id', assignment.variant_id);

    } catch (error) {
      console.error(`Error tracking exposure for ${experimentKey}:`, error);
    }
  }

  /**
   * Track conversion event
   */
  async trackConversion(
    experimentKey: string,
    conversionEvent: ConversionEvent
  ): Promise<void> {
    try {
      const assignment = await this.getExperimentAssignment(experimentKey);
      if (!assignment) return;

      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return;

      // Check if user has already converted
      const { data: existingAssignment } = await supabase
        .from('user_experiment_assignments')
        .select('has_converted')
        .eq('user_id', user.id)
        .eq('experiment_id', assignment.experiment_id)
        .single();

      if (existingAssignment?.has_converted) {
        console.log('User has already converted for this experiment');
        return;
      }

      // Update conversion tracking
      await supabase
        .from('user_experiment_assignments')
        .update({
          has_converted: true,
          conversion_events: supabase.sql`conversion_events || ${JSON.stringify([conversionEvent])}::jsonb`,
          conversion_value: conversionEvent.conversion_value,
        })
        .eq('user_id', user.id)
        .eq('experiment_id', assignment.experiment_id);

      // Log conversion event
      await this.trackExperimentEvent(
        'conversion',
        assignment.experiment_id,
        assignment.variant_id,
        conversionEvent
      );

      // Update variant conversion count
      await supabase
        .from('experiment_variants')
        .update({
          conversion_count: supabase.sql`conversion_count + 1`,
          conversion_rate: supabase.sql`CASE WHEN exposure_count > 0 THEN (conversion_count + 1)::decimal / exposure_count ELSE 0 END`,
        })
        .eq('id', assignment.variant_id);

    } catch (error) {
      console.error(`Error tracking conversion for ${experimentKey}:`, error);
    }
  }

  /**
   * Track goal completion
   */
  async trackGoal(experimentKey: string, goalName: string, value?: number): Promise<void> {
    const conversionEvent: ConversionEvent = {
      event_name: goalName,
      event_properties: { goal_name: goalName },
      conversion_value: value || 1,
    };

    await this.trackConversion(experimentKey, conversionEvent);
  }

  /**
   * Get experiment results
   */
  async getExperimentResults(experimentKey: string): Promise<ExperimentResults | null> {
    try {
      const experiment = await this.getExperiment(experimentKey);
      if (!experiment) return null;

      const { data: variants, error } = await supabase
        .from('experiment_variants')
        .select('*')
        .eq('experiment_id', experiment.id);

      if (error) throw error;

      const results: ExperimentResults = {
        experiment_id: experiment.id,
        variants: {},
        confidence_level: experiment.confidence_level || 0.95,
        is_statistically_significant: false,
      };

      // Calculate results for each variant
      for (const variant of variants || []) {
        const conversionRate = variant.exposure_count > 0 
          ? variant.conversion_count / variant.exposure_count 
          : 0;

        // Calculate confidence interval (simplified)
        const margin = this.calculateMarginOfError(
          variant.conversion_count,
          variant.exposure_count,
          results.confidence_level
        );

        results.variants[variant.variant_key] = {
          exposure_count: variant.exposure_count,
          conversion_count: variant.conversion_count,
          conversion_rate: conversionRate,
          confidence_interval: [
            Math.max(0, conversionRate - margin),
            Math.min(1, conversionRate + margin)
          ],
          statistical_significance: 0, // Would need proper statistical test
        };
      }

      // Determine winner (simplified)
      const variantKeys = Object.keys(results.variants);
      if (variantKeys.length >= 2) {
        const sortedVariants = variantKeys.sort((a, b) => 
          results.variants[b].conversion_rate - results.variants[a].conversion_rate
        );
        results.winner = sortedVariants[0];
        
        // Check statistical significance (simplified)
        const winner = results.variants[results.winner];
        const control = results.variants[variantKeys.find(k => k !== results.winner) || ''];
        
        if (winner && control && winner.exposure_count >= 100 && control.exposure_count >= 100) {
          results.is_statistically_significant = 
            Math.abs(winner.conversion_rate - control.conversion_rate) > 0.02; // 2% difference
        }
      }

      return results;
    } catch (error) {
      console.error(`Error getting experiment results for ${experimentKey}:`, error);
      return null;
    }
  }

  /**
   * Force user into specific variant (for testing)
   */
  async forceVariant(experimentKey: string, variantKey: string): Promise<void> {
    try {
      const experiment = await this.getExperiment(experimentKey);
      if (!experiment) throw new Error('Experiment not found');

      const { data: variant } = await supabase
        .from('experiment_variants')
        .select('*')
        .eq('experiment_id', experiment.id)
        .eq('variant_key', variantKey)
        .single();

      if (!variant) throw new Error('Variant not found');

      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      // Create manual assignment
      await supabase
        .from('user_experiment_assignments')
        .upsert({
          user_id: user.id,
          experiment_id: experiment.id,
          variant_id: variant.id,
          assignment_method: 'manual',
          assignment_hash: 'manual',
          assignment_context: { forced: true },
        });

      // Clear cache
      this.assignmentCache.delete(experimentKey);

      console.log(`User forced into variant ${variantKey} for experiment ${experimentKey}`);
    } catch (error) {
      console.error(`Error forcing variant for ${experimentKey}:`, error);
    }
  }

  // Private methods

  private async loadUserContext(): Promise<void> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return;

      this.userContext = {
        user_id: user.id,
        email: user.email,
        device: {
          platform: Platform.OS,
          app_version: Constants.expoConfig?.version || '1.0.0',
        },
      };

      // Load additional user data
      const { data: profile } = await supabase
        .from('user_profiles')
        .select('subscription_tier, created_at')
        .eq('id', user.id)
        .single();

      if (profile) {
        this.userContext.subscription_tier = profile.subscription_tier;
        this.userContext.registration_date = profile.created_at;
      }
    } catch (error) {
      console.error('Error loading user context:', error);
    }
  }

  private async loadUserAssignments(): Promise<void> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return;

      const { data: assignments } = await supabase
        .from('user_experiment_assignments')
        .select(`
          *,
          ab_experiments!inner(experiment_key),
          experiment_variants!inner(variant_key)
        `)
        .eq('user_id', user.id);

      assignments?.forEach(assignment => {
        const experimentKey = assignment.ab_experiments.experiment_key;
        this.assignmentCache.set(experimentKey, {
          experiment_id: assignment.experiment_id,
          variant_id: assignment.variant_id,
          variant_key: assignment.experiment_variants.variant_key,
          assignment_method: assignment.assignment_method,
          assignment_hash: assignment.assignment_hash,
          assignment_context: assignment.assignment_context,
          user_segment: assignment.user_segment,
        });
      });
    } catch (error) {
      console.error('Error loading user assignments:', error);
    }
  }

  private async getExperiment(experimentKey: string): Promise<ABExperiment | null> {
    try {
      const { data, error } = await supabase
        .from('ab_experiments')
        .select('*')
        .eq('experiment_key', experimentKey)
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error(`Error getting experiment ${experimentKey}:`, error);
      return null;
    }
  }

  private isUserEligible(experiment: ABExperiment): boolean {
    if (!this.userContext) return false;

    // Check traffic allocation
    const userHash = this.getUserHash(experiment.experiment_key);
    if (userHash > experiment.traffic_allocation) {
      return false;
    }

    // Check targeting rules
    if (experiment.targeting_rules && Object.keys(experiment.targeting_rules).length > 0) {
      return this.evaluateTargetingRules(experiment.targeting_rules);
    }

    return true;
  }

  private evaluateTargetingRules(rules: any): boolean {
    // Simplified targeting evaluation
    // In a real implementation, this would be more sophisticated
    return true;
  }

  private async getUserAssignment(experimentId: string): Promise<ExperimentAssignment | null> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return null;

      const { data } = await supabase
        .from('user_experiment_assignments')
        .select(`
          *,
          experiment_variants!inner(variant_key)
        `)
        .eq('user_id', user.id)
        .eq('experiment_id', experimentId)
        .single();

      if (!data) return null;

      return {
        experiment_id: data.experiment_id,
        variant_id: data.variant_id,
        variant_key: data.experiment_variants.variant_key,
        assignment_method: data.assignment_method,
        assignment_hash: data.assignment_hash,
        assignment_context: data.assignment_context,
        user_segment: data.user_segment,
      };
    } catch (error) {
      return null;
    }
  }

  private async createUserAssignment(experiment: ABExperiment): Promise<ExperimentAssignment | null> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return null;

      // Get experiment variants
      const { data: variants } = await supabase
        .from('experiment_variants')
        .select('*')
        .eq('experiment_id', experiment.id)
        .order('traffic_weight', { ascending: false });

      if (!variants || variants.length === 0) return null;

      // Select variant based on weights
      const selectedVariant = this.selectVariantByWeight(variants);
      const assignmentHash = this.getUserHash(experiment.experiment_key + selectedVariant.variant_key);

      // Create assignment
      const { data: assignment } = await supabase
        .from('user_experiment_assignments')
        .insert({
          user_id: user.id,
          experiment_id: experiment.id,
          variant_id: selectedVariant.id,
          assignment_method: 'deterministic',
          assignment_hash: assignmentHash.toString(),
          assignment_context: this.userContext,
          user_segment: this.userContext?.subscription_tier,
        })
        .select()
        .single();

      if (!assignment) return null;

      return {
        experiment_id: assignment.experiment_id,
        variant_id: assignment.variant_id,
        variant_key: selectedVariant.variant_key,
        assignment_method: assignment.assignment_method,
        assignment_hash: assignment.assignment_hash,
        assignment_context: assignment.assignment_context,
        user_segment: assignment.user_segment,
      };
    } catch (error) {
      console.error('Error creating user assignment:', error);
      return null;
    }
  }

  private selectVariantByWeight(variants: ExperimentVariant[]): ExperimentVariant {
    const totalWeight = variants.reduce((sum, v) => sum + v.traffic_weight, 0);
    const random = Math.random() * totalWeight;
    
    let currentWeight = 0;
    for (const variant of variants) {
      currentWeight += variant.traffic_weight;
      if (random <= currentWeight) {
        return variant;
      }
    }
    
    return variants[0]; // Fallback
  }

  private getUserHash(input: string): number {
    if (!this.userContext?.user_id) return Math.random() * 100;
    
    const hashInput = `${this.userContext.user_id}:${input}`;
    let hash = 0;
    
    for (let i = 0; i < hashInput.length; i++) {
      const char = hashInput.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    
    return Math.abs(hash) % 100;
  }

  private calculateMarginOfError(conversions: number, exposures: number, confidence: number): number {
    if (exposures === 0) return 0;
    
    const p = conversions / exposures;
    const z = confidence === 0.95 ? 1.96 : 2.58; // 95% or 99% confidence
    
    return z * Math.sqrt((p * (1 - p)) / exposures);
  }

  private async trackExperimentEvent(
    eventType: string,
    experimentId: string,
    variantId: string,
    eventData?: ConversionEvent
  ): Promise<void> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return;

      await supabase
        .from('experiment_events')
        .insert({
          event_type: eventType,
          user_id: user.id,
          experiment_id: experimentId,
          variant_id: variantId,
          event_name: eventData?.event_name,
          event_properties: eventData?.event_properties || {},
          conversion_value: eventData?.conversion_value || 0,
          user_context: this.userContext,
          session_id: eventData?.session_id,
          platform: Platform.OS,
          app_version: Constants.expoConfig?.version,
        });
    } catch (error) {
      console.error('Error tracking experiment event:', error);
    }
  }
}
