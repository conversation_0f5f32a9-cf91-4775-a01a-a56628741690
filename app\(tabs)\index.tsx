import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
  ScrollView,
  Platform,
  SafeAreaView,
  Alert,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { CameraView, CameraType, useCameraPermissions } from 'expo-camera';
import { useRouter } from 'expo-router';
import {
  Camera,
  Image as ImageIcon,
  Mic,
  Zap,
  Target,
  Sparkles,
  ChevronRight,
  Leaf,
  Bug,
  Flower,
} from 'lucide-react-native';
import HapticService from '@/components/HapticService';
import NotificationService from '@/components/NotificationService';

const { width, height } = Dimensions.get('window');

export default function ScanScreen() {
  const [facing, setFacing] = useState<CameraType>('back');
  const [permission, requestPermission] = useCameraPermissions();
  const [isScanning, setIsScanning] = useState(false);
  const [showCamera, setShowCamera] = useState(false);
  const router = useRouter();

  const hapticService = HapticService.getInstance();
  const notificationService = NotificationService.getInstance();

  const handleScan = async () => {
    await hapticService.triggerImpactFeedback('medium');
    setIsScanning(true);
    
    // Simulate scanning delay
    setTimeout(async () => {
      setIsScanning(false);
      setShowCamera(false);
      
      // Send scan result notification
      await notificationService.sendScanResultNotification('Monarch Butterfly', 96);
      await hapticService.triggerNotificationFeedback('success');
      
      router.push('/scan-result');
    }, 2000);
  };

  const startCamera = async () => {
    await hapticService.triggerSelectionFeedback();
    
    if (!permission?.granted) {
      const result = await requestPermission();
      if (!result.granted) {
        Alert.alert('Permission required', 'Camera access is needed to scan specimens.');
        return;
      }
    }
    setShowCamera(true);
  };

  const stats = [
    { label: 'Species Found', value: 127, icon: Leaf, color: '#22C55E' },
    { label: 'Day Streak', value: 7, icon: Zap, color: '#F59E0B' },
    { label: 'Achievements', value: 12, icon: Sparkles, color: '#3B82F6' },
  ];

  if (showCamera) {
    return (
      <View style={styles.cameraContainer}>
        <CameraView style={styles.camera} facing={facing}>
          {/* Camera Overlay */}
          <View style={styles.cameraOverlay}>
            <View style={styles.scanFrame}>
              <View style={[styles.corner, styles.topLeft]} />
              <View style={[styles.corner, styles.topRight]} />
              <View style={[styles.corner, styles.bottomLeft]} />
              <View style={[styles.corner, styles.bottomRight]} />
              
              {isScanning && (
                <View style={styles.scanningLine}>
                  <LinearGradient
                    colors={['transparent', '#22C55E', 'transparent']}
                    style={styles.scanLine}
                    start={{ x: 0, y: 0 }}
                    end={{ x: 1, y: 0 }}
                  />
                </View>
              )}
            </View>
            
            <View style={styles.cameraControls}>
              <TouchableOpacity
                style={styles.cancelButton}
                onPress={async () => {
                  await hapticService.triggerSelectionFeedback();
                  setShowCamera(false);
                }}>
                <Text style={styles.cancelButtonText}>Cancel</Text>
              </TouchableOpacity>
              
              <TouchableOpacity
                style={[styles.scanButton, isScanning && styles.scanButtonActive]}
                onPress={handleScan}
                disabled={isScanning}>
                {isScanning ? (
                  <Sparkles size={32} color="#FFFFFF" />
                ) : (
                  <Target size={32} color="#FFFFFF" />
                )}
              </TouchableOpacity>
              
              <TouchableOpacity
                style={styles.flipButton}
                onPress={async () => {
                  await hapticService.triggerSelectionFeedback();
                  setFacing(current => (current === 'back' ? 'front' : 'back'));
                }}>
                <Text style={styles.flipButtonText}>Flip</Text>
              </TouchableOpacity>
            </View>
          </View>
        </CameraView>
      </View>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <LinearGradient
        colors={['#F0FDF4', '#ECFDF5', '#D1FAE5']}
        style={styles.gradient}>
        <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
          {/* Header */}
          <View style={styles.header}>
            <Text style={styles.title}>BioScan</Text>
            <Text style={styles.subtitle}>Discover nature around you</Text>
          </View>

          {/* Quick Stats */}
          <View style={styles.statsContainer}>
            {stats.map((stat, index) => {
              const IconComponent = stat.icon;
              return (
                <View key={index} style={styles.statCard}>
                  <IconComponent size={24} color={stat.color} />
                  <Text style={styles.statNumber}>{stat.value}</Text>
                  <Text style={styles.statLabel}>{stat.label}</Text>
                </View>
              );
            })}
          </View>

          {/* Scan Options */}
          <View style={styles.scanSection}>
            <Text style={styles.sectionTitle}>Start Scanning</Text>
            
            <TouchableOpacity style={styles.primaryScanButton} onPress={startCamera}>
              <LinearGradient
                colors={['#22C55E', '#16A34A']}
                style={styles.gradientButton}>
                <Camera size={28} color="#FFFFFF" />
                <Text style={styles.primaryButtonText}>Scan with Camera</Text>
                <ChevronRight size={20} color="#FFFFFF" />
              </LinearGradient>
            </TouchableOpacity>

            <View style={styles.secondaryOptions}>
              <TouchableOpacity 
                style={styles.secondaryButton}
                onPress={async () => {
                  await hapticService.triggerSelectionFeedback();
                  Alert.alert('Upload Photo', 'Photo upload feature would be implemented here');
                }}>
                <ImageIcon size={24} color="#6B7280" />
                <Text style={styles.secondaryButtonText}>Upload Photo</Text>
              </TouchableOpacity>
              
              <TouchableOpacity 
                style={styles.secondaryButton}
                onPress={async () => {
                  await hapticService.triggerSelectionFeedback();
                  Alert.alert('Voice Description', 'Voice description feature would be implemented here');
                }}>
                <Mic size={24} color="#6B7280" />
                <Text style={styles.secondaryButtonText}>Describe</Text>
              </TouchableOpacity>
            </View>
          </View>

          {/* Getting Started Tips */}
          <View style={styles.tipsSection}>
            <Text style={styles.sectionTitle}>Scanning Tips</Text>
            
            <View style={styles.tipCard}>
              <View style={styles.tipIcon}>
                <Target size={20} color="#22C55E" />
              </View>
              <View style={styles.tipContent}>
                <Text style={styles.tipTitle}>Get Close & Clear</Text>
                <Text style={styles.tipDescription}>
                  Position your camera 6-12 inches from the subject for best results
                </Text>
              </View>
            </View>

            <View style={styles.tipCard}>
              <View style={styles.tipIcon}>
                <Sparkles size={20} color="#F59E0B" />
              </View>
              <View style={styles.tipContent}>
                <Text style={styles.tipTitle}>Good Lighting</Text>
                <Text style={styles.tipDescription}>
                  Natural daylight works best for accurate identification
                </Text>
              </View>
            </View>

            <View style={styles.tipCard}>
              <View style={styles.tipIcon}>
                <Leaf size={20} color="#3B82F6" />
              </View>
              <View style={styles.tipContent}>
                <Text style={styles.tipTitle}>Show Key Features</Text>
                <Text style={styles.tipDescription}>
                  Include distinctive parts like leaves, flowers, or markings
                </Text>
              </View>
            </View>
          </View>

          {/* Bottom Spacing for Tab Bar */}
          <View style={styles.bottomSpacing} />
        </ScrollView>
      </LinearGradient>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F0FDF4',
  },
  gradient: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 10,
  },
  title: {
    fontSize: 32,
    fontFamily: 'Inter-Bold',
    color: '#065F46',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
  },
  statsContainer: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    marginBottom: 30,
    gap: 12,
  },
  statCard: {
    flex: 1,
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 16,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  statNumber: {
    fontSize: 24,
    fontFamily: 'Inter-Bold',
    color: '#111827',
    marginTop: 8,
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
    textAlign: 'center',
  },
  scanSection: {
    paddingHorizontal: 20,
    marginBottom: 30,
  },
  sectionTitle: {
    fontSize: 20,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    marginBottom: 16,
  },
  primaryScanButton: {
    marginBottom: 16,
    borderRadius: 20,
    overflow: 'hidden',
    shadowColor: '#22C55E',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 12,
    elevation: 4,
  },
  gradientButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 18,
    paddingHorizontal: 24,
    gap: 12,
  },
  primaryButtonText: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: '#FFFFFF',
    flex: 1,
    textAlign: 'center',
  },
  secondaryOptions: {
    flexDirection: 'row',
    gap: 12,
  },
  secondaryButton: {
    flex: 1,
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 16,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  secondaryButtonText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
    marginTop: 8,
  },
  tipsSection: {
    paddingHorizontal: 20,
    marginBottom: 20,
  },
  tipCard: {
    flexDirection: 'row',
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  tipIcon: {
    width: 40,
    height: 40,
    borderRadius: 10,
    backgroundColor: '#F0FDF4',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  tipContent: {
    flex: 1,
  },
  tipTitle: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    marginBottom: 4,
  },
  tipDescription: {
    fontSize: 13,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    lineHeight: 18,
  },
  bottomSpacing: {
    height: 100,
  },
  // Camera Styles
  cameraContainer: {
    flex: 1,
  },
  camera: {
    flex: 1,
  },
  cameraOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    justifyContent: 'space-between',
    paddingTop: 60,
    paddingBottom: 40,
  },
  scanFrame: {
    alignSelf: 'center',
    width: width * 0.8,
    height: width * 0.8,
    position: 'relative',
  },
  corner: {
    position: 'absolute',
    width: 40,
    height: 40,
    borderColor: '#22C55E',
    borderWidth: 3,
  },
  topLeft: {
    top: 0,
    left: 0,
    borderRightWidth: 0,
    borderBottomWidth: 0,
    borderTopLeftRadius: 8,
  },
  topRight: {
    top: 0,
    right: 0,
    borderLeftWidth: 0,
    borderBottomWidth: 0,
    borderTopRightRadius: 8,
  },
  bottomLeft: {
    bottom: 0,
    left: 0,
    borderRightWidth: 0,
    borderTopWidth: 0,
    borderBottomLeftRadius: 8,
  },
  bottomRight: {
    bottom: 0,
    right: 0,
    borderLeftWidth: 0,
    borderTopWidth: 0,
    borderBottomRightRadius: 8,
  },
  scanningLine: {
    position: 'absolute',
    top: '50%',
    left: 0,
    right: 0,
    height: 2,
  },
  scanLine: {
    flex: 1,
    height: '100%',
  },
  cameraControls: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  cancelButton: {
    paddingVertical: 12,
    paddingHorizontal: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 20,
  },
  cancelButtonText: {
    color: '#FFFFFF',
    fontFamily: 'Inter-Medium',
    fontSize: 16,
  },
  scanButton: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#22C55E',
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#22C55E',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.4,
    shadowRadius: 12,
    elevation: 8,
  },
  scanButtonActive: {
    backgroundColor: '#16A34A',
  },
  flipButton: {
    paddingVertical: 12,
    paddingHorizontal: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 20,
  },
  flipButtonText: {
    color: '#FFFFFF',
    fontFamily: 'Inter-Medium',
    fontSize: 16,
  },
});