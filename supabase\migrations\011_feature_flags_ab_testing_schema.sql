-- Feature Flags & A/B Testing Schema
-- This migration creates comprehensive feature flag and A/B testing infrastructure

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Feature Flags Table
CREATE TABLE IF NOT EXISTS feature_flags (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Flag identification
    flag_key VARCHAR(100) NOT NULL UNIQUE,
    flag_name VARCHAR(255) NOT NULL,
    description TEXT,
    
    -- Flag configuration
    flag_type VARCHAR(20) NOT NULL DEFAULT 'boolean' CHECK (flag_type IN ('boolean', 'string', 'number', 'json')),
    default_value JSONB NOT NULL DEFAULT 'false',
    
    -- Targeting and rollout
    is_enabled BOOLEAN NOT NULL DEFAULT false,
    rollout_percentage DECIMAL(5,2) DEFAULT 0 CHECK (rollout_percentage >= 0 AND rollout_percentage <= 100),
    
    -- Environment and context
    environment VARCHAR(20) NOT NULL DEFAULT 'development' CHECK (environment IN ('development', 'staging', 'production')),
    platform VARCHAR(20) DEFAULT 'all' CHECK (platform IN ('all', 'ios', 'android', 'web')),
    
    -- Targeting rules
    targeting_rules JSONB DEFAULT '{}',
    user_segments JSONB DEFAULT '[]',
    
    -- Metadata
    tags JSONB DEFAULT '[]',
    owner_id UUID REFERENCES auth.users(id),
    team VARCHAR(100),
    
    -- Lifecycle
    status VARCHAR(20) NOT NULL DEFAULT 'draft' CHECK (status IN ('draft', 'active', 'archived', 'deprecated')),
    start_date TIMESTAMP WITH TIME ZONE,
    end_date TIMESTAMP WITH TIME ZONE,
    
    -- Audit
    created_by UUID REFERENCES auth.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- A/B Testing Experiments Table
CREATE TABLE IF NOT EXISTS ab_experiments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Experiment identification
    experiment_key VARCHAR(100) NOT NULL UNIQUE,
    experiment_name VARCHAR(255) NOT NULL,
    description TEXT,
    hypothesis TEXT,
    
    -- Experiment configuration
    experiment_type VARCHAR(20) NOT NULL DEFAULT 'ab_test' CHECK (experiment_type IN ('ab_test', 'multivariate', 'feature_flag')),
    status VARCHAR(20) NOT NULL DEFAULT 'draft' CHECK (status IN ('draft', 'running', 'paused', 'completed', 'archived')),
    
    -- Traffic allocation
    traffic_allocation DECIMAL(5,2) DEFAULT 100 CHECK (traffic_allocation >= 0 AND traffic_allocation <= 100),
    
    -- Targeting
    targeting_rules JSONB DEFAULT '{}',
    audience_segments JSONB DEFAULT '[]',
    
    -- Environment and platform
    environment VARCHAR(20) NOT NULL DEFAULT 'development',
    platform VARCHAR(20) DEFAULT 'all',
    
    -- Timeline
    start_date TIMESTAMP WITH TIME ZONE,
    end_date TIMESTAMP WITH TIME ZONE,
    duration_days INTEGER,
    
    -- Statistical configuration
    confidence_level DECIMAL(3,2) DEFAULT 0.95,
    minimum_sample_size INTEGER DEFAULT 1000,
    statistical_significance_threshold DECIMAL(3,2) DEFAULT 0.05,
    
    -- Success metrics
    primary_metric VARCHAR(100),
    secondary_metrics JSONB DEFAULT '[]',
    conversion_goals JSONB DEFAULT '[]',
    
    -- Results
    results JSONB DEFAULT '{}',
    winner_variant VARCHAR(100),
    statistical_significance DECIMAL(5,4),
    
    -- Metadata
    tags JSONB DEFAULT '[]',
    owner_id UUID REFERENCES auth.users(id),
    team VARCHAR(100),
    
    -- Audit
    created_by UUID REFERENCES auth.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Experiment Variants Table
CREATE TABLE IF NOT EXISTS experiment_variants (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    experiment_id UUID NOT NULL REFERENCES ab_experiments(id) ON DELETE CASCADE,
    
    -- Variant identification
    variant_key VARCHAR(100) NOT NULL,
    variant_name VARCHAR(255) NOT NULL,
    description TEXT,
    
    -- Variant configuration
    is_control BOOLEAN NOT NULL DEFAULT false,
    traffic_weight DECIMAL(5,2) NOT NULL DEFAULT 50 CHECK (traffic_weight >= 0 AND traffic_weight <= 100),
    
    -- Variant parameters
    parameters JSONB DEFAULT '{}',
    feature_flags JSONB DEFAULT '{}',
    
    -- Results tracking
    exposure_count INTEGER DEFAULT 0,
    conversion_count INTEGER DEFAULT 0,
    conversion_rate DECIMAL(5,4) DEFAULT 0,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(experiment_id, variant_key)
);

-- User Feature Flag Assignments Table
CREATE TABLE IF NOT EXISTS user_feature_assignments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    feature_flag_id UUID NOT NULL REFERENCES feature_flags(id) ON DELETE CASCADE,
    
    -- Assignment details
    assigned_value JSONB NOT NULL,
    assignment_reason VARCHAR(50) NOT NULL CHECK (assignment_reason IN ('rollout', 'targeting', 'override', 'experiment')),
    
    -- Context
    assignment_context JSONB DEFAULT '{}',
    user_segment VARCHAR(100),
    
    -- Metadata
    assigned_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE,
    
    UNIQUE(user_id, feature_flag_id)
);

-- User Experiment Assignments Table
CREATE TABLE IF NOT EXISTS user_experiment_assignments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    experiment_id UUID NOT NULL REFERENCES ab_experiments(id) ON DELETE CASCADE,
    variant_id UUID NOT NULL REFERENCES experiment_variants(id) ON DELETE CASCADE,
    
    -- Assignment details
    assignment_method VARCHAR(30) NOT NULL DEFAULT 'random' CHECK (assignment_method IN ('random', 'deterministic', 'manual')),
    assignment_hash VARCHAR(64),
    
    -- Context
    assignment_context JSONB DEFAULT '{}',
    user_segment VARCHAR(100),
    
    -- Tracking
    first_exposure_at TIMESTAMP WITH TIME ZONE,
    last_exposure_at TIMESTAMP WITH TIME ZONE,
    exposure_count INTEGER DEFAULT 0,
    
    -- Conversion tracking
    has_converted BOOLEAN DEFAULT false,
    conversion_events JSONB DEFAULT '[]',
    conversion_value DECIMAL(10,2) DEFAULT 0,
    
    assigned_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(user_id, experiment_id)
);

-- Feature Flag Events Table (for analytics)
CREATE TABLE IF NOT EXISTS feature_flag_events (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Event identification
    event_type VARCHAR(30) NOT NULL CHECK (event_type IN ('flag_evaluation', 'flag_exposure', 'flag_override')),
    
    -- References
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    feature_flag_id UUID REFERENCES feature_flags(id) ON DELETE CASCADE,
    experiment_id UUID REFERENCES ab_experiments(id) ON DELETE CASCADE,
    
    -- Event data
    flag_key VARCHAR(100) NOT NULL,
    flag_value JSONB,
    previous_value JSONB,
    
    -- Context
    user_context JSONB DEFAULT '{}',
    device_context JSONB DEFAULT '{}',
    session_id VARCHAR(255),
    
    -- Metadata
    platform VARCHAR(20),
    app_version VARCHAR(50),
    sdk_version VARCHAR(50),
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Experiment Events Table (for analytics)
CREATE TABLE IF NOT EXISTS experiment_events (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Event identification
    event_type VARCHAR(30) NOT NULL CHECK (event_type IN ('exposure', 'conversion', 'goal_completion')),
    
    -- References
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    experiment_id UUID NOT NULL REFERENCES ab_experiments(id) ON DELETE CASCADE,
    variant_id UUID NOT NULL REFERENCES experiment_variants(id) ON DELETE CASCADE,
    
    -- Event data
    event_name VARCHAR(100),
    event_properties JSONB DEFAULT '{}',
    conversion_value DECIMAL(10,2) DEFAULT 0,
    
    -- Context
    user_context JSONB DEFAULT '{}',
    session_id VARCHAR(255),
    
    -- Metadata
    platform VARCHAR(20),
    app_version VARCHAR(50),
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Feature Flag Overrides Table (for testing and debugging)
CREATE TABLE IF NOT EXISTS feature_flag_overrides (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Override target
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    feature_flag_id UUID NOT NULL REFERENCES feature_flags(id) ON DELETE CASCADE,
    
    -- Override configuration
    override_value JSONB NOT NULL,
    override_reason TEXT,
    
    -- Scope
    environment VARCHAR(20),
    platform VARCHAR(20),
    
    -- Lifecycle
    is_active BOOLEAN NOT NULL DEFAULT true,
    expires_at TIMESTAMP WITH TIME ZONE,
    
    -- Audit
    created_by UUID REFERENCES auth.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(user_id, feature_flag_id, environment, platform)
);

-- Experiment Segments Table (for audience targeting)
CREATE TABLE IF NOT EXISTS experiment_segments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Segment identification
    segment_key VARCHAR(100) NOT NULL UNIQUE,
    segment_name VARCHAR(255) NOT NULL,
    description TEXT,
    
    -- Segment rules
    targeting_rules JSONB NOT NULL DEFAULT '{}',
    
    -- Metadata
    is_active BOOLEAN NOT NULL DEFAULT true,
    created_by UUID REFERENCES auth.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_feature_flags_key ON feature_flags(flag_key);
CREATE INDEX IF NOT EXISTS idx_feature_flags_environment ON feature_flags(environment);
CREATE INDEX IF NOT EXISTS idx_feature_flags_status ON feature_flags(status);
CREATE INDEX IF NOT EXISTS idx_feature_flags_enabled ON feature_flags(is_enabled);

CREATE INDEX IF NOT EXISTS idx_ab_experiments_key ON ab_experiments(experiment_key);
CREATE INDEX IF NOT EXISTS idx_ab_experiments_status ON ab_experiments(status);
CREATE INDEX IF NOT EXISTS idx_ab_experiments_environment ON ab_experiments(environment);

CREATE INDEX IF NOT EXISTS idx_experiment_variants_experiment_id ON experiment_variants(experiment_id);
CREATE INDEX IF NOT EXISTS idx_experiment_variants_key ON experiment_variants(variant_key);

CREATE INDEX IF NOT EXISTS idx_user_feature_assignments_user_id ON user_feature_assignments(user_id);
CREATE INDEX IF NOT EXISTS idx_user_feature_assignments_flag_id ON user_feature_assignments(feature_flag_id);

CREATE INDEX IF NOT EXISTS idx_user_experiment_assignments_user_id ON user_experiment_assignments(user_id);
CREATE INDEX IF NOT EXISTS idx_user_experiment_assignments_experiment_id ON user_experiment_assignments(experiment_id);

CREATE INDEX IF NOT EXISTS idx_feature_flag_events_user_id ON feature_flag_events(user_id);
CREATE INDEX IF NOT EXISTS idx_feature_flag_events_flag_id ON feature_flag_events(feature_flag_id);
CREATE INDEX IF NOT EXISTS idx_feature_flag_events_created_at ON feature_flag_events(created_at);

CREATE INDEX IF NOT EXISTS idx_experiment_events_user_id ON experiment_events(user_id);
CREATE INDEX IF NOT EXISTS idx_experiment_events_experiment_id ON experiment_events(experiment_id);
CREATE INDEX IF NOT EXISTS idx_experiment_events_created_at ON experiment_events(created_at);

CREATE INDEX IF NOT EXISTS idx_feature_flag_overrides_user_id ON feature_flag_overrides(user_id);
CREATE INDEX IF NOT EXISTS idx_feature_flag_overrides_flag_id ON feature_flag_overrides(feature_flag_id);

-- Create updated_at triggers
CREATE TRIGGER update_feature_flags_updated_at BEFORE UPDATE ON feature_flags FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_ab_experiments_updated_at BEFORE UPDATE ON ab_experiments FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_experiment_variants_updated_at BEFORE UPDATE ON experiment_variants FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_experiment_segments_updated_at BEFORE UPDATE ON experiment_segments FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Enable RLS on all feature flag and A/B testing tables
ALTER TABLE feature_flags ENABLE ROW LEVEL SECURITY;
ALTER TABLE ab_experiments ENABLE ROW LEVEL SECURITY;
ALTER TABLE experiment_variants ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_feature_assignments ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_experiment_assignments ENABLE ROW LEVEL SECURITY;
ALTER TABLE feature_flag_events ENABLE ROW LEVEL SECURITY;
ALTER TABLE experiment_events ENABLE ROW LEVEL SECURITY;
ALTER TABLE feature_flag_overrides ENABLE ROW LEVEL SECURITY;
ALTER TABLE experiment_segments ENABLE ROW LEVEL SECURITY;

-- Feature Flags Policies
CREATE POLICY "Service role can manage feature flags" ON feature_flags
    FOR ALL USING (auth.role() = 'service_role');

CREATE POLICY "Admins can manage feature flags" ON feature_flags
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM user_profiles
            WHERE id = auth.uid()
            AND role IN ('admin', 'super_admin', 'developer')
        )
    );

CREATE POLICY "Users can view active feature flags" ON feature_flags
    FOR SELECT USING (
        status = 'active'
        AND is_enabled = true
        AND (start_date IS NULL OR start_date <= NOW())
        AND (end_date IS NULL OR end_date >= NOW())
    );

-- A/B Experiments Policies
CREATE POLICY "Service role can manage experiments" ON ab_experiments
    FOR ALL USING (auth.role() = 'service_role');

CREATE POLICY "Admins can manage experiments" ON ab_experiments
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM user_profiles
            WHERE id = auth.uid()
            AND role IN ('admin', 'super_admin', 'developer')
        )
    );

CREATE POLICY "Users can view running experiments" ON ab_experiments
    FOR SELECT USING (status = 'running');

-- Experiment Variants Policies
CREATE POLICY "Service role can manage variants" ON experiment_variants
    FOR ALL USING (auth.role() = 'service_role');

CREATE POLICY "Admins can manage variants" ON experiment_variants
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM user_profiles
            WHERE id = auth.uid()
            AND role IN ('admin', 'super_admin', 'developer')
        )
    );

CREATE POLICY "Users can view variants of running experiments" ON experiment_variants
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM ab_experiments
            WHERE id = experiment_variants.experiment_id
            AND status = 'running'
        )
    );

-- User Feature Assignments Policies
CREATE POLICY "Users can view own feature assignments" ON user_feature_assignments
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Service role can manage feature assignments" ON user_feature_assignments
    FOR ALL USING (auth.role() = 'service_role');

CREATE POLICY "Admins can view feature assignments" ON user_feature_assignments
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM user_profiles
            WHERE id = auth.uid()
            AND role IN ('admin', 'super_admin', 'developer')
        )
    );

-- User Experiment Assignments Policies
CREATE POLICY "Users can view own experiment assignments" ON user_experiment_assignments
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Service role can manage experiment assignments" ON user_experiment_assignments
    FOR ALL USING (auth.role() = 'service_role');

CREATE POLICY "Admins can view experiment assignments" ON user_experiment_assignments
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM user_profiles
            WHERE id = auth.uid()
            AND role IN ('admin', 'super_admin', 'developer')
        )
    );

-- Feature Flag Events Policies
CREATE POLICY "Users can view own flag events" ON feature_flag_events
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Service role can manage flag events" ON feature_flag_events
    FOR ALL USING (auth.role() = 'service_role');

CREATE POLICY "Admins can view flag events" ON feature_flag_events
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM user_profiles
            WHERE id = auth.uid()
            AND role IN ('admin', 'super_admin', 'developer')
        )
    );

-- Experiment Events Policies
CREATE POLICY "Users can view own experiment events" ON experiment_events
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Service role can manage experiment events" ON experiment_events
    FOR ALL USING (auth.role() = 'service_role');

CREATE POLICY "Admins can view experiment events" ON experiment_events
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM user_profiles
            WHERE id = auth.uid()
            AND role IN ('admin', 'super_admin', 'developer')
        )
    );

-- Feature Flag Overrides Policies
CREATE POLICY "Users can view own overrides" ON feature_flag_overrides
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Service role can manage overrides" ON feature_flag_overrides
    FOR ALL USING (auth.role() = 'service_role');

CREATE POLICY "Admins can manage overrides" ON feature_flag_overrides
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM user_profiles
            WHERE id = auth.uid()
            AND role IN ('admin', 'super_admin', 'developer')
        )
    );

-- Experiment Segments Policies
CREATE POLICY "Service role can manage segments" ON experiment_segments
    FOR ALL USING (auth.role() = 'service_role');

CREATE POLICY "Admins can manage segments" ON experiment_segments
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM user_profiles
            WHERE id = auth.uid()
            AND role IN ('admin', 'super_admin', 'developer')
        )
    );

CREATE POLICY "Users can view active segments" ON experiment_segments
    FOR SELECT USING (is_active = true);

-- Create SQL functions for feature flags and A/B testing

-- Function to evaluate feature flag for user
CREATE OR REPLACE FUNCTION evaluate_feature_flag(
    flag_key_param VARCHAR,
    user_id_param UUID,
    user_context_param JSONB DEFAULT '{}'
)
RETURNS TABLE (
    flag_key VARCHAR,
    flag_value JSONB,
    assignment_reason VARCHAR,
    experiment_id UUID,
    variant_key VARCHAR
) AS $$
DECLARE
    flag_record RECORD;
    user_hash INTEGER;
    override_record RECORD;
BEGIN
    -- Get feature flag
    SELECT * INTO flag_record
    FROM feature_flags
    WHERE flag_key = flag_key_param
    AND status = 'active'
    AND is_enabled = true
    AND (start_date IS NULL OR start_date <= NOW())
    AND (end_date IS NULL OR end_date >= NOW());

    -- Return default if flag not found or disabled
    IF flag_record IS NULL THEN
        RETURN QUERY SELECT
            flag_key_param,
            'false'::JSONB,
            'flag_not_found'::VARCHAR,
            NULL::UUID,
            NULL::VARCHAR;
        RETURN;
    END IF;

    -- Check for user override
    SELECT * INTO override_record
    FROM feature_flag_overrides
    WHERE user_id = user_id_param
    AND feature_flag_id = flag_record.id
    AND is_active = true
    AND (expires_at IS NULL OR expires_at > NOW());

    IF override_record IS NOT NULL THEN
        RETURN QUERY SELECT
            flag_key_param,
            override_record.override_value,
            'override'::VARCHAR,
            NULL::UUID,
            NULL::VARCHAR;
        RETURN;
    END IF;

    -- Check rollout percentage
    user_hash := ABS(HASHTEXT(user_id_param::TEXT || ':' || flag_key_param)) % 100;

    IF user_hash >= flag_record.rollout_percentage THEN
        RETURN QUERY SELECT
            flag_key_param,
            CASE
                WHEN flag_record.flag_type = 'boolean' THEN 'false'::JSONB
                WHEN flag_record.flag_type = 'string' THEN '""'::JSONB
                WHEN flag_record.flag_type = 'number' THEN '0'::JSONB
                ELSE '{}'::JSONB
            END,
            'rollout_excluded'::VARCHAR,
            NULL::UUID,
            NULL::VARCHAR;
        RETURN;
    END IF;

    -- Return flag value
    RETURN QUERY SELECT
        flag_key_param,
        flag_record.default_value,
        'rollout_included'::VARCHAR,
        NULL::UUID,
        NULL::VARCHAR;
END;
$$ LANGUAGE plpgsql;

-- Function to assign user to experiment variant
CREATE OR REPLACE FUNCTION assign_experiment_variant(
    experiment_key_param VARCHAR,
    user_id_param UUID,
    user_context_param JSONB DEFAULT '{}'
)
RETURNS TABLE (
    experiment_id UUID,
    variant_id UUID,
    variant_key VARCHAR,
    assignment_hash VARCHAR
) AS $$
DECLARE
    experiment_record RECORD;
    variant_record RECORD;
    user_hash INTEGER;
    total_weight INTEGER;
    current_weight INTEGER;
    random_value INTEGER;
BEGIN
    -- Get experiment
    SELECT * INTO experiment_record
    FROM ab_experiments
    WHERE experiment_key = experiment_key_param
    AND status = 'running'
    AND (start_date IS NULL OR start_date <= NOW())
    AND (end_date IS NULL OR end_date >= NOW());

    -- Return null if experiment not found or not running
    IF experiment_record IS NULL THEN
        RETURN;
    END IF;

    -- Check traffic allocation
    user_hash := ABS(HASHTEXT(user_id_param::TEXT || ':' || experiment_key_param)) % 100;

    IF user_hash >= experiment_record.traffic_allocation THEN
        RETURN;
    END IF;

    -- Get total weight of active variants
    SELECT SUM(traffic_weight) INTO total_weight
    FROM experiment_variants
    WHERE experiment_id = experiment_record.id;

    IF total_weight IS NULL OR total_weight = 0 THEN
        RETURN;
    END IF;

    -- Generate deterministic random value for variant selection
    random_value := ABS(HASHTEXT(user_id_param::TEXT || ':' || experiment_key_param || ':variant')) % total_weight;

    -- Select variant based on weights
    current_weight := 0;
    FOR variant_record IN
        SELECT * FROM experiment_variants
        WHERE experiment_id = experiment_record.id
        ORDER BY traffic_weight DESC
    LOOP
        current_weight := current_weight + variant_record.traffic_weight;
        IF random_value < current_weight THEN
            RETURN QUERY SELECT
                experiment_record.id,
                variant_record.id,
                variant_record.variant_key,
                user_hash::VARCHAR;
            RETURN;
        END IF;
    END LOOP;
END;
$$ LANGUAGE plpgsql;

-- Function to track feature flag event
CREATE OR REPLACE FUNCTION track_feature_flag_event(
    event_type_param VARCHAR,
    user_id_param UUID,
    flag_key_param VARCHAR,
    flag_value_param JSONB,
    user_context_param JSONB DEFAULT '{}',
    device_context_param JSONB DEFAULT '{}'
)
RETURNS BOOLEAN AS $$
DECLARE
    flag_id_var UUID;
BEGIN
    -- Get flag ID
    SELECT id INTO flag_id_var
    FROM feature_flags
    WHERE flag_key = flag_key_param;

    -- Insert event
    INSERT INTO feature_flag_events (
        event_type,
        user_id,
        feature_flag_id,
        flag_key,
        flag_value,
        user_context,
        device_context
    ) VALUES (
        event_type_param,
        user_id_param,
        flag_id_var,
        flag_key_param,
        flag_value_param,
        user_context_param,
        device_context_param
    );

    RETURN TRUE;
EXCEPTION
    WHEN OTHERS THEN
        RETURN FALSE;
END;
$$ LANGUAGE plpgsql;
