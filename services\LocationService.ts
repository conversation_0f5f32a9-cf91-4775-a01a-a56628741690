import * as Location from 'expo-location';
import { supabase } from '../lib/supabase';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { AdvancedPrivacyService } from './AdvancedPrivacyService';

export interface LocationData {
  latitude: number;
  longitude: number;
  altitude?: number;
  accuracy?: number;
  heading?: number;
  speed?: number;
  timestamp?: number;
}

export interface UserLocation {
  id: string;
  latitude: number;
  longitude: number;
  altitude?: number;
  accuracy?: number;
  location_type: 'gps' | 'manual' | 'network' | 'passive';
  location_source: 'user' | 'identification' | 'exploration' | 'background';
  address_line1?: string;
  city?: string;
  state_province?: string;
  country?: string;
  is_public: boolean;
  precision_level: 'exact' | 'approximate' | 'city' | 'region' | 'country';
  recorded_at: string;
  created_at: string;
}

export interface LocationAlert {
  id: string;
  latitude: number;
  longitude: number;
  radius_meters: number;
  alert_type: 'species_sighting' | 'rare_species' | 'new_discovery' | 'seasonal_activity' | 'migration';
  alert_name: string;
  alert_description?: string;
  is_active: boolean;
  push_notifications: boolean;
  email_notifications: boolean;
  max_alerts_per_day: number;
}

export interface LocationRecommendation {
  id: string;
  latitude: number;
  longitude: number;
  recommendation_type: 'exploration' | 'species_hunting' | 'photography' | 'research' | 'education';
  title: string;
  description?: string;
  relevance_score: number;
  difficulty_level: 'easy' | 'moderate' | 'challenging' | 'expert';
  expected_species: any[];
  best_time_to_visit: any;
  accessibility_info?: string;
  estimated_duration?: string;
}

export interface NearbySpecies {
  species_id: string;
  species_name: string;
  common_name: string;
  distance_meters: number;
  last_sighting_date: string;
  confidence_level: number;
  sighting_count: number;
}

export class LocationService {
  private static instance: LocationService;
  private currentLocation: LocationData | null = null;
  private locationWatcher: Location.LocationSubscription | null = null;
  private privacyService: AdvancedPrivacyService;
  private isTracking = false;

  static getInstance(): LocationService {
    if (!LocationService.instance) {
      LocationService.instance = new LocationService();
    }
    return LocationService.instance;
  }

  constructor() {
    this.privacyService = AdvancedPrivacyService.getInstance();
  }

  /**
   * Initialize location services
   */
  async initialize(): Promise<boolean> {
    try {
      // Check privacy consent for location tracking
      const hasConsent = await this.privacyService.hasValidConsent('location_tracking');
      if (!hasConsent) {
        console.log('Location tracking consent not granted');
        return false;
      }

      // Request location permissions
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status !== 'granted') {
        console.log('Location permission not granted');
        return false;
      }

      // Get current location
      await this.getCurrentLocation();
      
      console.log('Location services initialized successfully');
      return true;
    } catch (error) {
      console.error('Error initializing location services:', error);
      return false;
    }
  }

  /**
   * Get current user location
   */
  async getCurrentLocation(): Promise<LocationData | null> {
    try {
      const { status } = await Location.getForegroundPermissionsAsync();
      if (status !== 'granted') {
        throw new Error('Location permission not granted');
      }

      const location = await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.High,
        maximumAge: 10000, // 10 seconds
      });

      const locationData: LocationData = {
        latitude: location.coords.latitude,
        longitude: location.coords.longitude,
        altitude: location.coords.altitude || undefined,
        accuracy: location.coords.accuracy || undefined,
        heading: location.coords.heading || undefined,
        speed: location.coords.speed || undefined,
        timestamp: location.timestamp,
      };

      this.currentLocation = locationData;
      return locationData;
    } catch (error) {
      console.error('Error getting current location:', error);
      return null;
    }
  }

  /**
   * Start location tracking
   */
  async startLocationTracking(): Promise<boolean> {
    try {
      if (this.isTracking) {
        console.log('Location tracking already active');
        return true;
      }

      // Check background location permission
      const { status } = await Location.requestBackgroundPermissionsAsync();
      if (status !== 'granted') {
        console.log('Background location permission not granted');
        return false;
      }

      this.locationWatcher = await Location.watchPositionAsync(
        {
          accuracy: Location.Accuracy.Balanced,
          timeInterval: 30000, // 30 seconds
          distanceInterval: 50, // 50 meters
        },
        (location) => {
          this.handleLocationUpdate(location);
        }
      );

      this.isTracking = true;
      console.log('Location tracking started');
      return true;
    } catch (error) {
      console.error('Error starting location tracking:', error);
      return false;
    }
  }

  /**
   * Stop location tracking
   */
  async stopLocationTracking(): Promise<void> {
    try {
      if (this.locationWatcher) {
        this.locationWatcher.remove();
        this.locationWatcher = null;
      }
      this.isTracking = false;
      console.log('Location tracking stopped');
    } catch (error) {
      console.error('Error stopping location tracking:', error);
    }
  }

  /**
   * Save user location to database
   */
  async saveUserLocation(
    location: LocationData,
    source: 'user' | 'identification' | 'exploration' | 'background' = 'user',
    isPublic: boolean = false
  ): Promise<string | null> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      // Apply privacy preferences
      const preferences = await this.privacyService.getAdvancedPrivacyPreferences();
      const precisionLevel = preferences?.location_precision_level || 'exact';
      
      // Adjust location precision based on user preferences
      const adjustedLocation = this.adjustLocationPrecision(location, precisionLevel);

      // Get address information
      const addressInfo = await this.reverseGeocode(adjustedLocation.latitude, adjustedLocation.longitude);

      const { data, error } = await supabase
        .from('user_locations')
        .insert({
          user_id: user.id,
          latitude: adjustedLocation.latitude,
          longitude: adjustedLocation.longitude,
          altitude: adjustedLocation.altitude,
          accuracy: adjustedLocation.accuracy,
          heading: adjustedLocation.heading,
          speed: adjustedLocation.speed,
          location_type: 'gps',
          location_source: source,
          address_line1: addressInfo?.address,
          city: addressInfo?.city,
          state_province: addressInfo?.region,
          country: addressInfo?.country,
          is_public: isPublic,
          precision_level: precisionLevel,
          recorded_at: new Date(location.timestamp || Date.now()).toISOString(),
        })
        .select('id')
        .single();

      if (error) throw error;

      // Log location access
      await this.privacyService.logDataAccess({
        access_type: 'write',
        data_category: 'location_data',
        accessor_type: 'system',
        access_reason: `Location saved from ${source}`,
      });

      return data.id;
    } catch (error) {
      console.error('Error saving user location:', error);
      return null;
    }
  }

  /**
   * Get user's location history
   */
  async getUserLocationHistory(limit: number = 50): Promise<UserLocation[]> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      const { data, error } = await supabase
        .from('user_locations')
        .select('*')
        .eq('user_id', user.id)
        .order('recorded_at', { ascending: false })
        .limit(limit);

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error getting location history:', error);
      return [];
    }
  }

  /**
   * Find nearby species based on current location
   */
  async findNearbySpecies(
    location: LocationData,
    radiusKm: number = 10
  ): Promise<NearbySpecies[]> {
    try {
      const { data, error } = await supabase.rpc('find_nearby_species', {
        user_lat: location.latitude,
        user_lng: location.longitude,
        radius_km: radiusKm,
      });

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error finding nearby species:', error);
      return [];
    }
  }

  /**
   * Create location alert
   */
  async createLocationAlert(alert: Omit<LocationAlert, 'id'>): Promise<string | null> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      const { data, error } = await supabase
        .from('location_alerts')
        .insert({
          user_id: user.id,
          ...alert,
        })
        .select('id')
        .single();

      if (error) throw error;
      return data.id;
    } catch (error) {
      console.error('Error creating location alert:', error);
      return null;
    }
  }

  /**
   * Get user's location alerts
   */
  async getUserLocationAlerts(): Promise<LocationAlert[]> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      const { data, error } = await supabase
        .from('location_alerts')
        .select('*')
        .eq('user_id', user.id)
        .eq('is_active', true)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error getting location alerts:', error);
      return [];
    }
  }

  /**
   * Get location-based recommendations
   */
  async getLocationRecommendations(
    location: LocationData,
    type?: string,
    limit: number = 10
  ): Promise<LocationRecommendation[]> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      let query = supabase
        .from('location_recommendations')
        .select('*')
        .eq('user_id', user.id)
        .order('relevance_score', { ascending: false })
        .limit(limit);

      if (type) {
        query = query.eq('recommendation_type', type);
      }

      const { data, error } = await query;
      if (error) throw error;

      // Calculate distances and sort by relevance + proximity
      const recommendations = (data || []).map(rec => ({
        ...rec,
        distance: this.calculateDistance(
          location.latitude,
          location.longitude,
          rec.latitude,
          rec.longitude
        ),
      }));

      return recommendations.sort((a, b) => {
        const scoreA = a.relevance_score - (a.distance / 1000) * 0.1; // Reduce score by distance
        const scoreB = b.relevance_score - (b.distance / 1000) * 0.1;
        return scoreB - scoreA;
      });
    } catch (error) {
      console.error('Error getting location recommendations:', error);
      return [];
    }
  }

  /**
   * Save identification location
   */
  async saveIdentificationLocation(
    identificationId: string,
    location: LocationData,
    isPublic: boolean = false,
    shareWithCommunity: boolean = false
  ): Promise<string | null> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      // Apply privacy preferences
      const preferences = await this.privacyService.getAdvancedPrivacyPreferences();
      const precisionLevel = preferences?.location_precision_level || 'exact';
      const adjustedLocation = this.adjustLocationPrecision(location, precisionLevel);

      // Get place information
      const placeInfo = await this.reverseGeocode(adjustedLocation.latitude, adjustedLocation.longitude);

      const { data, error } = await supabase
        .from('user_identification_locations')
        .insert({
          user_id: user.id,
          identification_id: identificationId,
          latitude: adjustedLocation.latitude,
          longitude: adjustedLocation.longitude,
          altitude: adjustedLocation.altitude,
          accuracy: adjustedLocation.accuracy,
          address_components: placeInfo,
          place_name: placeInfo?.name,
          place_type: placeInfo?.type,
          is_public: isPublic,
          share_with_community: shareWithCommunity,
          precision_level: precisionLevel,
        })
        .select('id')
        .single();

      if (error) throw error;
      return data.id;
    } catch (error) {
      console.error('Error saving identification location:', error);
      return null;
    }
  }

  /**
   * Check if user is within any active geofences
   */
  async checkGeofences(location: LocationData): Promise<any[]> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return [];

      const { data, error } = await supabase.rpc('check_user_geofences', {
        user_lat: location.latitude,
        user_lng: location.longitude,
        user_uuid: user.id,
      });

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error checking geofences:', error);
      return [];
    }
  }

  // Private helper methods

  private async handleLocationUpdate(location: Location.LocationObject): Promise<void> {
    try {
      const locationData: LocationData = {
        latitude: location.coords.latitude,
        longitude: location.coords.longitude,
        altitude: location.coords.altitude || undefined,
        accuracy: location.coords.accuracy || undefined,
        heading: location.coords.heading || undefined,
        speed: location.coords.speed || undefined,
        timestamp: location.timestamp,
      };

      this.currentLocation = locationData;

      // Save location if significant movement
      const lastLocation = await this.getLastSavedLocation();
      if (!lastLocation || this.calculateDistance(
        lastLocation.latitude,
        lastLocation.longitude,
        locationData.latitude,
        locationData.longitude
      ) > 100) { // 100 meters threshold
        await this.saveUserLocation(locationData, 'background');
      }

      // Check geofences
      const triggeredGeofences = await this.checkGeofences(locationData);
      if (triggeredGeofences.length > 0) {
        await this.handleGeofenceEvents(triggeredGeofences, locationData);
      }

      // Update location analytics
      await this.updateLocationAnalytics(locationData);
    } catch (error) {
      console.error('Error handling location update:', error);
    }
  }

  private adjustLocationPrecision(location: LocationData, level: string): LocationData {
    switch (level) {
      case 'city':
        return {
          ...location,
          latitude: Math.round(location.latitude * 100) / 100, // ~1km precision
          longitude: Math.round(location.longitude * 100) / 100,
        };
      case 'region':
        return {
          ...location,
          latitude: Math.round(location.latitude * 10) / 10, // ~10km precision
          longitude: Math.round(location.longitude * 10) / 10,
        };
      case 'country':
        return {
          ...location,
          latitude: Math.round(location.latitude), // ~100km precision
          longitude: Math.round(location.longitude),
        };
      case 'approximate':
        return {
          ...location,
          latitude: location.latitude + (Math.random() - 0.5) * 0.01, // Add ~500m noise
          longitude: location.longitude + (Math.random() - 0.5) * 0.01,
        };
      default:
        return location;
    }
  }

  private async reverseGeocode(latitude: number, longitude: number): Promise<any> {
    try {
      const result = await Location.reverseGeocodeAsync({ latitude, longitude });
      if (result.length > 0) {
        const place = result[0];
        return {
          address: `${place.street || ''} ${place.streetNumber || ''}`.trim(),
          city: place.city,
          region: place.region,
          country: place.country,
          postalCode: place.postalCode,
          name: place.name,
          type: place.district ? 'district' : 'address',
        };
      }
      return null;
    } catch (error) {
      console.error('Error reverse geocoding:', error);
      return null;
    }
  }

  private calculateDistance(lat1: number, lon1: number, lat2: number, lon2: number): number {
    const R = 6371e3; // Earth's radius in meters
    const φ1 = lat1 * Math.PI / 180;
    const φ2 = lat2 * Math.PI / 180;
    const Δφ = (lat2 - lat1) * Math.PI / 180;
    const Δλ = (lon2 - lon1) * Math.PI / 180;

    const a = Math.sin(Δφ / 2) * Math.sin(Δφ / 2) +
              Math.cos(φ1) * Math.cos(φ2) *
              Math.sin(Δλ / 2) * Math.sin(Δλ / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

    return R * c; // Distance in meters
  }

  private async getLastSavedLocation(): Promise<LocationData | null> {
    try {
      const locations = await this.getUserLocationHistory(1);
      if (locations.length > 0) {
        const loc = locations[0];
        return {
          latitude: loc.latitude,
          longitude: loc.longitude,
          altitude: loc.altitude,
          accuracy: loc.accuracy,
        };
      }
      return null;
    } catch (error) {
      return null;
    }
  }

  private async handleGeofenceEvents(geofences: any[], location: LocationData): Promise<void> {
    // Handle geofence entry/exit events
    for (const geofence of geofences) {
      console.log(`Geofence triggered: ${geofence.name}`);
      // Implement geofence-specific actions
    }
  }

  private async updateLocationAnalytics(location: LocationData): Promise<void> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return;

      const today = new Date().toISOString().split('T')[0];
      
      // Update daily analytics
      await supabase.rpc('update_location_analytics', {
        user_uuid: user.id,
        analytics_date: today,
        new_location: {
          latitude: location.latitude,
          longitude: location.longitude,
        },
      });
    } catch (error) {
      console.error('Error updating location analytics:', error);
    }
  }
}
