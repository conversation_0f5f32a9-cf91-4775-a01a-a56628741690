import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  Dimensions,
  Animated,
  PanResponder,
  Platform,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { X, Target, Camera, Check } from 'lucide-react-native';

const { width, height } = Dimensions.get('window');

interface SettingsPopupProps {
  visible: boolean;
  onClose: () => void;
  confidenceThreshold: number;
  imageQuality: 'low' | 'medium' | 'high';
  onConfidenceChange: (value: number) => void;
  onImageQualityChange: (quality: 'low' | 'medium' | 'high') => void;
  onSave?: () => void;
}

const imageQualityOptions = [
  { value: 'low' as const, label: 'Low', description: 'Faster processing, smaller files' },
  { value: 'medium' as const, label: 'Medium', description: 'Balanced quality and speed' },
  { value: 'high' as const, label: 'High', description: 'Best quality, larger files' },
];

export default function SettingsPopup({
  visible,
  onClose,
  confidenceThreshold,
  imageQuality,
  onConfidenceChange,
  onImageQualityChange,
  onSave,
}: SettingsPopupProps) {
  const [slideAnim] = useState(new Animated.Value(height));
  const [backdropAnim] = useState(new Animated.Value(0));
  const [sliderValue, setSliderValue] = useState(confidenceThreshold);
  const [selectedQuality, setSelectedQuality] = useState(imageQuality);

  // Slider state
  const [sliderWidth, setSliderWidth] = useState(0);
  const [isDragging, setIsDragging] = useState(false);

  useEffect(() => {
    if (visible) {
      Animated.parallel([
        Animated.timing(backdropAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: false,
        }),
        Animated.spring(slideAnim, {
          toValue: 0,
          tension: 100,
          friction: 8,
          useNativeDriver: false,
        }),
      ]).start();
    } else {
      Animated.parallel([
        Animated.timing(backdropAnim, {
          toValue: 0,
          duration: 200,
          useNativeDriver: false,
        }),
        Animated.timing(slideAnim, {
          toValue: height,
          duration: 250,
          useNativeDriver: false,
        }),
      ]).start();
    }
  }, [visible]);

  const handleSliderMove = (gestureState: any) => {
    if (!sliderWidth) return;
    
    const percentage = Math.max(0, Math.min(100, (gestureState.moveX / sliderWidth) * 100));
    setSliderValue(Math.round(percentage));
  };

  const panResponder = PanResponder.create({
    onStartShouldSetPanResponder: () => true,
    onMoveShouldSetPanResponder: () => true,
    onPanResponderGrant: () => {
      setIsDragging(true);
    },
    onPanResponderMove: (evt, gestureState) => {
      handleSliderMove(gestureState);
    },
    onPanResponderRelease: () => {
      setIsDragging(false);
      onConfidenceChange(sliderValue);
    },
  });

  const handleSave = () => {
    onConfidenceChange(sliderValue);
    onImageQualityChange(selectedQuality);
    onSave?.();
    onClose();
  };

  const getConfidenceColor = (value: number) => {
    if (value >= 80) return '#22C55E';
    if (value >= 60) return '#F59E0B';
    return '#EF4444';
  };

  return (
    <Modal
      visible={visible}
      transparent
      animationType="none"
      onRequestClose={onClose}>
      <Animated.View
        style={[
          styles.backdrop,
          {
            opacity: backdropAnim,
          },
        ]}>
        <TouchableOpacity
          style={styles.backdropTouch}
          activeOpacity={1}
          onPress={onClose}
        />
        
        <Animated.View
          style={[
            styles.container,
            {
              transform: [{ translateY: slideAnim }],
            },
          ]}>
          <View style={styles.card}>
            {/* Header */}
            <View style={styles.header}>
              <View style={styles.headerContent}>
                <View style={styles.headerIcon}>
                  <Target size={24} color="#3B82F6" />
                </View>
                <Text style={styles.headerTitle}>Camera Settings</Text>
              </View>
              <TouchableOpacity style={styles.closeButton} onPress={onClose}>
                <X size={20} color="#6B7280" />
              </TouchableOpacity>
            </View>

            {/* Confidence Threshold Section */}
            <View style={styles.section}>
              <View style={styles.sectionHeader}>
                <Text style={styles.sectionTitle}>Confidence Threshold</Text>
                <View style={[styles.valueBadge, { backgroundColor: getConfidenceColor(sliderValue) }]}>
                  <Text style={styles.valueText}>{sliderValue}%</Text>
                </View>
              </View>
              <Text style={styles.sectionDescription}>
                Minimum confidence level required for species identification
              </Text>

              {/* Custom Slider */}
              <View style={styles.sliderContainer}>
                <View style={styles.sliderLabels}>
                  <Text style={styles.sliderLabel}>0%</Text>
                  <Text style={styles.sliderLabel}>50%</Text>
                  <Text style={styles.sliderLabel}>100%</Text>
                </View>
                
                <View
                  style={styles.sliderTrack}
                  onLayout={(event) => setSliderWidth(event.nativeEvent.layout.width)}
                  {...panResponder.panHandlers}>
                  
                  {/* Track Background */}
                  <View style={styles.sliderTrackBackground} />
                  
                  {/* Active Track */}
                  <View
                    style={[
                      styles.sliderTrackActive,
                      {
                        width: `${sliderValue}%`,
                        backgroundColor: getConfidenceColor(sliderValue),
                      },
                    ]}
                  />
                  
                  {/* Thumb */}
                  <Animated.View
                    style={[
                      styles.sliderThumb,
                      {
                        left: `${sliderValue}%`,
                        backgroundColor: getConfidenceColor(sliderValue),
                        transform: [
                          { scale: isDragging ? 1.2 : 1 },
                          { translateX: -12 },
                        ],
                      },
                    ]}>
                    <View style={styles.sliderThumbInner} />
                  </Animated.View>
                </View>

                {/* Confidence Level Indicators */}
                <View style={styles.confidenceIndicators}>
                  <View style={styles.confidenceIndicator}>
                    <View style={[styles.confidenceDot, { backgroundColor: '#EF4444' }]} />
                    <Text style={styles.confidenceLabel}>Low (0-59%)</Text>
                  </View>
                  <View style={styles.confidenceIndicator}>
                    <View style={[styles.confidenceDot, { backgroundColor: '#F59E0B' }]} />
                    <Text style={styles.confidenceLabel}>Medium (60-79%)</Text>
                  </View>
                  <View style={styles.confidenceIndicator}>
                    <View style={[styles.confidenceDot, { backgroundColor: '#22C55E' }]} />
                    <Text style={styles.confidenceLabel}>High (80-100%)</Text>
                  </View>
                </View>
              </View>
            </View>

            {/* Divider */}
            <View style={styles.divider} />

            {/* Image Quality Section */}
            <View style={styles.section}>
              <View style={styles.sectionHeader}>
                <Text style={styles.sectionTitle}>Image Quality</Text>
                <Camera size={20} color="#6B7280" />
              </View>
              <Text style={styles.sectionDescription}>
                Choose the quality level for captured images
              </Text>

              {/* Radio Button Options */}
              <View style={styles.radioGroup}>
                {imageQualityOptions.map((option) => (
                  <TouchableOpacity
                    key={option.value}
                    style={[
                      styles.radioOption,
                      selectedQuality === option.value && styles.radioOptionSelected,
                    ]}
                    onPress={() => setSelectedQuality(option.value)}>
                    
                    <View style={styles.radioContent}>
                      <View style={styles.radioLeft}>
                        <View style={[
                          styles.radioButton,
                          selectedQuality === option.value && styles.radioButtonSelected,
                        ]}>
                          {selectedQuality === option.value && (
                            <Check size={14} color="#FFFFFF" />
                          )}
                        </View>
                        <View style={styles.radioText}>
                          <Text style={[
                            styles.radioLabel,
                            selectedQuality === option.value && styles.radioLabelSelected,
                          ]}>
                            {option.label}
                          </Text>
                          <Text style={[
                            styles.radioDescription,
                            selectedQuality === option.value && styles.radioDescriptionSelected,
                          ]}>
                            {option.description}
                          </Text>
                        </View>
                      </View>
                      
                      {selectedQuality === option.value && (
                        <View style={styles.selectedIndicator}>
                          <LinearGradient
                            colors={['#3B82F6', '#1D4ED8']}
                            style={styles.selectedGradient}>
                            <Text style={styles.selectedText}>Selected</Text>
                          </LinearGradient>
                        </View>
                      )}
                    </View>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            {/* Action Buttons */}
            <View style={styles.actions}>
              <TouchableOpacity style={styles.cancelButton} onPress={onClose}>
                <Text style={styles.cancelButtonText}>Cancel</Text>
              </TouchableOpacity>
              
              <TouchableOpacity style={styles.saveButton} onPress={handleSave}>
                <LinearGradient
                  colors={['#3B82F6', '#1D4ED8']}
                  style={styles.saveButtonGradient}>
                  <Text style={styles.saveButtonText}>Apply Settings</Text>
                </LinearGradient>
              </TouchableOpacity>
            </View>
          </View>
        </Animated.View>
      </Animated.View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  backdrop: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  backdropTouch: {
    flex: 1,
  },
  container: {
    maxHeight: height * 0.85,
  },
  card: {
    backgroundColor: '#FFFFFF',
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    paddingTop: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 12,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 24,
    paddingVertical: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  headerIcon: {
    width: 40,
    height: 40,
    borderRadius: 12,
    backgroundColor: '#EBF8FF',
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerTitle: {
    fontSize: 20,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
  },
  closeButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#F3F4F6',
    alignItems: 'center',
    justifyContent: 'center',
  },
  section: {
    paddingHorizontal: 24,
    paddingVertical: 24,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  sectionTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
  },
  sectionDescription: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    marginBottom: 20,
    lineHeight: 20,
  },
  valueBadge: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 12,
  },
  valueText: {
    fontSize: 14,
    fontFamily: 'Inter-Bold',
    color: '#FFFFFF',
  },
  sliderContainer: {
    gap: 16,
  },
  sliderLabels: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  sliderLabel: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
  },
  sliderTrack: {
    height: 40,
    justifyContent: 'center',
    position: 'relative',
  },
  sliderTrackBackground: {
    height: 6,
    backgroundColor: '#E5E7EB',
    borderRadius: 3,
  },
  sliderTrackActive: {
    position: 'absolute',
    height: 6,
    borderRadius: 3,
  },
  sliderThumb: {
    position: 'absolute',
    width: 24,
    height: 24,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },
  sliderThumbInner: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: '#FFFFFF',
  },
  confidenceIndicators: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingTop: 8,
  },
  confidenceIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  confidenceDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  confidenceLabel: {
    fontSize: 11,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
  },
  divider: {
    height: 1,
    backgroundColor: '#F3F4F6',
    marginHorizontal: 24,
  },
  radioGroup: {
    gap: 12,
  },
  radioOption: {
    borderWidth: 2,
    borderColor: '#E5E7EB',
    borderRadius: 16,
    padding: 16,
    backgroundColor: '#FFFFFF',
  },
  radioOptionSelected: {
    borderColor: '#3B82F6',
    backgroundColor: '#F8FAFF',
  },
  radioContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  radioLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
    flex: 1,
  },
  radioButton: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: '#D1D5DB',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#FFFFFF',
  },
  radioButtonSelected: {
    borderColor: '#3B82F6',
    backgroundColor: '#3B82F6',
  },
  radioText: {
    flex: 1,
  },
  radioLabel: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    marginBottom: 2,
  },
  radioLabelSelected: {
    color: '#1D4ED8',
  },
  radioDescription: {
    fontSize: 13,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    lineHeight: 18,
  },
  radioDescriptionSelected: {
    color: '#3B82F6',
  },
  selectedIndicator: {
    borderRadius: 8,
    overflow: 'hidden',
  },
  selectedGradient: {
    paddingHorizontal: 8,
    paddingVertical: 4,
  },
  selectedText: {
    fontSize: 11,
    fontFamily: 'Inter-Bold',
    color: '#FFFFFF',
  },
  actions: {
    flexDirection: 'row',
    paddingHorizontal: 24,
    paddingVertical: 24,
    paddingBottom: Platform.OS === 'ios' ? 40 : 24,
    gap: 12,
    borderTopWidth: 1,
    borderTopColor: '#F3F4F6',
  },
  cancelButton: {
    flex: 1,
    paddingVertical: 16,
    borderRadius: 12,
    backgroundColor: '#F3F4F6',
    alignItems: 'center',
    justifyContent: 'center',
  },
  cancelButtonText: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#6B7280',
  },
  saveButton: {
    flex: 2,
    borderRadius: 12,
    overflow: 'hidden',
  },
  saveButtonGradient: {
    paddingVertical: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  saveButtonText: {
    fontSize: 16,
    fontFamily: 'Inter-Bold',
    color: '#FFFFFF',
  },
});