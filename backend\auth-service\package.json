{"name": "bioscan-auth-service", "version": "1.0.0", "description": "BioScan Authentication Service - JWT, OAuth2, and security management", "main": "dist/index.js", "scripts": {"start": "node dist/index.js", "dev": "nodemon --exec ts-node src/index.ts", "build": "tsc", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "type-check": "tsc --noEmit", "migrate": "npx prisma migrate dev", "db:generate": "npx prisma generate", "db:push": "npx prisma db push", "db:seed": "ts-node prisma/seed.ts"}, "dependencies": {"express": "^4.18.2", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "cors": "^2.8.5", "compression": "^1.7.4", "morgan": "^1.10.0", "jsonwebtoken": "^9.0.2", "argon2": "^0.31.2", "redis": "^4.6.10", "prisma": "^5.7.0", "@prisma/client": "^5.7.0", "express-validator": "^7.0.1", "winston": "^3.11.0", "prom-client": "^15.0.0", "dotenv": "^16.3.1", "passport": "^0.7.0", "passport-google-oauth20": "^2.0.0", "passport-apple": "^2.0.2", "passport-jwt": "^4.0.1", "nodemailer": "^6.9.7", "crypto": "^1.0.1", "speakeasy": "^2.0.0", "qrcode": "^1.5.3", "zxcvbn": "^4.4.2", "cookie-parser": "^1.4.6", "express-session": "^1.17.3", "connect-redis": "^7.1.0"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/compression": "^1.7.5", "@types/morgan": "^1.9.9", "@types/jsonwebtoken": "^9.0.5", "@types/node": "^20.10.0", "@types/jest": "^29.5.8", "@types/supertest": "^2.0.16", "@types/passport": "^1.0.16", "@types/passport-google-oauth20": "^2.0.14", "@types/passport-jwt": "^3.0.13", "@types/nodemailer": "^6.4.14", "@types/speakeasy": "^2.0.10", "@types/qrcode": "^1.5.5", "@types/zxcvbn": "^4.4.4", "@types/cookie-parser": "^1.4.6", "@types/express-session": "^1.17.10", "typescript": "^5.3.2", "ts-node": "^10.9.1", "nodemon": "^3.0.2", "jest": "^29.7.0", "ts-jest": "^29.1.1", "supertest": "^6.3.3", "eslint": "^8.54.0", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0"}, "engines": {"node": ">=18.0.0"}}