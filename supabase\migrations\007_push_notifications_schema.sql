-- Push Notifications System Schema
-- This migration creates comprehensive push notification management with personalization

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Notification Templates Table
CREATE TABLE IF NOT EXISTS notification_templates (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL UNIQUE,
    category VARCHAR(50) NOT NULL CHECK (category IN ('daily_tip', 'achievement', 'reminder', 'social', 'system', 'marketing', 'educational')),
    title_template TEXT NOT NULL,
    body_template TEXT NOT NULL,
    action_url VARCHAR(500),
    icon_url VARCHAR(500),
    image_url VARCHAR(500),
    
    -- Personalization settings
    personalization_enabled BOOLEAN NOT NULL DEFAULT true,
    requires_user_data BOOLEAN NOT NULL DEFAULT false,
    target_audience JSONB DEFAULT '{}', -- Targeting criteria
    
    -- Scheduling
    is_scheduled BOOLEAN NOT NULL DEFAULT false,
    schedule_type VARCHAR(20) CHECK (schedule_type IN ('daily', 'weekly', 'monthly', 'custom')),
    schedule_time TIME,
    schedule_days INTEGER[], -- Days of week (0=Sunday, 6=Saturday)
    
    -- A/B Testing
    ab_test_enabled BOOLEAN NOT NULL DEFAULT false,
    ab_test_group VARCHAR(10) CHECK (ab_test_group IN ('A', 'B', 'control')),
    ab_test_percentage DECIMAL(5,2) DEFAULT 100.00,
    
    -- Status and metrics
    is_active BOOLEAN NOT NULL DEFAULT true,
    send_count INTEGER DEFAULT 0,
    open_count INTEGER DEFAULT 0,
    click_count INTEGER DEFAULT 0,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- User Notification Preferences Table
CREATE TABLE IF NOT EXISTS user_notification_preferences (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE UNIQUE,
    
    -- Global settings
    notifications_enabled BOOLEAN NOT NULL DEFAULT true,
    push_enabled BOOLEAN NOT NULL DEFAULT true,
    email_enabled BOOLEAN NOT NULL DEFAULT true,
    in_app_enabled BOOLEAN NOT NULL DEFAULT true,
    
    -- Category preferences
    daily_tips_enabled BOOLEAN NOT NULL DEFAULT true,
    achievements_enabled BOOLEAN NOT NULL DEFAULT true,
    reminders_enabled BOOLEAN NOT NULL DEFAULT true,
    social_enabled BOOLEAN NOT NULL DEFAULT true,
    system_enabled BOOLEAN NOT NULL DEFAULT true,
    marketing_enabled BOOLEAN NOT NULL DEFAULT false,
    educational_enabled BOOLEAN NOT NULL DEFAULT true,
    
    -- Timing preferences
    quiet_hours_enabled BOOLEAN NOT NULL DEFAULT false,
    quiet_hours_start TIME DEFAULT '22:00:00',
    quiet_hours_end TIME DEFAULT '08:00:00',
    timezone VARCHAR(50) DEFAULT 'UTC',
    
    -- Frequency limits
    max_daily_notifications INTEGER DEFAULT 5,
    max_weekly_notifications INTEGER DEFAULT 20,
    
    -- Personalization preferences
    personalized_content BOOLEAN NOT NULL DEFAULT true,
    location_based_content BOOLEAN NOT NULL DEFAULT false,
    behavior_based_content BOOLEAN NOT NULL DEFAULT true,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Push Notification Tokens Table
CREATE TABLE IF NOT EXISTS push_notification_tokens (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    token VARCHAR(500) NOT NULL,
    platform VARCHAR(20) NOT NULL CHECK (platform IN ('ios', 'android', 'web')),
    device_id VARCHAR(200),
    device_name VARCHAR(100),
    app_version VARCHAR(20),
    is_active BOOLEAN NOT NULL DEFAULT true,
    last_used_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(user_id, token, platform)
);

-- Notification Queue Table
CREATE TABLE IF NOT EXISTS notification_queue (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    template_id UUID REFERENCES notification_templates(id) ON DELETE SET NULL,
    
    -- Message content
    title TEXT NOT NULL,
    body TEXT NOT NULL,
    action_url VARCHAR(500),
    icon_url VARCHAR(500),
    image_url VARCHAR(500),
    custom_data JSONB DEFAULT '{}',
    
    -- Delivery settings
    delivery_method VARCHAR(20) NOT NULL DEFAULT 'push' CHECK (delivery_method IN ('push', 'email', 'in_app', 'sms')),
    priority VARCHAR(10) NOT NULL DEFAULT 'normal' CHECK (priority IN ('low', 'normal', 'high', 'urgent')),
    
    -- Scheduling
    scheduled_for TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE,
    
    -- Status tracking
    status VARCHAR(20) NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'sent', 'delivered', 'failed', 'expired', 'cancelled')),
    sent_at TIMESTAMP WITH TIME ZONE,
    delivered_at TIMESTAMP WITH TIME ZONE,
    error_message TEXT,
    
    -- A/B Testing
    ab_test_group VARCHAR(10),
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Notification Delivery Log Table
CREATE TABLE IF NOT EXISTS notification_delivery_log (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    queue_id UUID REFERENCES notification_queue(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    template_id UUID REFERENCES notification_templates(id) ON DELETE SET NULL,
    
    -- Delivery details
    delivery_method VARCHAR(20) NOT NULL,
    platform VARCHAR(20),
    token VARCHAR(500),
    
    -- Status and timing
    status VARCHAR(20) NOT NULL CHECK (status IN ('sent', 'delivered', 'failed', 'opened', 'clicked')),
    response_data JSONB,
    error_code VARCHAR(50),
    error_message TEXT,
    
    -- User interaction
    opened_at TIMESTAMP WITH TIME ZONE,
    clicked_at TIMESTAMP WITH TIME ZONE,
    action_taken VARCHAR(100),
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Notification Analytics Table
CREATE TABLE IF NOT EXISTS notification_analytics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    date DATE NOT NULL,
    template_id UUID REFERENCES notification_templates(id) ON DELETE CASCADE,
    category VARCHAR(50) NOT NULL,
    
    -- Metrics
    sent_count INTEGER DEFAULT 0,
    delivered_count INTEGER DEFAULT 0,
    opened_count INTEGER DEFAULT 0,
    clicked_count INTEGER DEFAULT 0,
    failed_count INTEGER DEFAULT 0,
    
    -- Rates (calculated)
    delivery_rate DECIMAL(5,2) DEFAULT 0.00,
    open_rate DECIMAL(5,2) DEFAULT 0.00,
    click_rate DECIMAL(5,2) DEFAULT 0.00,
    
    -- A/B Testing metrics
    ab_test_group VARCHAR(10),
    conversion_count INTEGER DEFAULT 0,
    conversion_rate DECIMAL(5,2) DEFAULT 0.00,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(date, template_id, ab_test_group)
);

-- User Engagement Scores Table
CREATE TABLE IF NOT EXISTS user_engagement_scores (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE UNIQUE,
    
    -- Engagement metrics
    total_notifications_received INTEGER DEFAULT 0,
    total_notifications_opened INTEGER DEFAULT 0,
    total_notifications_clicked INTEGER DEFAULT 0,
    
    -- Calculated scores (0-100)
    engagement_score DECIMAL(5,2) DEFAULT 50.00,
    open_rate DECIMAL(5,2) DEFAULT 0.00,
    click_rate DECIMAL(5,2) DEFAULT 0.00,
    
    -- Behavioral insights
    preferred_time_of_day TIME,
    preferred_days_of_week INTEGER[],
    preferred_categories TEXT[],
    
    -- Churn prediction
    churn_risk_score DECIMAL(5,2) DEFAULT 0.00,
    last_engagement_at TIMESTAMP WITH TIME ZONE,
    
    -- Personalization data
    interests JSONB DEFAULT '{}',
    location_preferences JSONB DEFAULT '{}',
    
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_notification_templates_category ON notification_templates(category);
CREATE INDEX IF NOT EXISTS idx_notification_templates_active ON notification_templates(is_active);
CREATE INDEX IF NOT EXISTS idx_notification_templates_scheduled ON notification_templates(is_scheduled, schedule_time);

CREATE INDEX IF NOT EXISTS idx_user_notification_preferences_user_id ON user_notification_preferences(user_id);

CREATE INDEX IF NOT EXISTS idx_push_tokens_user_id ON push_notification_tokens(user_id);
CREATE INDEX IF NOT EXISTS idx_push_tokens_platform ON push_notification_tokens(platform);
CREATE INDEX IF NOT EXISTS idx_push_tokens_active ON push_notification_tokens(is_active);

CREATE INDEX IF NOT EXISTS idx_notification_queue_user_id ON notification_queue(user_id);
CREATE INDEX IF NOT EXISTS idx_notification_queue_status ON notification_queue(status);
CREATE INDEX IF NOT EXISTS idx_notification_queue_scheduled ON notification_queue(scheduled_for);
CREATE INDEX IF NOT EXISTS idx_notification_queue_template ON notification_queue(template_id);

CREATE INDEX IF NOT EXISTS idx_delivery_log_user_id ON notification_delivery_log(user_id);
CREATE INDEX IF NOT EXISTS idx_delivery_log_template_id ON notification_delivery_log(template_id);
CREATE INDEX IF NOT EXISTS idx_delivery_log_status ON notification_delivery_log(status);
CREATE INDEX IF NOT EXISTS idx_delivery_log_created_at ON notification_delivery_log(created_at);

CREATE INDEX IF NOT EXISTS idx_notification_analytics_date ON notification_analytics(date);
CREATE INDEX IF NOT EXISTS idx_notification_analytics_template ON notification_analytics(template_id);
CREATE INDEX IF NOT EXISTS idx_notification_analytics_category ON notification_analytics(category);

CREATE INDEX IF NOT EXISTS idx_user_engagement_user_id ON user_engagement_scores(user_id);
CREATE INDEX IF NOT EXISTS idx_user_engagement_score ON user_engagement_scores(engagement_score);
CREATE INDEX IF NOT EXISTS idx_user_engagement_churn ON user_engagement_scores(churn_risk_score);

-- Create updated_at triggers
CREATE TRIGGER update_notification_templates_updated_at BEFORE UPDATE ON notification_templates FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_user_notification_preferences_updated_at BEFORE UPDATE ON user_notification_preferences FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_notification_queue_updated_at BEFORE UPDATE ON notification_queue FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_user_engagement_scores_updated_at BEFORE UPDATE ON user_engagement_scores FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert default notification templates
INSERT INTO notification_templates (name, category, title_template, body_template, schedule_type, schedule_time, is_scheduled) VALUES
('daily_nature_tip', 'daily_tip', 'Daily Nature Tip 🌿', 'Did you know? {{tip_content}}', 'daily', '09:00:00', true),
('weekly_challenge', 'educational', 'Weekly Challenge 🎯', 'This week''s challenge: {{challenge_description}}', 'weekly', '10:00:00', true),
('identification_streak', 'achievement', 'Great Job! 🏆', 'You''re on a {{streak_count}} day identification streak!', NULL, NULL, false),
('new_species_discovered', 'achievement', 'New Discovery! 🔍', 'You''ve identified {{species_name}} for the first time!', NULL, NULL, false),
('location_reminder', 'reminder', 'Explore Nearby 📍', 'There are {{species_count}} species waiting to be discovered near you!', NULL, NULL, false),
('community_highlight', 'social', 'Community Spotlight ⭐', '{{user_name}} just identified a rare {{species_name}}!', NULL, NULL, false),
('app_update', 'system', 'App Update Available 📱', 'New features and improvements are ready for you!', NULL, NULL, false),
('premium_trial', 'marketing', 'Try Premium Free 💎', 'Unlock unlimited identifications with a 7-day free trial!', NULL, NULL, false)
ON CONFLICT (name) DO NOTHING;

-- Enable RLS on all notification tables
ALTER TABLE notification_templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_notification_preferences ENABLE ROW LEVEL SECURITY;
ALTER TABLE push_notification_tokens ENABLE ROW LEVEL SECURITY;
ALTER TABLE notification_queue ENABLE ROW LEVEL SECURITY;
ALTER TABLE notification_delivery_log ENABLE ROW LEVEL SECURITY;
ALTER TABLE notification_analytics ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_engagement_scores ENABLE ROW LEVEL SECURITY;

-- Notification Templates Policies (Admin and service access)
CREATE POLICY "Service role can manage notification templates" ON notification_templates
    FOR ALL USING (auth.role() = 'service_role');

CREATE POLICY "Public can view active templates" ON notification_templates
    FOR SELECT USING (is_active = true);

CREATE POLICY "Admins can manage templates" ON notification_templates
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM user_profiles
            WHERE id = auth.uid()
            AND role IN ('admin', 'super_admin')
        )
    );

-- User Notification Preferences Policies
CREATE POLICY "Users can view own notification preferences" ON user_notification_preferences
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own notification preferences" ON user_notification_preferences
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own notification preferences" ON user_notification_preferences
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Service role can manage notification preferences" ON user_notification_preferences
    FOR ALL USING (auth.role() = 'service_role');

-- Push Notification Tokens Policies
CREATE POLICY "Users can view own push tokens" ON push_notification_tokens
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own push tokens" ON push_notification_tokens
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own push tokens" ON push_notification_tokens
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own push tokens" ON push_notification_tokens
    FOR DELETE USING (auth.uid() = user_id);

CREATE POLICY "Service role can manage push tokens" ON push_notification_tokens
    FOR ALL USING (auth.role() = 'service_role');

-- Notification Queue Policies
CREATE POLICY "Users can view own notifications" ON notification_queue
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Service role can manage notification queue" ON notification_queue
    FOR ALL USING (auth.role() = 'service_role');

CREATE POLICY "Admins can view notification queue" ON notification_queue
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM user_profiles
            WHERE id = auth.uid()
            AND role IN ('admin', 'super_admin')
        )
    );

-- Notification Delivery Log Policies
CREATE POLICY "Users can view own delivery logs" ON notification_delivery_log
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Service role can manage delivery logs" ON notification_delivery_log
    FOR ALL USING (auth.role() = 'service_role');

CREATE POLICY "Admins can view delivery logs" ON notification_delivery_log
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM user_profiles
            WHERE id = auth.uid()
            AND role IN ('admin', 'super_admin')
        )
    );

-- Notification Analytics Policies (Admin only)
CREATE POLICY "Service role can manage analytics" ON notification_analytics
    FOR ALL USING (auth.role() = 'service_role');

CREATE POLICY "Admins can view analytics" ON notification_analytics
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM user_profiles
            WHERE id = auth.uid()
            AND role IN ('admin', 'super_admin')
        )
    );

-- User Engagement Scores Policies
CREATE POLICY "Users can view own engagement scores" ON user_engagement_scores
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Service role can manage engagement scores" ON user_engagement_scores
    FOR ALL USING (auth.role() = 'service_role');

CREATE POLICY "Admins can view engagement scores" ON user_engagement_scores
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM user_profiles
            WHERE id = auth.uid()
            AND role IN ('admin', 'super_admin')
        )
    );
