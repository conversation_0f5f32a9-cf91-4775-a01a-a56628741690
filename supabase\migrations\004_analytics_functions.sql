-- Analytics Functions for Payment System
-- This migration creates comprehensive analytics functions for revenue, subscriptions, and payments

-- Function to get revenue metrics
CREATE OR REPLACE FUNCTION get_revenue_metrics(period_type TEXT DEFAULT 'month')
RETURNS JSON AS $$
DECLARE
    start_date TIMESTAMP WITH TIME ZONE;
    end_date TIMESTAMP WITH TIME ZONE;
    prev_start_date TIMESTAMP WITH TIME ZONE;
    prev_end_date TIMESTAMP WITH TIME ZONE;
    current_mrr DECIMAL(12,2);
    prev_mrr DECIMAL(12,2);
    total_revenue DECIMAL(12,2);
    revenue_growth DECIMAL(5,2);
BEGIN
    -- Calculate date ranges based on period
    CASE period_type
        WHEN 'day' THEN
            start_date := date_trunc('day', NOW());
            end_date := start_date + INTERVAL '1 day';
            prev_start_date := start_date - INTERVAL '1 day';
            prev_end_date := start_date;
        WHEN 'week' THEN
            start_date := date_trunc('week', NOW());
            end_date := start_date + INTERVAL '1 week';
            prev_start_date := start_date - INTERVAL '1 week';
            prev_end_date := start_date;
        WHEN 'year' THEN
            start_date := date_trunc('year', NOW());
            end_date := start_date + INTERVAL '1 year';
            prev_start_date := start_date - INTERVAL '1 year';
            prev_end_date := start_date;
        ELSE -- month
            start_date := date_trunc('month', NOW());
            end_date := start_date + INTERVAL '1 month';
            prev_start_date := start_date - INTERVAL '1 month';
            prev_end_date := start_date;
    END CASE;

    -- Calculate current MRR
    SELECT COALESCE(SUM(
        CASE 
            WHEN us.current_period_end > us.current_period_start + INTERVAL '25 days' 
            THEN sp.price_yearly / 12 
            ELSE sp.price_monthly 
        END
    ), 0) INTO current_mrr
    FROM user_subscriptions us
    JOIN subscription_plans sp ON us.plan_id = sp.id
    WHERE us.status = 'active'
    AND us.current_period_end >= NOW();

    -- Calculate previous period MRR
    SELECT COALESCE(SUM(
        CASE 
            WHEN us.current_period_end > us.current_period_start + INTERVAL '25 days' 
            THEN sp.price_yearly / 12 
            ELSE sp.price_monthly 
        END
    ), 0) INTO prev_mrr
    FROM user_subscriptions us
    JOIN subscription_plans sp ON us.plan_id = sp.id
    WHERE us.status = 'active'
    AND us.current_period_end >= prev_start_date
    AND us.current_period_end < prev_end_date;

    -- Calculate total revenue for period
    SELECT COALESCE(SUM(amount_paid), 0) INTO total_revenue
    FROM invoices
    WHERE status = 'paid'
    AND created_at >= start_date
    AND created_at < end_date;

    -- Calculate revenue growth
    IF prev_mrr > 0 THEN
        revenue_growth := ((current_mrr - prev_mrr) / prev_mrr) * 100;
    ELSE
        revenue_growth := 0;
    END IF;

    RETURN json_build_object(
        'mrr', current_mrr,
        'arr', current_mrr * 12,
        'totalRevenue', total_revenue,
        'revenueGrowth', revenue_growth,
        'period', period_type
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get subscription metrics
CREATE OR REPLACE FUNCTION get_subscription_metrics(period_type TEXT DEFAULT 'month')
RETURNS JSON AS $$
DECLARE
    start_date TIMESTAMP WITH TIME ZONE;
    end_date TIMESTAMP WITH TIME ZONE;
    total_subs INTEGER;
    active_subs INTEGER;
    trial_subs INTEGER;
    canceled_subs INTEGER;
    churn_rate DECIMAL(5,2);
    conversion_rate DECIMAL(5,2);
    arpu DECIMAL(10,2);
BEGIN
    -- Calculate date range
    CASE period_type
        WHEN 'day' THEN
            start_date := date_trunc('day', NOW());
            end_date := start_date + INTERVAL '1 day';
        WHEN 'week' THEN
            start_date := date_trunc('week', NOW());
            end_date := start_date + INTERVAL '1 week';
        WHEN 'year' THEN
            start_date := date_trunc('year', NOW());
            end_date := start_date + INTERVAL '1 year';
        ELSE -- month
            start_date := date_trunc('month', NOW());
            end_date := start_date + INTERVAL '1 month';
    END CASE;

    -- Get subscription counts
    SELECT COUNT(*) INTO total_subs FROM user_subscriptions;
    
    SELECT COUNT(*) INTO active_subs 
    FROM user_subscriptions 
    WHERE status = 'active';
    
    SELECT COUNT(*) INTO trial_subs 
    FROM user_subscriptions 
    WHERE status = 'trialing';
    
    SELECT COUNT(*) INTO canceled_subs 
    FROM user_subscriptions 
    WHERE status = 'canceled'
    AND canceled_at >= start_date
    AND canceled_at < end_date;

    -- Calculate churn rate (canceled in period / active at start of period)
    IF active_subs > 0 THEN
        churn_rate := (canceled_subs::DECIMAL / active_subs) * 100;
    ELSE
        churn_rate := 0;
    END IF;

    -- Calculate trial to paid conversion rate
    WITH trial_conversions AS (
        SELECT COUNT(*) as conversions
        FROM user_subscriptions
        WHERE status = 'active'
        AND trial_end IS NOT NULL
        AND created_at >= start_date - INTERVAL '30 days'
        AND created_at < end_date
    )
    SELECT CASE 
        WHEN trial_subs > 0 THEN (conversions::DECIMAL / trial_subs) * 100
        ELSE 0
    END INTO conversion_rate
    FROM trial_conversions;

    -- Calculate ARPU (Average Revenue Per User)
    WITH revenue_data AS (
        SELECT 
            COUNT(DISTINCT us.user_id) as users,
            SUM(CASE 
                WHEN us.current_period_end > us.current_period_start + INTERVAL '25 days' 
                THEN sp.price_yearly / 12 
                ELSE sp.price_monthly 
            END) as total_revenue
        FROM user_subscriptions us
        JOIN subscription_plans sp ON us.plan_id = sp.id
        WHERE us.status = 'active'
    )
    SELECT CASE 
        WHEN users > 0 THEN total_revenue / users
        ELSE 0
    END INTO arpu
    FROM revenue_data;

    RETURN json_build_object(
        'totalSubscriptions', total_subs,
        'activeSubscriptions', active_subs,
        'trialSubscriptions', trial_subs,
        'canceledSubscriptions', canceled_subs,
        'churnRate', churn_rate,
        'conversionRate', conversion_rate,
        'averageRevenuePerUser', arpu
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get payment metrics
CREATE OR REPLACE FUNCTION get_payment_metrics(period_type TEXT DEFAULT 'month')
RETURNS JSON AS $$
DECLARE
    start_date TIMESTAMP WITH TIME ZONE;
    end_date TIMESTAMP WITH TIME ZONE;
    total_transactions INTEGER;
    successful_transactions INTEGER;
    failed_transactions INTEGER;
    success_rate DECIMAL(5,2);
    avg_transaction_value DECIMAL(10,2);
    total_volume DECIMAL(12,2);
BEGIN
    -- Calculate date range
    CASE period_type
        WHEN 'day' THEN
            start_date := date_trunc('day', NOW());
            end_date := start_date + INTERVAL '1 day';
        WHEN 'week' THEN
            start_date := date_trunc('week', NOW());
            end_date := start_date + INTERVAL '1 week';
        WHEN 'year' THEN
            start_date := date_trunc('year', NOW());
            end_date := start_date + INTERVAL '1 year';
        ELSE -- month
            start_date := date_trunc('month', NOW());
            end_date := start_date + INTERVAL '1 month';
    END CASE;

    -- Get transaction counts
    SELECT COUNT(*) INTO total_transactions
    FROM payment_transactions
    WHERE created_at >= start_date AND created_at < end_date;

    SELECT COUNT(*) INTO successful_transactions
    FROM payment_transactions
    WHERE status = 'succeeded'
    AND created_at >= start_date AND created_at < end_date;

    SELECT COUNT(*) INTO failed_transactions
    FROM payment_transactions
    WHERE status = 'failed'
    AND created_at >= start_date AND created_at < end_date;

    -- Calculate success rate
    IF total_transactions > 0 THEN
        success_rate := (successful_transactions::DECIMAL / total_transactions) * 100;
    ELSE
        success_rate := 0;
    END IF;

    -- Calculate average transaction value and total volume
    SELECT 
        COALESCE(AVG(amount), 0),
        COALESCE(SUM(amount), 0)
    INTO avg_transaction_value, total_volume
    FROM payment_transactions
    WHERE status = 'succeeded'
    AND created_at >= start_date AND created_at < end_date;

    RETURN json_build_object(
        'totalTransactions', total_transactions,
        'successfulTransactions', successful_transactions,
        'failedTransactions', failed_transactions,
        'successRate', success_rate,
        'averageTransactionValue', avg_transaction_value,
        'totalVolume', total_volume
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get top performing plans
CREATE OR REPLACE FUNCTION get_top_performing_plans(plan_limit INTEGER DEFAULT 5)
RETURNS TABLE (
    plan_id UUID,
    plan_name TEXT,
    subscribers BIGINT,
    revenue DECIMAL(12,2),
    conversion_rate DECIMAL(5,2),
    churn_rate DECIMAL(5,2),
    average_lifetime_value DECIMAL(10,2)
) AS $$
BEGIN
    RETURN QUERY
    WITH plan_stats AS (
        SELECT 
            sp.id,
            sp.name,
            COUNT(us.id) as sub_count,
            SUM(CASE 
                WHEN us.current_period_end > us.current_period_start + INTERVAL '25 days' 
                THEN sp.price_yearly / 12 
                ELSE sp.price_monthly 
            END) as monthly_revenue,
            COUNT(CASE WHEN us.status = 'canceled' THEN 1 END)::DECIMAL / NULLIF(COUNT(us.id), 0) * 100 as churn,
            AVG(CASE 
                WHEN us.current_period_end > us.current_period_start + INTERVAL '25 days' 
                THEN sp.price_yearly 
                ELSE sp.price_monthly * 12 
            END) as avg_ltv
        FROM subscription_plans sp
        LEFT JOIN user_subscriptions us ON sp.id = us.plan_id
        WHERE sp.is_active = true
        GROUP BY sp.id, sp.name, sp.price_monthly, sp.price_yearly
    )
    SELECT 
        ps.id,
        ps.name,
        ps.sub_count,
        COALESCE(ps.monthly_revenue, 0),
        0::DECIMAL(5,2), -- Placeholder for conversion rate
        COALESCE(ps.churn, 0),
        COALESCE(ps.avg_ltv, 0)
    FROM plan_stats ps
    ORDER BY ps.monthly_revenue DESC, ps.sub_count DESC
    LIMIT plan_limit;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get recent payment activity
CREATE OR REPLACE FUNCTION get_recent_payment_activity(activity_limit INTEGER DEFAULT 10)
RETURNS TABLE (
    activity_type TEXT,
    user_id UUID,
    amount DECIMAL(10,2),
    status TEXT,
    created_at TIMESTAMP WITH TIME ZONE,
    metadata JSONB
) AS $$
BEGIN
    RETURN QUERY
    (
        SELECT
            'payment'::TEXT,
            pt.user_id,
            pt.amount,
            pt.status,
            pt.created_at,
            jsonb_build_object(
                'transaction_id', pt.stripe_payment_intent_id,
                'currency', pt.currency
            )
        FROM payment_transactions pt
        ORDER BY pt.created_at DESC
        LIMIT activity_limit / 2
    )
    UNION ALL
    (
        SELECT
            'subscription'::TEXT,
            us.user_id,
            CASE
                WHEN us.current_period_end > us.current_period_start + INTERVAL '25 days'
                THEN sp.price_yearly / 12
                ELSE sp.price_monthly
            END,
            us.status,
            us.created_at,
            jsonb_build_object(
                'plan_name', sp.name,
                'subscription_id', us.stripe_subscription_id
            )
        FROM user_subscriptions us
        JOIN subscription_plans sp ON us.plan_id = sp.id
        ORDER BY us.created_at DESC
        LIMIT activity_limit / 2
    )
    ORDER BY created_at DESC
    LIMIT activity_limit;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get user analytics
CREATE OR REPLACE FUNCTION get_user_analytics(user_uuid UUID)
RETURNS JSON AS $$
DECLARE
    total_spent DECIMAL(12,2);
    lifetime_value DECIMAL(12,2);
    avg_risk_score INTEGER;
    last_activity TIMESTAMP WITH TIME ZONE;
    subscription_count INTEGER;
    payment_count INTEGER;
BEGIN
    -- Calculate total spent
    SELECT COALESCE(SUM(amount), 0) INTO total_spent
    FROM payment_transactions
    WHERE user_id = user_uuid AND status = 'succeeded';

    -- Calculate lifetime value (projected)
    SELECT COALESCE(
        SUM(CASE
            WHEN us.current_period_end > us.current_period_start + INTERVAL '25 days'
            THEN sp.price_yearly
            ELSE sp.price_monthly * 12
        END), 0
    ) INTO lifetime_value
    FROM user_subscriptions us
    JOIN subscription_plans sp ON us.plan_id = sp.id
    WHERE us.user_id = user_uuid AND us.status = 'active';

    -- Get average risk score
    SELECT COALESCE(AVG(risk_score), 0) INTO avg_risk_score
    FROM fraud_detection_logs
    WHERE user_id = user_uuid;

    -- Get last activity
    SELECT GREATEST(
        COALESCE(MAX(pt.created_at), '1970-01-01'::TIMESTAMP WITH TIME ZONE),
        COALESCE(MAX(us.updated_at), '1970-01-01'::TIMESTAMP WITH TIME ZONE)
    ) INTO last_activity
    FROM payment_transactions pt
    FULL OUTER JOIN user_subscriptions us ON pt.user_id = us.user_id
    WHERE COALESCE(pt.user_id, us.user_id) = user_uuid;

    -- Get counts
    SELECT COUNT(*) INTO subscription_count
    FROM user_subscriptions
    WHERE user_id = user_uuid;

    SELECT COUNT(*) INTO payment_count
    FROM payment_transactions
    WHERE user_id = user_uuid;

    RETURN json_build_object(
        'userId', user_uuid,
        'totalSpent', total_spent,
        'lifetimeValue', lifetime_value,
        'riskScore', avg_risk_score,
        'lastActivity', last_activity,
        'subscriptionCount', subscription_count,
        'paymentCount', payment_count
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
