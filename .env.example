# BioScan App Environment Configuration
# Copy this file to .env and fill in your actual values

# Supabase Configuration (Existing - Keep for database operations)
EXPO_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
EXPO_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key

# Firebase Configuration (New - For Authentication)
EXPO_PUBLIC_FIREBASE_API_KEY=your-firebase-api-key
EXPO_PUBLIC_FIREBASE_AUTH_DOMAIN=your-project.firebaseapp.com
EXPO_PUBLIC_FIREBASE_PROJECT_ID=your-firebase-project-id
EXPO_PUBLIC_FIREBASE_STORAGE_BUCKET=your-project.appspot.com
EXPO_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=your-messaging-sender-id
EXPO_PUBLIC_FIREBASE_APP_ID=your-firebase-app-id

# Turso Configuration (New - For Local Storage)
EXPO_PUBLIC_TURSO_URL=libsql://your-database.turso.io
EXPO_PUBLIC_TURSO_AUTH_TOKEN=your-turso-auth-token

# Google Gemini AI Configuration (Existing)
EXPO_PUBLIC_GEMINI_API_KEY=your-gemini-api-key

# External API Keys (Optional - For enhanced features)
EXPO_PUBLIC_EBIRD_API_KEY=your-ebird-api-key
EXPO_PUBLIC_INATURALIST_API_KEY=your-inaturalist-api-key
EXPO_PUBLIC_WEATHER_API_KEY=your-weather-api-key

# Feature Flags (Development)
EXPO_PUBLIC_ENABLE_OFFLINE_MODE=true
EXPO_PUBLIC_ENABLE_BETA_FEATURES=false
EXPO_PUBLIC_ENABLE_DEBUG_LOGGING=false
EXPO_PUBLIC_FORCE_MIGRATION=false

# Development Settings
EXPO_PUBLIC_API_BASE_URL=https://api.bioscan.app
EXPO_PUBLIC_ENVIRONMENT=development
