import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  Linking,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { router } from 'expo-router';
import * as Haptics from 'expo-haptics';

import {
  PaymentService,
  SubscriptionPlan,
  UserSubscription,
  UsageStats,
} from '../../services/PaymentService';
import { useAuth } from '../../components/AuthContext';

export default function SubscriptionScreen() {
  const [plans, setPlans] = useState<SubscriptionPlan[]>([]);
  const [currentSubscription, setCurrentSubscription] = useState<UserSubscription | null>(null);
  const [usageStats, setUsageStats] = useState<UsageStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [billingPeriod, setBillingPeriod] = useState<'monthly' | 'yearly'>('monthly');
  const [processingPlanId, setProcessingPlanId] = useState<string | null>(null);

  const { user, isAuthenticated } = useAuth();
  const paymentService = new PaymentService();

  useEffect(() => {
    if (!isAuthenticated) {
      router.replace('/auth/login');
      return;
    }
    
    loadSubscriptionData();
  }, [isAuthenticated]);

  const loadSubscriptionData = async () => {
    try {
      setLoading(true);
      
      const [plansData, subscriptionData, usageData] = await Promise.all([
        paymentService.getSubscriptionPlans(),
        paymentService.getCurrentSubscription(),
        paymentService.getUsageStats(),
      ]);

      setPlans(plansData);
      setCurrentSubscription(subscriptionData);
      setUsageStats(usageData);
    } catch (error) {
      console.error('Error loading subscription data:', error);
      Alert.alert('Error', 'Failed to load subscription information');
    } finally {
      setLoading(false);
    }
  };

  const handleSubscribe = async (plan: SubscriptionPlan) => {
    try {
      setProcessingPlanId(plan.id);
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);

      const { url } = await paymentService.createCheckoutSession(plan.id, billingPeriod);
      
      // Open Stripe Checkout
      const supported = await Linking.canOpenURL(url);
      if (supported) {
        await Linking.openURL(url);
      } else {
        Alert.alert('Error', 'Unable to open payment page');
      }
    } catch (error) {
      console.error('Error creating checkout session:', error);
      Alert.alert('Error', 'Failed to start subscription process');
    } finally {
      setProcessingPlanId(null);
    }
  };

  const handleManageSubscription = async () => {
    try {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
      
      const { url } = await paymentService.createCustomerPortalSession();
      
      const supported = await Linking.canOpenURL(url);
      if (supported) {
        await Linking.openURL(url);
      } else {
        Alert.alert('Error', 'Unable to open subscription management');
      }
    } catch (error) {
      console.error('Error opening customer portal:', error);
      Alert.alert('Error', 'Failed to open subscription management');
    }
  };

  const handleCancelSubscription = () => {
    Alert.alert(
      'Cancel Subscription',
      'Are you sure you want to cancel your subscription? You\'ll continue to have access until the end of your current billing period.',
      [
        { text: 'Keep Subscription', style: 'cancel' },
        {
          text: 'Cancel',
          style: 'destructive',
          onPress: async () => {
            try {
              await paymentService.cancelSubscription();
              await loadSubscriptionData(); // Refresh data
            } catch (error) {
              console.error('Error cancelling subscription:', error);
            }
          },
        },
      ]
    );
  };

  const getUsageColor = (percentage: number) => {
    if (percentage >= 90) return '#EF4444';
    if (percentage >= 70) return '#F59E0B';
    return '#22C55E';
  };

  const renderUsageStats = () => {
    if (!usageStats) return null;

    return (
      <View style={styles.usageContainer}>
        <Text style={styles.usageTitle}>Usage This Month</Text>
        
        <View style={styles.usageBar}>
          <View style={styles.usageBarBackground}>
            <View
              style={[
                styles.usageBarFill,
                {
                  width: `${usageStats.usage_percentage}%`,
                  backgroundColor: getUsageColor(usageStats.usage_percentage),
                },
              ]}
            />
          </View>
          <Text style={styles.usagePercentage}>
            {usageStats.usage_percentage}%
          </Text>
        </View>

        <View style={styles.usageStats}>
          <View style={styles.usageStat}>
            <Text style={styles.usageStatNumber}>
              {usageStats.current_period_identifications}
            </Text>
            <Text style={styles.usageStatLabel}>Used</Text>
          </View>
          <View style={styles.usageStat}>
            <Text style={styles.usageStatNumber}>
              {usageStats.remaining_identifications}
            </Text>
            <Text style={styles.usageStatLabel}>Remaining</Text>
          </View>
          <View style={styles.usageStat}>
            <Text style={styles.usageStatNumber}>
              {usageStats.total_identifications}
            </Text>
            <Text style={styles.usageStatLabel}>Total</Text>
          </View>
        </View>
      </View>
    );
  };

  const renderCurrentSubscription = () => {
    if (!currentSubscription) return null;

    const isActive = currentSubscription.status === 'active';
    const isPremium = currentSubscription.plan?.name !== 'Free';

    return (
      <View style={styles.currentSubscriptionContainer}>
        <LinearGradient
          colors={isActive ? ['#22C55E', '#16A34A'] : ['#6B7280', '#4B5563']}
          style={styles.currentSubscriptionGradient}
        >
          <View style={styles.currentSubscriptionHeader}>
            <Text style={styles.currentSubscriptionTitle}>
              Current Plan: {currentSubscription.plan?.name}
            </Text>
            <View style={[
              styles.statusBadge,
              { backgroundColor: isActive ? '#DCFCE7' : '#F3F4F6' }
            ]}>
              <Text style={[
                styles.statusText,
                { color: isActive ? '#166534' : '#6B7280' }
              ]}>
                {currentSubscription.status.toUpperCase()}
              </Text>
            </View>
          </View>

          {isPremium && (
            <Text style={styles.currentSubscriptionPrice}>
              {paymentService.formatPrice(
                billingPeriod === 'yearly' 
                  ? currentSubscription.plan?.price_yearly || 0
                  : currentSubscription.plan?.price_monthly || 0
              )}
              /{billingPeriod === 'yearly' ? 'year' : 'month'}
            </Text>
          )}

          <Text style={styles.currentSubscriptionPeriod}>
            {isActive ? 'Renews' : 'Expires'} on{' '}
            {new Date(currentSubscription.current_period_end).toLocaleDateString()}
          </Text>

          {currentSubscription.cancel_at_period_end && (
            <Text style={styles.cancellationNotice}>
              ⚠️ Subscription will cancel at the end of this period
            </Text>
          )}
        </LinearGradient>

        <View style={styles.subscriptionActions}>
          <TouchableOpacity
            style={styles.manageButton}
            onPress={handleManageSubscription}
          >
            <Ionicons name="settings" size={20} color="#22C55E" />
            <Text style={styles.manageButtonText}>Manage Subscription</Text>
          </TouchableOpacity>

          {isActive && !currentSubscription.cancel_at_period_end && (
            <TouchableOpacity
              style={styles.cancelButton}
              onPress={handleCancelSubscription}
            >
              <Ionicons name="close-circle" size={20} color="#EF4444" />
              <Text style={styles.cancelButtonText}>Cancel</Text>
            </TouchableOpacity>
          )}
        </View>
      </View>
    );
  };

  const renderPlanCard = (plan: SubscriptionPlan) => {
    const isCurrentPlan = currentSubscription?.plan_id === plan.id;
    const isProcessing = processingPlanId === plan.id;
    const price = billingPeriod === 'yearly' ? plan.price_yearly : plan.price_monthly;
    const savings = billingPeriod === 'yearly' 
      ? paymentService.calculateYearlySavings(plan.price_monthly, plan.price_yearly)
      : null;

    return (
      <View key={plan.id} style={[
        styles.planCard,
        isCurrentPlan && styles.currentPlanCard
      ]}>
        {plan.name === 'Premium' && (
          <View style={styles.popularBadge}>
            <Text style={styles.popularText}>Most Popular</Text>
          </View>
        )}

        <Text style={styles.planName}>{plan.name}</Text>
        <Text style={styles.planDescription}>{plan.description}</Text>

        <View style={styles.priceContainer}>
          <Text style={styles.planPrice}>
            {paymentService.formatPrice(price)}
          </Text>
          <Text style={styles.pricePeriod}>
            /{billingPeriod === 'yearly' ? 'year' : 'month'}
          </Text>
        </View>

        {savings && savings.percentage > 0 && (
          <Text style={styles.savingsText}>
            Save {savings.percentage}% with yearly billing
          </Text>
        )}

        <View style={styles.featuresContainer}>
          {plan.features.map((feature, index) => (
            <View key={index} style={styles.featureItem}>
              <Ionicons name="checkmark" size={16} color="#22C55E" />
              <Text style={styles.featureText}>{feature}</Text>
            </View>
          ))}
        </View>

        {!isCurrentPlan && (
          <TouchableOpacity
            style={[
              styles.subscribeButton,
              plan.name === 'Premium' && styles.premiumButton,
              isProcessing && styles.processingButton,
            ]}
            onPress={() => handleSubscribe(plan)}
            disabled={isProcessing}
          >
            {isProcessing ? (
              <ActivityIndicator size="small" color="white" />
            ) : (
              <>
                <Text style={styles.subscribeButtonText}>
                  {plan.name === 'Free' ? 'Current Plan' : 'Subscribe'}
                </Text>
                {plan.name !== 'Free' && (
                  <Ionicons name="arrow-forward" size={16} color="white" />
                )}
              </>
            )}
          </TouchableOpacity>
        )}

        {isCurrentPlan && (
          <View style={styles.currentPlanBadge}>
            <Ionicons name="checkmark-circle" size={16} color="#22C55E" />
            <Text style={styles.currentPlanText}>Current Plan</Text>
          </View>
        )}
      </View>
    );
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#22C55E" />
        <Text style={styles.loadingText}>Loading subscription information...</Text>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      {/* Header */}
      <LinearGradient
        colors={['#22C55E', '#16A34A']}
        style={styles.header}
      >
        <Text style={styles.headerTitle}>Subscription</Text>
        <Text style={styles.headerSubtitle}>
          Choose the plan that's right for you
        </Text>
      </LinearGradient>

      {/* Usage Stats */}
      {renderUsageStats()}

      {/* Current Subscription */}
      {renderCurrentSubscription()}

      {/* Billing Period Toggle */}
      <View style={styles.billingToggleContainer}>
        <Text style={styles.billingToggleTitle}>Billing Period</Text>
        <View style={styles.billingToggle}>
          <TouchableOpacity
            style={[
              styles.billingOption,
              billingPeriod === 'monthly' && styles.billingOptionActive,
            ]}
            onPress={() => setBillingPeriod('monthly')}
          >
            <Text style={[
              styles.billingOptionText,
              billingPeriod === 'monthly' && styles.billingOptionTextActive,
            ]}>
              Monthly
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[
              styles.billingOption,
              billingPeriod === 'yearly' && styles.billingOptionActive,
            ]}
            onPress={() => setBillingPeriod('yearly')}
          >
            <Text style={[
              styles.billingOptionText,
              billingPeriod === 'yearly' && styles.billingOptionTextActive,
            ]}>
              Yearly
            </Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Subscription Plans */}
      <View style={styles.plansContainer}>
        {plans.map(renderPlanCard)}
      </View>

      {/* Footer */}
      <View style={styles.footer}>
        <Text style={styles.footerText}>
          All subscriptions include a 7-day free trial. Cancel anytime.
        </Text>
        <TouchableOpacity onPress={() => Linking.openURL('https://bioscan.app/terms')}>
          <Text style={styles.footerLink}>Terms of Service</Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#F9FAFB',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#6B7280',
  },
  header: {
    padding: 20,
    paddingTop: 60,
    paddingBottom: 30,
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: 'white',
    marginBottom: 4,
  },
  headerSubtitle: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.9)',
  },
  usageContainer: {
    margin: 20,
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  usageTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1F2937',
    marginBottom: 16,
  },
  usageBar: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  usageBarBackground: {
    flex: 1,
    height: 8,
    backgroundColor: '#E5E7EB',
    borderRadius: 4,
    overflow: 'hidden',
    marginRight: 12,
  },
  usageBarFill: {
    height: '100%',
    borderRadius: 4,
  },
  usagePercentage: {
    fontSize: 14,
    fontWeight: '600',
    color: '#1F2937',
    minWidth: 40,
    textAlign: 'right',
  },
  usageStats: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  usageStat: {
    alignItems: 'center',
  },
  usageStatNumber: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1F2937',
  },
  usageStatLabel: {
    fontSize: 12,
    color: '#6B7280',
    marginTop: 4,
  },
  currentSubscriptionContainer: {
    margin: 20,
    marginTop: 0,
    borderRadius: 16,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 6,
  },
  currentSubscriptionGradient: {
    padding: 20,
  },
  currentSubscriptionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  currentSubscriptionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: 'white',
    flex: 1,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '600',
  },
  currentSubscriptionPrice: {
    fontSize: 24,
    fontWeight: 'bold',
    color: 'white',
    marginBottom: 4,
  },
  currentSubscriptionPeriod: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.9)',
  },
  cancellationNotice: {
    fontSize: 14,
    color: '#FEF3C7',
    marginTop: 8,
    fontWeight: '500',
  },
  subscriptionActions: {
    flexDirection: 'row',
    backgroundColor: 'white',
    padding: 16,
    gap: 12,
  },
  manageButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#22C55E',
    gap: 8,
  },
  manageButtonText: {
    color: '#22C55E',
    fontSize: 16,
    fontWeight: '600',
  },
  cancelButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#EF4444',
    gap: 8,
  },
  cancelButtonText: {
    color: '#EF4444',
    fontSize: 16,
    fontWeight: '600',
  },
  billingToggleContainer: {
    margin: 20,
    marginTop: 0,
  },
  billingToggleTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 12,
  },
  billingToggle: {
    flexDirection: 'row',
    backgroundColor: '#F3F4F6',
    borderRadius: 8,
    padding: 4,
  },
  billingOption: {
    flex: 1,
    paddingVertical: 8,
    alignItems: 'center',
    borderRadius: 6,
  },
  billingOptionActive: {
    backgroundColor: 'white',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  billingOptionText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#6B7280',
  },
  billingOptionTextActive: {
    color: '#1F2937',
  },
  plansContainer: {
    paddingHorizontal: 20,
    gap: 16,
  },
  planCard: {
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 20,
    borderWidth: 2,
    borderColor: '#E5E7EB',
    position: 'relative',
  },
  currentPlanCard: {
    borderColor: '#22C55E',
  },
  popularBadge: {
    position: 'absolute',
    top: -1,
    left: 20,
    right: 20,
    backgroundColor: '#3B82F6',
    paddingVertical: 4,
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    alignItems: 'center',
  },
  popularText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '600',
  },
  planName: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1F2937',
    marginBottom: 4,
    marginTop: 16,
  },
  planDescription: {
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 16,
  },
  priceContainer: {
    flexDirection: 'row',
    alignItems: 'baseline',
    marginBottom: 8,
  },
  planPrice: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#1F2937',
  },
  pricePeriod: {
    fontSize: 16,
    color: '#6B7280',
    marginLeft: 4,
  },
  savingsText: {
    fontSize: 14,
    color: '#22C55E',
    fontWeight: '600',
    marginBottom: 16,
  },
  featuresContainer: {
    marginBottom: 20,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
    gap: 8,
  },
  featureText: {
    fontSize: 14,
    color: '#4B5563',
    flex: 1,
  },
  subscribeButton: {
    backgroundColor: '#6B7280',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    borderRadius: 12,
    gap: 8,
  },
  premiumButton: {
    backgroundColor: '#22C55E',
  },
  processingButton: {
    backgroundColor: '#9CA3AF',
  },
  subscribeButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  currentPlanBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    gap: 8,
  },
  currentPlanText: {
    color: '#22C55E',
    fontSize: 16,
    fontWeight: '600',
  },
  footer: {
    padding: 20,
    alignItems: 'center',
  },
  footerText: {
    fontSize: 14,
    color: '#6B7280',
    textAlign: 'center',
    marginBottom: 8,
  },
  footerLink: {
    fontSize: 14,
    color: '#22C55E',
    fontWeight: '500',
  },
});
