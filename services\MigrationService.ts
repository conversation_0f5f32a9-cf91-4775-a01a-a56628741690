import AsyncStorage from '@react-native-async-storage/async-storage';
import { tursoService } from '../lib/turso';

export interface MigrationResult {
  success: boolean;
  migratedKeys: string[];
  failedKeys: string[];
  errors: Array<{ key: string; error: string }>;
}

export class MigrationService {
  private static instance: MigrationService;
  
  // Keys that should be migrated from AsyncStorage to Turso
  private readonly MIGRATION_KEYS = [
    'hasCompletedOnboarding',
    'userPreferences',
    'appSettings',
    'searchHistory',
    'savedSearches',
    'offlineIdentifications',
    'lastSyncTimestamp',
    'featureFlags',
    'notificationSettings',
    'privacySettings',
    'locationSettings',
    'cameraSettings',
    'displaySettings',
    'accessibilitySettings'
  ];

  private constructor() {}

  public static getInstance(): MigrationService {
    if (!MigrationService.instance) {
      MigrationService.instance = new MigrationService();
    }
    return MigrationService.instance;
  }

  /**
   * Migrate all AsyncStorage data to Turso
   */
  public async migrateToTurso(userId: string): Promise<MigrationResult> {
    const result: MigrationResult = {
      success: true,
      migratedKeys: [],
      failedKeys: [],
      errors: []
    };

    try {
      // Initialize Turso service if not already done
      await tursoService.initialize();

      // Check if migration has already been completed
      const migrationStatus = await tursoService.getAppSetting('migration_completed');
      if (migrationStatus) {
        console.log('Migration already completed, skipping...');
        return result;
      }

      // Get all AsyncStorage keys
      const allKeys = await AsyncStorage.getAllKeys();
      const keysToMigrate = allKeys.filter(key => 
        this.MIGRATION_KEYS.some(migrationKey => key.includes(migrationKey))
      );

      console.log(`Starting migration of ${keysToMigrate.length} keys...`);

      // Migrate each key
      for (const key of keysToMigrate) {
        try {
          await this.migrateKey(key, userId);
          result.migratedKeys.push(key);
        } catch (error) {
          console.error(`Failed to migrate key ${key}:`, error);
          result.failedKeys.push(key);
          result.errors.push({
            key,
            error: error instanceof Error ? error.message : 'Unknown error'
          });
          result.success = false;
        }
      }

      // Migrate specific structured data
      await this.migrateStructuredData(userId, result);

      // Mark migration as completed if successful
      if (result.success) {
        await tursoService.setAppSetting('migration_completed', true);
        await tursoService.setAppSetting('migration_date', new Date().toISOString());
        console.log('Migration completed successfully');
      }

    } catch (error) {
      console.error('Migration failed:', error);
      result.success = false;
      result.errors.push({
        key: 'MIGRATION_PROCESS',
        error: error instanceof Error ? error.message : 'Unknown migration error'
      });
    }

    return result;
  }

  /**
   * Migrate a specific key from AsyncStorage to Turso
   */
  private async migrateKey(key: string, userId: string): Promise<void> {
    const value = await AsyncStorage.getItem(key);
    if (value === null) return;

    try {
      const parsedValue = JSON.parse(value);
      
      // Determine the appropriate Turso storage method based on key type
      if (key.includes('userPreferences') || key.includes('Settings')) {
        await tursoService.setUserPreference(userId, key, parsedValue);
      } else {
        await tursoService.setAppSetting(key, parsedValue);
      }
    } catch (parseError) {
      // If JSON parsing fails, store as string
      if (key.includes('userPreferences') || key.includes('Settings')) {
        await tursoService.setUserPreference(userId, key, value);
      } else {
        await tursoService.setAppSetting(key, value);
      }
    }
  }

  /**
   * Migrate structured data that requires special handling
   */
  private async migrateStructuredData(userId: string, result: MigrationResult): Promise<void> {
    try {
      // Migrate onboarding status
      const onboardingStatus = await AsyncStorage.getItem('hasCompletedOnboarding');
      if (onboardingStatus) {
        await tursoService.setUserPreference(userId, 'hasCompletedOnboarding', onboardingStatus === 'true');
        result.migratedKeys.push('hasCompletedOnboarding_structured');
      }

      // Migrate user preferences object
      const userPrefs = await AsyncStorage.getItem('userPreferences');
      if (userPrefs) {
        const preferences = JSON.parse(userPrefs);
        for (const [key, value] of Object.entries(preferences)) {
          await tursoService.setUserPreference(userId, key, value);
        }
        result.migratedKeys.push('userPreferences_structured');
      }

      // Migrate search history
      const searchHistory = await AsyncStorage.getItem('searchHistory');
      if (searchHistory) {
        const history = JSON.parse(searchHistory);
        if (Array.isArray(history)) {
          for (const search of history) {
            await tursoService.addSearchHistory(
              userId,
              search.query || '',
              search.filters,
              search.resultsCount
            );
          }
        }
        result.migratedKeys.push('searchHistory_structured');
      }

      // Migrate offline identifications
      const offlineIds = await AsyncStorage.getItem('offlineIdentifications');
      if (offlineIds) {
        const identifications = JSON.parse(offlineIds);
        if (Array.isArray(identifications)) {
          for (const identification of identifications) {
            await tursoService.cacheIdentification(
              userId,
              identification.imageData || '',
              identification.result
            );
          }
        }
        result.migratedKeys.push('offlineIdentifications_structured');
      }

    } catch (error) {
      console.error('Failed to migrate structured data:', error);
      result.errors.push({
        key: 'STRUCTURED_DATA',
        error: error instanceof Error ? error.message : 'Unknown structured data error'
      });
    }
  }

  /**
   * Verify migration integrity by comparing data
   */
  public async verifyMigration(userId: string): Promise<boolean> {
    try {
      // Check if migration was marked as completed
      const migrationCompleted = await tursoService.getAppSetting('migration_completed');
      if (!migrationCompleted) {
        return false;
      }

      // Verify key data points
      const onboardingStatus = await tursoService.getUserPreference(userId, 'hasCompletedOnboarding');
      const asyncOnboarding = await AsyncStorage.getItem('hasCompletedOnboarding');
      
      if (asyncOnboarding && onboardingStatus !== (asyncOnboarding === 'true')) {
        console.warn('Onboarding status mismatch detected');
        return false;
      }

      // Verify user preferences
      const tursoPrefs = await tursoService.getAllUserPreferences(userId);
      const asyncPrefs = await AsyncStorage.getItem('userPreferences');
      
      if (asyncPrefs) {
        const parsedAsyncPrefs = JSON.parse(asyncPrefs);
        for (const key of Object.keys(parsedAsyncPrefs)) {
          if (tursoPrefs[key] === undefined) {
            console.warn(`Missing preference in Turso: ${key}`);
            return false;
          }
        }
      }

      console.log('Migration verification passed');
      return true;

    } catch (error) {
      console.error('Migration verification failed:', error);
      return false;
    }
  }

  /**
   * Rollback migration by restoring AsyncStorage data
   */
  public async rollbackMigration(): Promise<boolean> {
    try {
      console.log('Starting migration rollback...');
      
      // This is a safety mechanism - in practice, we should maintain AsyncStorage
      // data during the migration period for rollback purposes
      
      // Mark migration as not completed
      await tursoService.setAppSetting('migration_completed', false);
      await tursoService.setAppSetting('rollback_date', new Date().toISOString());
      
      console.log('Migration rollback completed');
      return true;

    } catch (error) {
      console.error('Migration rollback failed:', error);
      return false;
    }
  }

  /**
   * Clean up AsyncStorage after successful migration and verification
   */
  public async cleanupAsyncStorage(): Promise<void> {
    try {
      // Only cleanup if migration is verified and completed
      const migrationCompleted = await tursoService.getAppSetting('migration_completed');
      if (!migrationCompleted) {
        throw new Error('Cannot cleanup - migration not completed');
      }

      // Get all keys to clean up
      const allKeys = await AsyncStorage.getAllKeys();
      const keysToCleanup = allKeys.filter(key => 
        this.MIGRATION_KEYS.some(migrationKey => key.includes(migrationKey))
      );

      // Remove migrated keys from AsyncStorage
      await AsyncStorage.multiRemove(keysToCleanup);
      
      // Keep a record of cleanup
      await tursoService.setAppSetting('asyncstorage_cleanup_date', new Date().toISOString());
      
      console.log(`Cleaned up ${keysToCleanup.length} keys from AsyncStorage`);

    } catch (error) {
      console.error('AsyncStorage cleanup failed:', error);
      throw error;
    }
  }

  /**
   * Get migration status and statistics
   */
  public async getMigrationStatus(): Promise<{
    completed: boolean;
    migrationDate?: string;
    rollbackDate?: string;
    cleanupDate?: string;
  }> {
    try {
      const completed = await tursoService.getAppSetting('migration_completed') || false;
      const migrationDate = await tursoService.getAppSetting('migration_date');
      const rollbackDate = await tursoService.getAppSetting('rollback_date');
      const cleanupDate = await tursoService.getAppSetting('asyncstorage_cleanup_date');

      return {
        completed,
        migrationDate,
        rollbackDate,
        cleanupDate
      };
    } catch (error) {
      console.error('Failed to get migration status:', error);
      return { completed: false };
    }
  }
}

export const migrationService = MigrationService.getInstance();
