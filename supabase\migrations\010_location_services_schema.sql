-- Location Services Schema
-- This migration creates comprehensive location-based features for species identification and discovery

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "postgis";

-- User Locations Table
CREATE TABLE IF NOT EXISTS user_locations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    
    -- Location data
    latitude DECIMAL(10, 8) NOT NULL,
    longitude DECIMAL(11, 8) NOT NULL,
    altitude DECIMAL(8, 2),
    accuracy DECIMAL(8, 2),
    heading DECIMAL(5, 2),
    speed DECIMAL(8, 2),
    
    -- PostGIS geometry column for spatial queries
    geom GEOMETRY(POINT, 4326),
    
    -- Location context
    location_type VARCHAR(20) NOT NULL DEFAULT 'manual' CHECK (location_type IN ('gps', 'manual', 'network', 'passive')),
    location_source VARCHAR(30) NOT NULL DEFAULT 'user' CHECK (location_source IN ('user', 'identification', 'exploration', 'background')),
    
    -- Address information (optional)
    address_line1 TEXT,
    address_line2 TEXT,
    city VARCHAR(100),
    state_province VARCHAR(100),
    country VARCHAR(100),
    postal_code VARCHAR(20),
    
    -- Location metadata
    timezone VARCHAR(50),
    weather_conditions JSONB,
    elevation_data JSONB,
    
    -- Privacy and consent
    is_public BOOLEAN NOT NULL DEFAULT false,
    precision_level VARCHAR(20) DEFAULT 'exact' CHECK (precision_level IN ('exact', 'approximate', 'city', 'region', 'country')),
    
    -- Tracking
    recorded_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Species Locations Table (for species sighting locations)
CREATE TABLE IF NOT EXISTS species_locations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    species_id UUID NOT NULL, -- References species database
    
    -- Location data
    latitude DECIMAL(10, 8) NOT NULL,
    longitude DECIMAL(11, 8) NOT NULL,
    altitude DECIMAL(8, 2),
    geom GEOMETRY(POINT, 4326),
    
    -- Sighting information
    sighting_count INTEGER DEFAULT 1,
    last_sighting_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    confidence_level DECIMAL(3, 2) DEFAULT 0.5 CHECK (confidence_level >= 0 AND confidence_level <= 1),
    
    -- Environmental data
    habitat_type VARCHAR(50),
    season VARCHAR(20) CHECK (season IN ('spring', 'summer', 'fall', 'winter')),
    time_of_day VARCHAR(20) CHECK (time_of_day IN ('dawn', 'morning', 'midday', 'afternoon', 'dusk', 'night')),
    weather_conditions JSONB,
    
    -- Data quality
    data_source VARCHAR(30) DEFAULT 'user_report' CHECK (data_source IN ('user_report', 'scientific_study', 'citizen_science', 'expert_verification')),
    verification_status VARCHAR(20) DEFAULT 'unverified' CHECK (verification_status IN ('unverified', 'pending', 'verified', 'disputed')),
    
    -- Privacy
    is_public BOOLEAN NOT NULL DEFAULT true,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- User Identification Locations Table
CREATE TABLE IF NOT EXISTS user_identification_locations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    identification_id UUID NOT NULL, -- References species_identifications table
    
    -- Location data
    latitude DECIMAL(10, 8) NOT NULL,
    longitude DECIMAL(11, 8) NOT NULL,
    altitude DECIMAL(8, 2),
    accuracy DECIMAL(8, 2),
    geom GEOMETRY(POINT, 4326),
    
    -- Identification context
    identification_method VARCHAR(30) DEFAULT 'camera' CHECK (identification_method IN ('camera', 'audio', 'manual', 'ar')),
    environmental_conditions JSONB,
    
    -- Location metadata
    address_components JSONB,
    place_name VARCHAR(255),
    place_type VARCHAR(50),
    
    -- Privacy settings
    is_public BOOLEAN NOT NULL DEFAULT false,
    share_with_community BOOLEAN NOT NULL DEFAULT false,
    precision_level VARCHAR(20) DEFAULT 'exact',
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Location-Based Discoveries Table
CREATE TABLE IF NOT EXISTS location_discoveries (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    
    -- Discovery location
    latitude DECIMAL(10, 8) NOT NULL,
    longitude DECIMAL(11, 8) NOT NULL,
    geom GEOMETRY(POINT, 4326),
    
    -- Discovery details
    discovery_type VARCHAR(30) NOT NULL CHECK (discovery_type IN ('new_species', 'rare_sighting', 'habitat', 'migration_pattern', 'seasonal_change')),
    species_id UUID, -- Optional reference to species
    discovery_title VARCHAR(255) NOT NULL,
    discovery_description TEXT,
    
    -- Discovery metadata
    discovery_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    confidence_score DECIMAL(3, 2) DEFAULT 0.5,
    verification_status VARCHAR(20) DEFAULT 'pending',
    
    -- Media attachments
    photos JSONB DEFAULT '[]',
    audio_recordings JSONB DEFAULT '[]',
    videos JSONB DEFAULT '[]',
    
    -- Community interaction
    is_public BOOLEAN NOT NULL DEFAULT true,
    likes_count INTEGER DEFAULT 0,
    comments_count INTEGER DEFAULT 0,
    shares_count INTEGER DEFAULT 0,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Location Alerts Table
CREATE TABLE IF NOT EXISTS location_alerts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    
    -- Alert location (center point)
    latitude DECIMAL(10, 8) NOT NULL,
    longitude DECIMAL(11, 8) NOT NULL,
    geom GEOMETRY(POINT, 4326),
    
    -- Alert area
    radius_meters INTEGER NOT NULL DEFAULT 1000,
    alert_area GEOMETRY(POLYGON, 4326),
    
    -- Alert criteria
    alert_type VARCHAR(30) NOT NULL CHECK (alert_type IN ('species_sighting', 'rare_species', 'new_discovery', 'seasonal_activity', 'migration')),
    species_filter JSONB DEFAULT '{}', -- Species IDs or categories to watch for
    
    -- Alert settings
    alert_name VARCHAR(255) NOT NULL,
    alert_description TEXT,
    is_active BOOLEAN NOT NULL DEFAULT true,
    
    -- Notification preferences
    push_notifications BOOLEAN NOT NULL DEFAULT true,
    email_notifications BOOLEAN NOT NULL DEFAULT false,
    in_app_notifications BOOLEAN NOT NULL DEFAULT true,
    
    -- Alert frequency
    max_alerts_per_day INTEGER DEFAULT 5,
    quiet_hours_start TIME,
    quiet_hours_end TIME,
    
    -- Tracking
    last_triggered_at TIMESTAMP WITH TIME ZONE,
    trigger_count INTEGER DEFAULT 0,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Location-Based Recommendations Table
CREATE TABLE IF NOT EXISTS location_recommendations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    
    -- Recommended location
    latitude DECIMAL(10, 8) NOT NULL,
    longitude DECIMAL(11, 8) NOT NULL,
    geom GEOMETRY(POINT, 4326),
    
    -- Recommendation details
    recommendation_type VARCHAR(30) NOT NULL CHECK (recommendation_type IN ('exploration', 'species_hunting', 'photography', 'research', 'education')),
    title VARCHAR(255) NOT NULL,
    description TEXT,
    
    -- Recommendation scoring
    relevance_score DECIMAL(3, 2) DEFAULT 0.5,
    difficulty_level VARCHAR(20) DEFAULT 'moderate' CHECK (difficulty_level IN ('easy', 'moderate', 'challenging', 'expert')),
    estimated_duration INTERVAL,
    
    -- Species information
    expected_species JSONB DEFAULT '[]',
    species_count_estimate INTEGER,
    best_time_to_visit JSONB DEFAULT '{}',
    
    -- Practical information
    accessibility_info TEXT,
    parking_available BOOLEAN DEFAULT true,
    facilities JSONB DEFAULT '{}',
    entry_requirements TEXT,
    
    -- User interaction
    is_bookmarked BOOLEAN DEFAULT false,
    is_visited BOOLEAN DEFAULT false,
    user_rating DECIMAL(2, 1) CHECK (user_rating >= 1 AND user_rating <= 5),
    user_notes TEXT,
    
    -- Recommendation metadata
    generated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE,
    recommendation_source VARCHAR(30) DEFAULT 'algorithm',
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Geofences Table
CREATE TABLE IF NOT EXISTS geofences (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    
    -- Geofence definition
    name VARCHAR(255) NOT NULL,
    description TEXT,
    geofence_type VARCHAR(30) NOT NULL CHECK (geofence_type IN ('personal', 'public', 'protected_area', 'research_site', 'hotspot')),
    
    -- Geometry
    center_latitude DECIMAL(10, 8) NOT NULL,
    center_longitude DECIMAL(11, 8) NOT NULL,
    radius_meters INTEGER,
    boundary GEOMETRY(POLYGON, 4326),
    
    -- Geofence properties
    is_active BOOLEAN NOT NULL DEFAULT true,
    entry_action VARCHAR(30) DEFAULT 'notify' CHECK (entry_action IN ('notify', 'log', 'suggest', 'none')),
    exit_action VARCHAR(30) DEFAULT 'log' CHECK (exit_action IN ('notify', 'log', 'suggest', 'none')),
    
    -- Metadata
    tags JSONB DEFAULT '[]',
    properties JSONB DEFAULT '{}',
    
    -- Access control
    is_public BOOLEAN NOT NULL DEFAULT false,
    created_by UUID REFERENCES auth.users(id),
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Location Analytics Table
CREATE TABLE IF NOT EXISTS location_analytics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    
    -- Analytics period
    date DATE NOT NULL,
    
    -- Location metrics
    total_locations_recorded INTEGER DEFAULT 0,
    unique_locations_visited INTEGER DEFAULT 0,
    total_distance_traveled DECIMAL(10, 2) DEFAULT 0, -- in kilometers
    average_accuracy DECIMAL(8, 2),
    
    -- Activity metrics
    identifications_with_location INTEGER DEFAULT 0,
    discoveries_made INTEGER DEFAULT 0,
    public_locations_shared INTEGER DEFAULT 0,
    
    -- Geographic distribution
    countries_visited JSONB DEFAULT '[]',
    states_provinces_visited JSONB DEFAULT '[]',
    cities_visited JSONB DEFAULT '[]',
    
    -- Environmental diversity
    habitat_types_explored JSONB DEFAULT '[]',
    elevation_range JSONB DEFAULT '{}',
    climate_zones_visited JSONB DEFAULT '[]',
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(user_id, date)
);

-- Create spatial indexes for performance
CREATE INDEX IF NOT EXISTS idx_user_locations_geom ON user_locations USING GIST (geom);
CREATE INDEX IF NOT EXISTS idx_user_locations_user_id ON user_locations(user_id);
CREATE INDEX IF NOT EXISTS idx_user_locations_recorded_at ON user_locations(recorded_at);

CREATE INDEX IF NOT EXISTS idx_species_locations_geom ON species_locations USING GIST (geom);
CREATE INDEX IF NOT EXISTS idx_species_locations_species_id ON species_locations(species_id);
CREATE INDEX IF NOT EXISTS idx_species_locations_sighting_date ON species_locations(last_sighting_date);

CREATE INDEX IF NOT EXISTS idx_user_identification_locations_geom ON user_identification_locations USING GIST (geom);
CREATE INDEX IF NOT EXISTS idx_user_identification_locations_user_id ON user_identification_locations(user_id);
CREATE INDEX IF NOT EXISTS idx_user_identification_locations_identification_id ON user_identification_locations(identification_id);

CREATE INDEX IF NOT EXISTS idx_location_discoveries_geom ON location_discoveries USING GIST (geom);
CREATE INDEX IF NOT EXISTS idx_location_discoveries_user_id ON location_discoveries(user_id);
CREATE INDEX IF NOT EXISTS idx_location_discoveries_type ON location_discoveries(discovery_type);
CREATE INDEX IF NOT EXISTS idx_location_discoveries_public ON location_discoveries(is_public);

CREATE INDEX IF NOT EXISTS idx_location_alerts_geom ON location_alerts USING GIST (geom);
CREATE INDEX IF NOT EXISTS idx_location_alerts_user_id ON location_alerts(user_id);
CREATE INDEX IF NOT EXISTS idx_location_alerts_active ON location_alerts(is_active);

CREATE INDEX IF NOT EXISTS idx_location_recommendations_geom ON location_recommendations USING GIST (geom);
CREATE INDEX IF NOT EXISTS idx_location_recommendations_user_id ON location_recommendations(user_id);
CREATE INDEX IF NOT EXISTS idx_location_recommendations_type ON location_recommendations(recommendation_type);

CREATE INDEX IF NOT EXISTS idx_geofences_boundary ON geofences USING GIST (boundary);
CREATE INDEX IF NOT EXISTS idx_geofences_user_id ON geofences(user_id);
CREATE INDEX IF NOT EXISTS idx_geofences_active ON geofences(is_active);

CREATE INDEX IF NOT EXISTS idx_location_analytics_user_date ON location_analytics(user_id, date);

-- Create triggers for updating geometry columns
CREATE OR REPLACE FUNCTION update_geom_from_lat_lng()
RETURNS TRIGGER AS $$
BEGIN
    NEW.geom = ST_SetSRID(ST_MakePoint(NEW.longitude, NEW.latitude), 4326);
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_user_locations_geom
    BEFORE INSERT OR UPDATE ON user_locations
    FOR EACH ROW EXECUTE FUNCTION update_geom_from_lat_lng();

CREATE TRIGGER trigger_species_locations_geom
    BEFORE INSERT OR UPDATE ON species_locations
    FOR EACH ROW EXECUTE FUNCTION update_geom_from_lat_lng();

CREATE TRIGGER trigger_user_identification_locations_geom
    BEFORE INSERT OR UPDATE ON user_identification_locations
    FOR EACH ROW EXECUTE FUNCTION update_geom_from_lat_lng();

CREATE TRIGGER trigger_location_discoveries_geom
    BEFORE INSERT OR UPDATE ON location_discoveries
    FOR EACH ROW EXECUTE FUNCTION update_geom_from_lat_lng();

CREATE TRIGGER trigger_location_alerts_geom
    BEFORE INSERT OR UPDATE ON location_alerts
    FOR EACH ROW EXECUTE FUNCTION update_geom_from_lat_lng();

CREATE TRIGGER trigger_location_recommendations_geom
    BEFORE INSERT OR UPDATE ON location_recommendations
    FOR EACH ROW EXECUTE FUNCTION update_geom_from_lat_lng();

-- Create updated_at triggers
CREATE TRIGGER update_user_locations_updated_at BEFORE UPDATE ON user_locations FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_species_locations_updated_at BEFORE UPDATE ON species_locations FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_location_discoveries_updated_at BEFORE UPDATE ON location_discoveries FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_location_alerts_updated_at BEFORE UPDATE ON location_alerts FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_location_recommendations_updated_at BEFORE UPDATE ON location_recommendations FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_geofences_updated_at BEFORE UPDATE ON geofences FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Enable RLS on all location tables
ALTER TABLE user_locations ENABLE ROW LEVEL SECURITY;
ALTER TABLE species_locations ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_identification_locations ENABLE ROW LEVEL SECURITY;
ALTER TABLE location_discoveries ENABLE ROW LEVEL SECURITY;
ALTER TABLE location_alerts ENABLE ROW LEVEL SECURITY;
ALTER TABLE location_recommendations ENABLE ROW LEVEL SECURITY;
ALTER TABLE geofences ENABLE ROW LEVEL SECURITY;
ALTER TABLE location_analytics ENABLE ROW LEVEL SECURITY;

-- User Locations Policies
CREATE POLICY "Users can view own locations" ON user_locations
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own locations" ON user_locations
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own locations" ON user_locations
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own locations" ON user_locations
    FOR DELETE USING (auth.uid() = user_id);

CREATE POLICY "Service role can manage user locations" ON user_locations
    FOR ALL USING (auth.role() = 'service_role');

-- Species Locations Policies
CREATE POLICY "Public can view public species locations" ON species_locations
    FOR SELECT USING (is_public = true);

CREATE POLICY "Service role can manage species locations" ON species_locations
    FOR ALL USING (auth.role() = 'service_role');

CREATE POLICY "Researchers can view all species locations" ON species_locations
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM user_profiles
            WHERE id = auth.uid()
            AND role IN ('researcher', 'admin', 'super_admin')
        )
    );

-- User Identification Locations Policies
CREATE POLICY "Users can view own identification locations" ON user_identification_locations
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own identification locations" ON user_identification_locations
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Public can view shared identification locations" ON user_identification_locations
    FOR SELECT USING (share_with_community = true);

CREATE POLICY "Service role can manage identification locations" ON user_identification_locations
    FOR ALL USING (auth.role() = 'service_role');

-- Location Discoveries Policies
CREATE POLICY "Users can view own discoveries" ON location_discoveries
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own discoveries" ON location_discoveries
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own discoveries" ON location_discoveries
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Public can view public discoveries" ON location_discoveries
    FOR SELECT USING (is_public = true);

CREATE POLICY "Service role can manage discoveries" ON location_discoveries
    FOR ALL USING (auth.role() = 'service_role');

-- Location Alerts Policies
CREATE POLICY "Users can manage own alerts" ON location_alerts
    FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Service role can manage alerts" ON location_alerts
    FOR ALL USING (auth.role() = 'service_role');

-- Location Recommendations Policies
CREATE POLICY "Users can view own recommendations" ON location_recommendations
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can update own recommendations" ON location_recommendations
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Service role can manage recommendations" ON location_recommendations
    FOR ALL USING (auth.role() = 'service_role');

-- Geofences Policies
CREATE POLICY "Users can manage own geofences" ON geofences
    FOR ALL USING (auth.uid() = user_id OR auth.uid() = created_by);

CREATE POLICY "Public can view public geofences" ON geofences
    FOR SELECT USING (is_public = true);

CREATE POLICY "Service role can manage geofences" ON geofences
    FOR ALL USING (auth.role() = 'service_role');

-- Location Analytics Policies
CREATE POLICY "Users can view own analytics" ON location_analytics
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Service role can manage analytics" ON location_analytics
    FOR ALL USING (auth.role() = 'service_role');

CREATE POLICY "Admins can view analytics" ON location_analytics
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM user_profiles
            WHERE id = auth.uid()
            AND role IN ('admin', 'super_admin')
        )
    );

-- Create SQL functions for location services

-- Function to find species within radius
CREATE OR REPLACE FUNCTION find_species_within_radius(
    center_lat DECIMAL,
    center_lng DECIMAL,
    radius_meters INTEGER
)
RETURNS TABLE (
    species_id UUID,
    species_name VARCHAR,
    common_name VARCHAR,
    species_type VARCHAR,
    latitude DECIMAL,
    longitude DECIMAL,
    last_sighting_date TIMESTAMP WITH TIME ZONE,
    sighting_count INTEGER,
    confidence_level DECIMAL
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        sl.species_id,
        COALESCE(s.scientific_name, 'Unknown') as species_name,
        COALESCE(s.common_name, 'Unknown') as common_name,
        COALESCE(s.species_type, 'unknown') as species_type,
        sl.latitude,
        sl.longitude,
        sl.last_sighting_date,
        sl.sighting_count,
        sl.confidence_level
    FROM species_locations sl
    LEFT JOIN species s ON sl.species_id = s.id
    WHERE sl.is_public = true
    AND ST_DWithin(
        sl.geom,
        ST_SetSRID(ST_MakePoint(center_lng, center_lat), 4326)::geography,
        radius_meters
    )
    ORDER BY ST_Distance(
        sl.geom,
        ST_SetSRID(ST_MakePoint(center_lng, center_lat), 4326)::geography
    );
END;
$$ LANGUAGE plpgsql;

-- Function to check if point is within user's geofences
CREATE OR REPLACE FUNCTION check_point_in_geofences(
    point_lat DECIMAL,
    point_lng DECIMAL,
    check_user_id UUID
)
RETURNS TABLE (
    id UUID,
    name VARCHAR,
    geofence_type VARCHAR,
    entry_action VARCHAR,
    exit_action VARCHAR,
    properties JSONB
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        g.id,
        g.name,
        g.geofence_type,
        g.entry_action,
        g.exit_action,
        g.properties
    FROM geofences g
    WHERE g.is_active = true
    AND (g.user_id = check_user_id OR g.is_public = true)
    AND (
        (g.boundary IS NOT NULL AND ST_Contains(
            g.boundary,
            ST_SetSRID(ST_MakePoint(point_lng, point_lat), 4326)
        ))
        OR
        (g.radius_meters IS NOT NULL AND ST_DWithin(
            ST_SetSRID(ST_MakePoint(g.center_longitude, g.center_latitude), 4326)::geography,
            ST_SetSRID(ST_MakePoint(point_lng, point_lat), 4326)::geography,
            g.radius_meters
        ))
    );
END;
$$ LANGUAGE plpgsql;

-- Function to update location analytics
CREATE OR REPLACE FUNCTION update_location_analytics(
    user_uuid UUID,
    analytics_date DATE,
    new_location JSONB
)
RETURNS BOOLEAN AS $$
DECLARE
    existing_record RECORD;
    new_distance DECIMAL := 0;
BEGIN
    -- Get existing analytics record
    SELECT * INTO existing_record
    FROM location_analytics
    WHERE user_id = user_uuid AND date = analytics_date;

    IF existing_record IS NOT NULL THEN
        -- Calculate distance from last location if available
        IF existing_record.last_location IS NOT NULL THEN
            SELECT ST_Distance(
                ST_SetSRID(ST_MakePoint(
                    (existing_record.last_location->>'longitude')::DECIMAL,
                    (existing_record.last_location->>'latitude')::DECIMAL
                ), 4326)::geography,
                ST_SetSRID(ST_MakePoint(
                    (new_location->>'longitude')::DECIMAL,
                    (new_location->>'latitude')::DECIMAL
                ), 4326)::geography
            ) / 1000 INTO new_distance; -- Convert to km
        END IF;

        -- Update existing record
        UPDATE location_analytics
        SET
            total_locations_recorded = total_locations_recorded + 1,
            total_distance_traveled = total_distance_traveled + new_distance,
            last_location = new_location,
            updated_at = NOW()
        WHERE user_id = user_uuid AND date = analytics_date;
    ELSE
        -- Create new record
        INSERT INTO location_analytics (
            user_id,
            date,
            total_locations_recorded,
            unique_locations_visited,
            total_distance_traveled,
            last_location
        ) VALUES (
            user_uuid,
            analytics_date,
            1,
            1,
            0,
            new_location
        );
    END IF;

    RETURN TRUE;
END;
$$ LANGUAGE plpgsql;
