import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  Switch,
  Alert,
  Dimensions,
  Platform,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Settings as SettingsIcon, User, Bell, Shield, Camera, Globe, Palette, Database, CircleHelp as HelpCircle, Info, ChevronRight, Crown, Zap, Eye, Target, Star, Smartphone, RefreshCw, FileSliders as Sliders, Download, Upload, Trash2, HardDrive, Type } from 'lucide-react-native';
import { useSubscription } from '@/components/SubscriptionContext';
import SubscriptionModal from '@/components/SubscriptionModal';
import SettingsPopup from '@/components/SettingsPopup';
import LargeTextModal from '@/components/LargeTextModal';
import NotificationService from '@/components/NotificationService';
import HapticService from '@/components/HapticService';
import DataManager from '@/components/DataManager';

const { width } = Dimensions.get('window');

interface UserSettings {
  theme: 'light' | 'dark' | 'auto';
  notifications: {
    enabled: boolean;
    scanResults: boolean;
    achievements: boolean;
    community: boolean;
  };
  camera: {
    autoFocus: boolean;
    flashMode: 'auto' | 'on' | 'off';
    saveOriginals: boolean;
    imageQuality: 'low' | 'medium' | 'high';
  };
  identification: {
    confidenceThreshold: number;
    maxAlternatives: number;
    enableOfflineMode: boolean;
    autoSave: boolean;
  };
  privacy: {
    shareLocation: boolean;
    publicProfile: boolean;
    dataCollection: boolean;
  };
  accessibility: {
    soundEnabled: boolean;
    hapticFeedback: boolean;
    largeText: boolean;
    highContrast: boolean;
  };
  language: string;
  units: 'metric' | 'imperial';
}

const defaultSettings: UserSettings = {
  theme: 'auto',
  notifications: {
    enabled: true,
    scanResults: true,
    achievements: true,
    community: false,
  },
  camera: {
    autoFocus: true,
    flashMode: 'auto',
    saveOriginals: true,
    imageQuality: 'high',
  },
  identification: {
    confidenceThreshold: 80,
    maxAlternatives: 3,
    enableOfflineMode: false,
    autoSave: true,
  },
  privacy: {
    shareLocation: false,
    publicProfile: false,
    dataCollection: true,
  },
  accessibility: {
    soundEnabled: true,
    hapticFeedback: true,
    largeText: false,
    highContrast: false,
  },
  language: 'en',
  units: 'metric',
};

export default function SettingsScreen() {
  const [settings, setSettings] = useState<UserSettings>(defaultSettings);
  const [loading, setLoading] = useState(true);
  const [showSubscriptionModal, setShowSubscriptionModal] = useState(false);
  const [showSettingsPopup, setShowSettingsPopup] = useState(false);
  const [showLargeTextModal, setShowLargeTextModal] = useState(false);
  const [storageInfo, setStorageInfo] = useState<any>(null);
  const { subscription } = useSubscription();

  const notificationService = NotificationService.getInstance();
  const hapticService = HapticService.getInstance();
  const dataManager = DataManager.getInstance();

  useEffect(() => {
    loadSettings();
    loadStorageInfo();
  }, []);

  const loadSettings = async () => {
    try {
      const savedSettings = await AsyncStorage.getItem('userSettings');
      if (savedSettings) {
        const parsed = { ...defaultSettings, ...JSON.parse(savedSettings) };
        setSettings(parsed);
        
        // Initialize services with loaded settings
        hapticService.setEnabled(parsed.accessibility.hapticFeedback);
      }
    } catch (error) {
      console.error('Failed to load settings:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadStorageInfo = async () => {
    try {
      const info = await dataManager.getStorageInfo();
      setStorageInfo(info);
    } catch (error) {
      console.error('Failed to load storage info:', error);
    }
  };

  const saveSettings = async (newSettings: UserSettings) => {
    try {
      await AsyncStorage.setItem('userSettings', JSON.stringify(newSettings));
      setSettings(newSettings);
      
      // Update services
      hapticService.setEnabled(newSettings.accessibility.hapticFeedback);
      
      // Trigger haptic feedback for settings change
      await hapticService.triggerSelectionFeedback();
    } catch (error) {
      console.error('Failed to save settings:', error);
      Alert.alert('Error', 'Failed to save settings. Please try again.');
    }
  };

  const testNotification = async () => {
    try {
      await hapticService.triggerImpactFeedback('medium');
      const success = await notificationService.sendNotification({
        title: 'Test Notification 🔔',
        body: 'Notifications are working correctly!',
        tag: 'test',
      });
      
      if (success) {
        Alert.alert('Success', 'Test notification sent successfully!');
      } else {
        Alert.alert('Permission Required', 'Please enable notifications in your browser settings.');
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to send test notification.');
    }
  };

  const exportData = async () => {
    try {
      await hapticService.triggerImpactFeedback('light');
      await dataManager.backupToFile();
      Alert.alert('Success', 'Data exported successfully!');
    } catch (error) {
      await hapticService.triggerNotificationFeedback('error');
      Alert.alert('Error', 'Failed to export data.');
    }
  };

  const clearCache = async () => {
    Alert.alert(
      'Clear Cache',
      'This will clear all cached data including images and temporary files. Continue?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Clear',
          style: 'destructive',
          onPress: async () => {
            try {
              await hapticService.triggerImpactFeedback('medium');
              await dataManager.clearCache();
              await loadStorageInfo();
              await hapticService.triggerNotificationFeedback('success');
              Alert.alert('Success', 'Cache cleared successfully!');
            } catch (error) {
              await hapticService.triggerNotificationFeedback('error');
              Alert.alert('Error', 'Failed to clear cache.');
            }
          },
        },
      ]
    );
  };

  const resetAllData = async () => {
    Alert.alert(
      'Reset All Data',
      'This will permanently delete ALL your data including settings, collections, and scan history. This action cannot be undone.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Reset All',
          style: 'destructive',
          onPress: async () => {
            try {
              await hapticService.triggerNotificationFeedback('warning');
              await dataManager.clearAllData();
              await hapticService.triggerNotificationFeedback('success');
              Alert.alert('Complete', 'All data has been reset. The app will restart.');
              // In a real app, you might restart or navigate to onboarding
            } catch (error) {
              await hapticService.triggerNotificationFeedback('error');
              Alert.alert('Error', 'Failed to reset data.');
            }
          },
        },
      ]
    );
  };

  const resetOnboarding = async () => {
    Alert.alert(
      'Reset Walkthrough',
      'This will show the app walkthrough again on next launch. Continue?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Reset',
          onPress: async () => {
            try {
              await AsyncStorage.removeItem('hasCompletedOnboarding');
              await hapticService.triggerNotificationFeedback('success');
              Alert.alert('Success', 'Walkthrough will be shown on next app launch');
            } catch (error) {
              Alert.alert('Error', 'Failed to reset walkthrough');
            }
          },
        },
      ]
    );
  };

  const getSubscriptionBadge = () => {
    switch (subscription.tier) {
      case 'pro':
        return { text: 'PRO', color: '#3B82F6', icon: Zap };
      case 'expert':
        return { text: 'EXPERT', color: '#8B5CF6', icon: Crown };
      default:
        return { text: 'FREE', color: '#6B7280', icon: Star };
    }
  };

  const SettingSection = ({ title, children }: { title: string; children: React.ReactNode }) => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>{title}</Text>
      <View style={styles.sectionContent}>{children}</View>
    </View>
  );

  const SettingRow = ({
    icon: Icon,
    title,
    subtitle,
    value,
    onPress,
    showChevron = true,
    rightComponent,
  }: {
    icon: any;
    title: string;
    subtitle?: string;
    value?: string;
    onPress?: () => void;
    showChevron?: boolean;
    rightComponent?: React.ReactNode;
  }) => (
    <TouchableOpacity 
      style={styles.settingRow} 
      onPress={async () => {
        await hapticService.triggerSelectionFeedback();
        onPress?.();
      }} 
      disabled={!onPress}>
      <View style={styles.settingLeft}>
        <View style={styles.settingIcon}>
          <Icon size={20} color="#6B7280" />
        </View>
        <View style={styles.settingText}>
          <Text style={styles.settingTitle}>{title}</Text>
          {subtitle && <Text style={styles.settingSubtitle}>{subtitle}</Text>}
          {value && <Text style={styles.settingValue}>{value}</Text>}
        </View>
      </View>
      <View style={styles.settingRight}>
        {rightComponent}
        {showChevron && onPress && <ChevronRight size={16} color="#9CA3AF" />}
      </View>
    </TouchableOpacity>
  );

  const SwitchRow = ({
    icon: Icon,
    title,
    subtitle,
    value,
    onValueChange,
  }: {
    icon: any;
    title: string;
    subtitle?: string;
    value: boolean;
    onValueChange: (value: boolean) => void;
  }) => (
    <SettingRow
      icon={Icon}
      title={title}
      subtitle={subtitle}
      showChevron={false}
      rightComponent={
        <Switch
          value={value}
          onValueChange={async (newValue) => {
            await hapticService.triggerSelectionFeedback();
            onValueChange(newValue);
          }}
          trackColor={{ false: '#E5E7EB', true: '#22C55E' }}
          thumbColor={value ? '#FFFFFF' : '#FFFFFF'}
        />
      }
    />
  );

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <RefreshCw size={32} color="#6B7280" />
          <Text style={styles.loadingText}>Loading settings...</Text>
        </View>
      </SafeAreaView>
    );
  }

  const badge = getSubscriptionBadge();
  const BadgeIcon = badge.icon;

  return (
    <SafeAreaView style={styles.container}>
      <LinearGradient colors={['#F8FAFC', '#F1F5F9', '#E2E8F0']} style={styles.gradient}>
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.title}>Settings</Text>
          <Text style={styles.subtitle}>Customize your BioScan experience</Text>
        </View>

        <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
          {/* Subscription Status */}
          <SettingSection title="Subscription">
            <TouchableOpacity
              style={styles.subscriptionCard}
              onPress={async () => {
                await hapticService.triggerImpactFeedback('light');
                setShowSubscriptionModal(true);
              }}>
              <LinearGradient
                colors={[badge.color, badge.color + 'CC']}
                style={styles.subscriptionGradient}>
                <View style={styles.subscriptionHeader}>
                  <BadgeIcon size={24} color="#FFFFFF" />
                  <Text style={styles.subscriptionTier}>{badge.text}</Text>
                </View>
                <Text style={styles.subscriptionDescription}>
                  {subscription.tier === 'free'
                    ? 'Upgrade to unlock unlimited scans and premium features'
                    : subscription.tier === 'pro'
                    ? 'Enjoying Pro features? Consider Expert for advanced tools'
                    : 'You have access to all premium features'}
                </Text>
                <View style={styles.subscriptionAction}>
                  <Text style={styles.subscriptionActionText}>
                    {subscription.tier === 'free' ? 'Upgrade Now' : 'Manage Subscription'}
                  </Text>
                  <ChevronRight size={16} color="#FFFFFF" />
                </View>
              </LinearGradient>
            </TouchableOpacity>
          </SettingSection>

          {/* AI Identification */}
          <SettingSection title="AI Identification">
            <SettingRow
              icon={Zap}
              title="Gemini AI Status"
              subtitle="AI-powered species identification is active"
              value="Connected"
            />
            <SettingRow
              icon={Sliders}
              title="Camera & Quality Settings"
              subtitle={`Confidence: ${settings.identification.confidenceThreshold}% • Quality: ${settings.camera.imageQuality.charAt(0).toUpperCase() + settings.camera.imageQuality.slice(1)}`}
              onPress={() => setShowSettingsPopup(true)}
            />
          </SettingSection>

          {/* Notifications */}
          <SettingSection title="Notifications">
            <SwitchRow
              icon={Bell}
              title="Enable Notifications"
              subtitle="Receive app notifications"
              value={settings.notifications.enabled}
              onValueChange={(value) => {
                const newSettings = {
                  ...settings,
                  notifications: { ...settings.notifications, enabled: value },
                };
                saveSettings(newSettings);
              }}
            />
            <SwitchRow
              icon={Zap}
              title="Scan Results"
              subtitle="Notify when identification is complete"
              value={settings.notifications.scanResults}
              onValueChange={(value) => {
                const newSettings = {
                  ...settings,
                  notifications: { ...settings.notifications, scanResults: value },
                };
                saveSettings(newSettings);
              }}
            />
            <SwitchRow
              icon={Star}
              title="Achievements"
              subtitle="Notify about new achievements and milestones"
              value={settings.notifications.achievements}
              onValueChange={(value) => {
                const newSettings = {
                  ...settings,
                  notifications: { ...settings.notifications, achievements: value },
                };
                saveSettings(newSettings);
              }}
            />
            <SettingRow
              icon={Bell}
              title="Test Notification"
              subtitle="Send a test notification to verify settings"
              onPress={testNotification}
            />
          </SettingSection>

          {/* Privacy & Security */}
          <SettingSection title="Privacy & Security">
            <SwitchRow
              icon={Globe}
              title="Share Location"
              subtitle="Include location data with scans"
              value={settings.privacy.shareLocation}
              onValueChange={(value) => {
                const newSettings = {
                  ...settings,
                  privacy: { ...settings.privacy, shareLocation: value },
                };
                saveSettings(newSettings);
              }}
            />
            <SwitchRow
              icon={User}
              title="Public Profile"
              subtitle="Make your profile visible to other users"
              value={settings.privacy.publicProfile}
              onValueChange={(value) => {
                const newSettings = {
                  ...settings,
                  privacy: { ...settings.privacy, publicProfile: value },
                };
                saveSettings(newSettings);
              }}
            />
            <SwitchRow
              icon={Database}
              title="Data Collection"
              subtitle="Help improve the app with anonymous usage data"
              value={settings.privacy.dataCollection}
              onValueChange={(value) => {
                const newSettings = {
                  ...settings,
                  privacy: { ...settings.privacy, dataCollection: value },
                };
                saveSettings(newSettings);
              }}
            />
          </SettingSection>

          {/* Accessibility */}
          <SettingSection title="Accessibility">
            <SwitchRow
              icon={Smartphone}
              title="Haptic Feedback"
              subtitle="Vibrate for touch interactions"
              value={settings.accessibility.hapticFeedback}
              onValueChange={(value) => {
                const newSettings = {
                  ...settings,
                  accessibility: { ...settings.accessibility, hapticFeedback: value },
                };
                saveSettings(newSettings);
              }}
            />
            <SettingRow
              icon={Type}
              title="Large Text"
              subtitle="Use larger text throughout the app"
              value={settings.accessibility.largeText ? 'Enabled' : 'Disabled'}
              onPress={() => setShowLargeTextModal(true)}
            />
          </SettingSection>

          {/* Data Management */}
          <SettingSection title="Data Management">
            <SettingRow
              icon={HardDrive}
              title="Storage Usage"
              subtitle={storageInfo ? `Total: ${dataManager.formatBytes(storageInfo.totalSize)}` : 'Calculating...'}
              onPress={loadStorageInfo}
            />
            <SettingRow
              icon={Download}
              title="Export Data"
              subtitle="Download your data as a backup file"
              onPress={exportData}
            />
            <SettingRow
              icon={Trash2}
              title="Clear Cache"
              subtitle={storageInfo ? `Free up ${dataManager.formatBytes(storageInfo.cacheSize)}` : 'Clear cached data'}
              onPress={clearCache}
            />
            <SettingRow
              icon={RefreshCw}
              title="Reset All Data"
              subtitle="Permanently delete all app data"
              onPress={resetAllData}
            />
          </SettingSection>

          {/* Support */}
          <SettingSection title="Support">
            <SettingRow
              icon={HelpCircle}
              title="Help & FAQ"
              subtitle="Get help and find answers"
              onPress={() => Alert.alert('Help', 'Help documentation would open here')}
            />
            <SettingRow
              icon={Info}
              title="About"
              subtitle="App version and information"
              value={`v${process.env.EXPO_PUBLIC_APP_VERSION || '1.0.0'}`}
              onPress={() => Alert.alert('About BioScan', `BioScan v${process.env.EXPO_PUBLIC_APP_VERSION || '1.0.0'}\nBuilt with Expo and React Native\nPowered by Google Gemini AI`)}
            />
            <SettingRow
              icon={RefreshCw}
              title="Reset Walkthrough"
              subtitle="Show the app tutorial again"
              onPress={resetOnboarding}
            />
          </SettingSection>

          <View style={styles.bottomSpacing} />
        </ScrollView>

        <SubscriptionModal
          visible={showSubscriptionModal}
          onClose={() => setShowSubscriptionModal(false)}
        />

        <SettingsPopup
          visible={showSettingsPopup}
          onClose={() => setShowSettingsPopup(false)}
          confidenceThreshold={settings.identification.confidenceThreshold}
          imageQuality={settings.camera.imageQuality}
          onConfidenceChange={(value) => {
            const newSettings = {
              ...settings,
              identification: { ...settings.identification, confidenceThreshold: value },
            };
            saveSettings(newSettings);
          }}
          onImageQualityChange={(quality) => {
            const newSettings = {
              ...settings,
              camera: { ...settings.camera, imageQuality: quality },
            };
            saveSettings(newSettings);
          }}
        />

        <LargeTextModal
          visible={showLargeTextModal}
          onClose={() => setShowLargeTextModal(false)}
          largeTextEnabled={settings.accessibility.largeText}
          onToggle={(enabled) => {
            const newSettings = {
              ...settings,
              accessibility: { ...settings.accessibility, largeText: enabled },
            };
            saveSettings(newSettings);
          }}
        />
      </LinearGradient>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8FAFC',
  },
  gradient: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    gap: 16,
  },
  loadingText: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
  },
  header: {
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 16,
  },
  title: {
    fontSize: 32,
    fontFamily: 'Inter-Bold',
    color: '#1E293B',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
  },
  scrollView: {
    flex: 1,
  },
  section: {
    marginBottom: 32,
  },
  sectionTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: '#374151',
    marginBottom: 12,
    paddingHorizontal: 20,
  },
  sectionContent: {
    backgroundColor: '#FFFFFF',
    marginHorizontal: 20,
    borderRadius: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  subscriptionCard: {
    marginHorizontal: 20,
    borderRadius: 16,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 8,
  },
  subscriptionGradient: {
    padding: 20,
  },
  subscriptionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
    gap: 8,
  },
  subscriptionTier: {
    fontSize: 18,
    fontFamily: 'Inter-Bold',
    color: '#FFFFFF',
  },
  subscriptionDescription: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#FFFFFF',
    opacity: 0.9,
    lineHeight: 20,
    marginBottom: 16,
  },
  subscriptionAction: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  subscriptionActionText: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#FFFFFF',
  },
  settingRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
  },
  settingLeft: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  settingIcon: {
    width: 40,
    height: 40,
    borderRadius: 10,
    backgroundColor: '#F3F4F6',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  settingText: {
    flex: 1,
  },
  settingTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    marginBottom: 2,
  },
  settingSubtitle: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    lineHeight: 18,
  },
  settingValue: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#374151',
    marginTop: 2,
  },
  settingRight: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  bottomSpacing: {
    height: 100,
  },
});