import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  Modal,
  ScrollView,
} from 'react-native';
import {
  Shield,
  ShieldCheck,
  ShieldAlert,
  Lock,
  Eye,
  EyeOff,
  Info,
  AlertTriangle,
  CheckCircle,
  XCircle,
} from 'lucide-react-native';
import { FraudDetectionService } from '../services/FraudDetectionService';

interface PaymentSecurityIndicatorProps {
  visible?: boolean;
  onSecurityCheck?: (isSecure: boolean) => void;
}

interface SecurityStatus {
  isSecure: boolean;
  riskLevel: 'low' | 'medium' | 'high';
  checks: SecurityCheck[];
  deviceFingerprint: string;
  recentAttempts: number;
}

interface SecurityCheck {
  name: string;
  status: 'pass' | 'warning' | 'fail';
  description: string;
  icon: any;
}

export const PaymentSecurityIndicator: React.FC<PaymentSecurityIndicatorProps> = ({
  visible = true,
  onSecurityCheck,
}) => {
  const [securityStatus, setSecurityStatus] = useState<SecurityStatus | null>(null);
  const [loading, setLoading] = useState(true);
  const [showDetails, setShowDetails] = useState(false);
  const [detailsModalVisible, setDetailsModalVisible] = useState(false);

  useEffect(() => {
    checkSecurity();
  }, []);

  const checkSecurity = async () => {
    try {
      setLoading(true);
      
      const [isAllowed, fraudStats] = await Promise.all([
        FraudDetectionService.isPaymentAllowed(),
        FraudDetectionService.getFraudStats()
      ]);

      const checks: SecurityCheck[] = [
        {
          name: 'Device Security',
          status: fraudStats.deviceFingerprint !== 'unknown_device' ? 'pass' : 'warning',
          description: 'Device has been verified and recognized',
          icon: Shield
        },
        {
          name: 'Rate Limiting',
          status: fraudStats.recentAttempts < 3 ? 'pass' : fraudStats.recentAttempts < 5 ? 'warning' : 'fail',
          description: `${fraudStats.recentAttempts} recent payment attempts`,
          icon: Lock
        },
        {
          name: 'Account History',
          status: fraudStats.lastValidation ? 'pass' : 'warning',
          description: fraudStats.lastValidation 
            ? `Last validation: Risk score ${fraudStats.lastValidation.riskScore}`
            : 'No previous payment history',
          icon: CheckCircle
        },
        {
          name: 'Network Security',
          status: 'pass', // This would check network conditions in a real implementation
          description: 'Secure connection established',
          icon: ShieldCheck
        }
      ];

      const failedChecks = checks.filter(check => check.status === 'fail').length;
      const warningChecks = checks.filter(check => check.status === 'warning').length;

      let riskLevel: 'low' | 'medium' | 'high' = 'low';
      if (failedChecks > 0 || !isAllowed) {
        riskLevel = 'high';
      } else if (warningChecks > 1) {
        riskLevel = 'medium';
      }

      const status: SecurityStatus = {
        isSecure: isAllowed && failedChecks === 0,
        riskLevel,
        checks,
        deviceFingerprint: fraudStats.deviceFingerprint,
        recentAttempts: fraudStats.recentAttempts
      };

      setSecurityStatus(status);
      
      if (onSecurityCheck) {
        onSecurityCheck(status.isSecure);
      }

    } catch (error) {
      console.error('Error checking security:', error);
      
      // Fallback security status
      const fallbackStatus: SecurityStatus = {
        isSecure: true, // Allow on error to avoid blocking legitimate users
        riskLevel: 'medium',
        checks: [{
          name: 'Security Check',
          status: 'warning',
          description: 'Unable to verify security status',
          icon: AlertTriangle
        }],
        deviceFingerprint: 'unknown',
        recentAttempts: 0
      };
      
      setSecurityStatus(fallbackStatus);
      
      if (onSecurityCheck) {
        onSecurityCheck(true);
      }
    } finally {
      setLoading(false);
    }
  };

  const getSecurityColor = () => {
    if (!securityStatus) return '#6B7280';
    
    switch (securityStatus.riskLevel) {
      case 'low': return '#10B981';
      case 'medium': return '#F59E0B';
      case 'high': return '#EF4444';
      default: return '#6B7280';
    }
  };

  const getSecurityIcon = () => {
    if (!securityStatus) return Shield;
    
    if (!securityStatus.isSecure) return ShieldAlert;
    
    switch (securityStatus.riskLevel) {
      case 'low': return ShieldCheck;
      case 'medium': return Shield;
      case 'high': return ShieldAlert;
      default: return Shield;
    }
  };

  const getSecurityMessage = () => {
    if (!securityStatus) return 'Checking security...';
    
    if (!securityStatus.isSecure) {
      return 'Payment blocked for security';
    }
    
    switch (securityStatus.riskLevel) {
      case 'low': return 'Payment secure';
      case 'medium': return 'Additional verification may be required';
      case 'high': return 'High risk detected';
      default: return 'Security status unknown';
    }
  };

  const handleDetailsPress = () => {
    setDetailsModalVisible(true);
  };

  const handleRefreshSecurity = () => {
    checkSecurity();
  };

  if (!visible) return null;

  const SecurityIcon = getSecurityIcon();
  const securityColor = getSecurityColor();

  return (
    <>
      <View style={styles.container}>
        <TouchableOpacity 
          style={[styles.indicator, { borderColor: securityColor }]}
          onPress={handleDetailsPress}
          disabled={loading}
        >
          <SecurityIcon size={16} color={securityColor} />
          <Text style={[styles.text, { color: securityColor }]}>
            {loading ? 'Checking...' : getSecurityMessage()}
          </Text>
          <Info size={14} color="#6B7280" />
        </TouchableOpacity>
      </View>

      <Modal
        visible={detailsModalVisible}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setDetailsModalVisible(false)}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Payment Security Details</Text>
            <TouchableOpacity onPress={() => setDetailsModalVisible(false)}>
              <XCircle size={24} color="#6B7280" />
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.modalContent}>
            {/* Overall Status */}
            <View style={styles.statusCard}>
              <View style={styles.statusHeader}>
                <SecurityIcon size={24} color={securityColor} />
                <View style={styles.statusText}>
                  <Text style={styles.statusTitle}>Security Status</Text>
                  <Text style={[styles.statusSubtitle, { color: securityColor }]}>
                    {getSecurityMessage()}
                  </Text>
                </View>
              </View>
            </View>

            {/* Security Checks */}
            <View style={styles.checksContainer}>
              <Text style={styles.sectionTitle}>Security Checks</Text>
              {securityStatus?.checks.map((check, index) => {
                const CheckIcon = check.icon;
                const checkColor = check.status === 'pass' ? '#10B981' : 
                                 check.status === 'warning' ? '#F59E0B' : '#EF4444';
                
                return (
                  <View key={index} style={styles.checkItem}>
                    <CheckIcon size={20} color={checkColor} />
                    <View style={styles.checkContent}>
                      <Text style={styles.checkName}>{check.name}</Text>
                      <Text style={styles.checkDescription}>{check.description}</Text>
                    </View>
                    <View style={[styles.checkStatus, { backgroundColor: checkColor }]}>
                      <Text style={styles.checkStatusText}>
                        {check.status.toUpperCase()}
                      </Text>
                    </View>
                  </View>
                );
              })}
            </View>

            {/* Device Information */}
            <View style={styles.deviceInfo}>
              <Text style={styles.sectionTitle}>Device Information</Text>
              <View style={styles.infoRow}>
                <Text style={styles.infoLabel}>Device ID:</Text>
                <Text style={styles.infoValue}>
                  {securityStatus?.deviceFingerprint.substring(0, 8)}...
                </Text>
              </View>
              <View style={styles.infoRow}>
                <Text style={styles.infoLabel}>Recent Attempts:</Text>
                <Text style={styles.infoValue}>{securityStatus?.recentAttempts || 0}</Text>
              </View>
            </View>

            {/* Actions */}
            <View style={styles.actions}>
              <TouchableOpacity style={styles.refreshButton} onPress={handleRefreshSecurity}>
                <Text style={styles.refreshButtonText}>Refresh Security Check</Text>
              </TouchableOpacity>
              
              {!securityStatus?.isSecure && (
                <TouchableOpacity 
                  style={styles.supportButton}
                  onPress={() => {
                    Alert.alert(
                      'Contact Support',
                      'If you believe this is an error, please contact our support team.',
                      [{ text: 'OK' }]
                    );
                  }}
                >
                  <Text style={styles.supportButtonText}>Contact Support</Text>
                </TouchableOpacity>
              )}
            </View>
          </ScrollView>
        </View>
      </Modal>
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: 8,
  },
  indicator: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderWidth: 1,
    borderRadius: 8,
    backgroundColor: '#F9FAFB',
    gap: 8,
  },
  text: {
    flex: 1,
    fontSize: 14,
    fontWeight: '500',
  },
  modalContainer: {
    flex: 1,
    backgroundColor: 'white',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827',
  },
  modalContent: {
    flex: 1,
    padding: 20,
  },
  statusCard: {
    backgroundColor: '#F9FAFB',
    borderRadius: 12,
    padding: 16,
    marginBottom: 20,
  },
  statusHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  statusText: {
    flex: 1,
  },
  statusTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#111827',
  },
  statusSubtitle: {
    fontSize: 14,
    marginTop: 2,
  },
  checksContainer: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 12,
  },
  checkItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
    gap: 12,
  },
  checkContent: {
    flex: 1,
  },
  checkName: {
    fontSize: 14,
    fontWeight: '500',
    color: '#111827',
  },
  checkDescription: {
    fontSize: 12,
    color: '#6B7280',
    marginTop: 2,
  },
  checkStatus: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  checkStatusText: {
    fontSize: 10,
    fontWeight: '600',
    color: 'white',
  },
  deviceInfo: {
    backgroundColor: '#F9FAFB',
    borderRadius: 12,
    padding: 16,
    marginBottom: 20,
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 4,
  },
  infoLabel: {
    fontSize: 14,
    color: '#6B7280',
  },
  infoValue: {
    fontSize: 14,
    fontWeight: '500',
    color: '#111827',
  },
  actions: {
    gap: 12,
  },
  refreshButton: {
    backgroundColor: '#3B82F6',
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  refreshButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  supportButton: {
    backgroundColor: '#F3F4F6',
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  supportButtonText: {
    color: '#374151',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default PaymentSecurityIndicator;
