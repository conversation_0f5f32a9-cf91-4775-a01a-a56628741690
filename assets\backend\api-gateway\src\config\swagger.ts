import { Options } from 'swagger-jsdoc';

export const swaggerOptions: Options = {
  definition: {
    openapi: '3.0.0',
    info: {
      title: 'BioScan API',
      version: '1.0.0',
      description: 'Comprehensive API for the BioScan nature identification application',
      contact: {
        name: 'BioScan Team',
        email: '<EMAIL>',
        url: 'https://bioscan.app'
      },
      license: {
        name: 'MIT',
        url: 'https://opensource.org/licenses/MIT'
      }
    },
    servers: [
      {
        url: process.env.API_BASE_URL || 'http://localhost:3000',
        description: 'Development server'
      },
      {
        url: 'https://api.bioscan.app',
        description: 'Production server'
      }
    ],
    components: {
      securitySchemes: {
        bearerAuth: {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT',
          description: 'JWT token for authentication'
        },
        refreshToken: {
          type: 'apiKey',
          in: 'cookie',
          name: 'refreshToken',
          description: 'HTTP-only cookie containing refresh token'
        }
      },
      schemas: {
        Error: {
          type: 'object',
          properties: {
            error: {
              type: 'object',
              properties: {
                code: {
                  type: 'string',
                  description: 'Error code'
                },
                message: {
                  type: 'string',
                  description: 'Error message'
                },
                timestamp: {
                  type: 'string',
                  format: 'date-time',
                  description: 'Error timestamp'
                },
                correlationId: {
                  type: 'string',
                  description: 'Request correlation ID'
                }
              }
            }
          }
        },
        User: {
          type: 'object',
          properties: {
            id: {
              type: 'string',
              format: 'uuid',
              description: 'User unique identifier'
            },
            email: {
              type: 'string',
              format: 'email',
              description: 'User email address'
            },
            firstName: {
              type: 'string',
              description: 'User first name'
            },
            lastName: {
              type: 'string',
              description: 'User last name'
            },
            role: {
              type: 'string',
              enum: ['user', 'premium', 'admin'],
              description: 'User role'
            },
            isEmailVerified: {
              type: 'boolean',
              description: 'Email verification status'
            },
            createdAt: {
              type: 'string',
              format: 'date-time',
              description: 'Account creation timestamp'
            },
            updatedAt: {
              type: 'string',
              format: 'date-time',
              description: 'Last update timestamp'
            }
          }
        },
        AuthTokens: {
          type: 'object',
          properties: {
            accessToken: {
              type: 'string',
              description: 'JWT access token (1 hour expiry)'
            },
            refreshToken: {
              type: 'string',
              description: 'Refresh token (7 days expiry, HTTP-only cookie)'
            },
            expiresIn: {
              type: 'number',
              description: 'Access token expiry time in seconds'
            }
          }
        },
        SpeciesIdentification: {
          type: 'object',
          properties: {
            id: {
              type: 'string',
              format: 'uuid',
              description: 'Identification unique identifier'
            },
            name: {
              type: 'string',
              description: 'Common name of the species'
            },
            scientificName: {
              type: 'string',
              description: 'Scientific name of the species'
            },
            confidence: {
              type: 'number',
              minimum: 0,
              maximum: 100,
              description: 'Confidence score (0-100)'
            },
            category: {
              type: 'string',
              description: 'Species category (e.g., bird, plant, insect)'
            },
            description: {
              type: 'string',
              description: 'Species description'
            },
            habitat: {
              type: 'string',
              description: 'Natural habitat information'
            },
            conservationStatus: {
              type: 'string',
              description: 'Conservation status'
            },
            facts: {
              type: 'array',
              items: {
                type: 'string'
              },
              description: 'Interesting facts about the species'
            },
            physicalTraits: {
              type: 'object',
              properties: {
                size: { type: 'string' },
                weight: { type: 'string' },
                lifespan: { type: 'string' },
                diet: { type: 'string' }
              }
            },
            alternatives: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  name: { type: 'string' },
                  scientificName: { type: 'string' },
                  confidence: { type: 'number' }
                }
              }
            },
            imageUrl: {
              type: 'string',
              format: 'uri',
              description: 'URL of the uploaded image'
            },
            location: {
              type: 'object',
              properties: {
                latitude: { type: 'number' },
                longitude: { type: 'number' },
                address: { type: 'string' }
              }
            },
            createdAt: {
              type: 'string',
              format: 'date-time'
            }
          }
        }
      },
      responses: {
        UnauthorizedError: {
          description: 'Authentication required',
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/Error'
              }
            }
          }
        },
        ForbiddenError: {
          description: 'Insufficient permissions',
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/Error'
              }
            }
          }
        },
        NotFoundError: {
          description: 'Resource not found',
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/Error'
              }
            }
          }
        },
        ValidationError: {
          description: 'Validation failed',
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/Error'
              }
            }
          }
        },
        RateLimitError: {
          description: 'Rate limit exceeded',
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/Error'
              }
            }
          }
        }
      }
    },
    security: [
      {
        bearerAuth: []
      }
    ],
    tags: [
      {
        name: 'Authentication',
        description: 'User authentication and authorization'
      },
      {
        name: 'Media',
        description: 'Image upload and processing'
      },
      {
        name: 'Species',
        description: 'Species identification and information'
      },
      {
        name: 'User',
        description: 'User profile and preferences'
      },
      {
        name: 'Notifications',
        description: 'Push notifications and alerts'
      },
      {
        name: 'Payments',
        description: 'Subscription and payment management'
      },
      {
        name: 'Analytics',
        description: 'Usage analytics and insights'
      }
    ]
  },
  apis: [
    './src/routes/*.ts',
    './src/controllers/*.ts'
  ]
};
