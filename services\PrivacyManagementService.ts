import { supabase } from '../lib/supabase';
import * as FileSystem from 'expo-file-system';
import * as Sharing from 'expo-sharing';
import { Alert } from 'react-native';

export interface PrivacySettings {
  // Data Collection Preferences
  allow_analytics: boolean;
  allow_performance_tracking: boolean;
  allow_crash_reporting: boolean;
  allow_usage_statistics: boolean;
  
  // Location Privacy
  share_location: boolean;
  location_precision: 'exact' | 'approximate' | 'city' | 'region' | 'country' | 'none';
  allow_location_history: boolean;
  
  // Profile Privacy
  public_profile: boolean;
  show_real_name: boolean;
  show_email: boolean;
  show_statistics: boolean;
  show_achievements: boolean;
  show_collection: boolean;
  
  // Communication Preferences
  allow_marketing_emails: boolean;
  allow_research_participation: boolean;
  allow_community_contact: boolean;
  
  // Data Sharing
  allow_scientific_research: boolean;
  allow_conservation_efforts: boolean;
  allow_third_party_integrations: boolean;
  
  // Media Privacy
  watermark_images: boolean;
  strip_metadata: boolean;
  allow_image_analysis: boolean;
  
  // Advanced Privacy
  data_retention_period_months: number;
  require_explicit_consent: boolean;
  opt_out_of_ai_training: boolean;
}

export interface DataExportRequest {
  id: string;
  request_type: 'full_export' | 'partial_export' | 'account_data' | 'identifications' | 'media' | 'location_data';
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'expired';
  export_format: 'json' | 'csv' | 'xml';
  include_media: boolean;
  include_location: boolean;
  include_metadata: boolean;
  date_range_start?: string;
  date_range_end?: string;
  file_url?: string;
  file_size_bytes?: number;
  expires_at: string;
  completed_at?: string;
  error_message?: string;
  created_at: string;
}

export interface DataDeletionRequest {
  id: string;
  deletion_type: 'account_deletion' | 'data_purge' | 'selective_deletion';
  status: 'pending' | 'approved' | 'processing' | 'completed' | 'rejected' | 'cancelled';
  data_categories: string[];
  retention_period_days: number;
  reason?: string;
  scheduled_deletion_at?: string;
  completed_at?: string;
  created_at: string;
}

export interface ConsentRecord {
  consent_type: string;
  consent_given: boolean;
  consent_version: string;
  consent_method: string;
  created_at: string;
  expires_at?: string;
}

export interface DataCategory {
  id: string;
  name: string;
  description: string;
  is_essential: boolean;
  retention_period_days?: number;
}

export class PrivacyManagementService {
  
  /**
   * Get user's current privacy settings
   */
  static async getPrivacySettings(): Promise<PrivacySettings | null> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        throw new Error('User not authenticated');
      }

      const { data, error } = await supabase
        .from('user_privacy_settings')
        .select('*')
        .eq('user_id', user.id)
        .single();

      if (error && error.code !== 'PGRST116') {
        throw error;
      }

      // Return default settings if none exist
      if (!data) {
        return this.getDefaultPrivacySettings();
      }

      return data;
    } catch (error) {
      console.error('Error fetching privacy settings:', error);
      return null;
    }
  }

  /**
   * Update user's privacy settings
   */
  static async updatePrivacySettings(settings: Partial<PrivacySettings>): Promise<boolean> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        throw new Error('User not authenticated');
      }

      // Log the privacy settings change
      await this.logConsentChange('privacy_settings_update', true, '1.0', 'explicit');

      const { error } = await supabase
        .from('user_privacy_settings')
        .upsert({
          user_id: user.id,
          ...settings,
          updated_at: new Date().toISOString()
        });

      if (error) {
        throw error;
      }

      // Log data access for compliance
      await this.logDataAccess('modify', 'privacy_settings', 'User updated privacy settings');

      return true;
    } catch (error) {
      console.error('Error updating privacy settings:', error);
      return false;
    }
  }

  /**
   * Request data export
   */
  static async requestDataExport(
    requestType: DataExportRequest['request_type'],
    format: 'json' | 'csv' | 'xml' = 'json',
    options: {
      includeMedia?: boolean;
      includeLocation?: boolean;
      includeMetadata?: boolean;
      dateRangeStart?: Date;
      dateRangeEnd?: Date;
    } = {}
  ): Promise<string | null> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        throw new Error('User not authenticated');
      }

      const { data, error } = await supabase
        .from('data_export_requests')
        .insert({
          user_id: user.id,
          request_type: requestType,
          export_format: format,
          include_media: options.includeMedia ?? true,
          include_location: options.includeLocation ?? true,
          include_metadata: options.includeMetadata ?? true,
          date_range_start: options.dateRangeStart?.toISOString(),
          date_range_end: options.dateRangeEnd?.toISOString()
        })
        .select()
        .single();

      if (error) {
        throw error;
      }

      // Log the export request
      await this.logDataAccess('export', 'all_data', `Data export requested: ${requestType}`);

      return data.id;
    } catch (error) {
      console.error('Error requesting data export:', error);
      return null;
    }
  }

  /**
   * Get data export requests
   */
  static async getDataExportRequests(): Promise<DataExportRequest[]> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        throw new Error('User not authenticated');
      }

      const { data, error } = await supabase
        .from('data_export_requests')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false });

      if (error) {
        throw error;
      }

      return data || [];
    } catch (error) {
      console.error('Error fetching export requests:', error);
      return [];
    }
  }

  /**
   * Download exported data
   */
  static async downloadExportedData(exportId: string): Promise<boolean> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        throw new Error('User not authenticated');
      }

      const { data: exportRequest, error } = await supabase
        .from('data_export_requests')
        .select('*')
        .eq('id', exportId)
        .eq('user_id', user.id)
        .single();

      if (error || !exportRequest) {
        throw new Error('Export request not found');
      }

      if (exportRequest.status !== 'completed' || !exportRequest.file_url) {
        throw new Error('Export not ready for download');
      }

      // Check if export has expired
      if (new Date(exportRequest.expires_at) < new Date()) {
        throw new Error('Export has expired');
      }

      // Download the file
      const fileName = `bioscan_data_export_${exportRequest.id}.${exportRequest.export_format}`;
      const fileUri = FileSystem.documentDirectory + fileName;

      const downloadResult = await FileSystem.downloadAsync(
        exportRequest.file_url,
        fileUri
      );

      if (downloadResult.status === 200) {
        // Share the file
        if (await Sharing.isAvailableAsync()) {
          await Sharing.shareAsync(downloadResult.uri);
        }

        // Log the download
        await this.logDataAccess('export', 'all_data', `Data export downloaded: ${exportId}`);

        return true;
      } else {
        throw new Error('Download failed');
      }
    } catch (error) {
      console.error('Error downloading export:', error);
      Alert.alert('Download Error', error instanceof Error ? error.message : 'Failed to download export');
      return false;
    }
  }

  /**
   * Request data deletion
   */
  static async requestDataDeletion(
    deletionType: DataDeletionRequest['deletion_type'],
    dataCategories: string[],
    reason?: string
  ): Promise<string | null> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        throw new Error('User not authenticated');
      }

      const { data, error } = await supabase
        .from('data_deletion_requests')
        .insert({
          user_id: user.id,
          deletion_type: deletionType,
          data_categories: dataCategories,
          reason: reason,
          retention_period_days: deletionType === 'account_deletion' ? 30 : 7
        })
        .select()
        .single();

      if (error) {
        throw error;
      }

      // Log the deletion request
      await this.logDataAccess('delete', 'deletion_request', `Data deletion requested: ${deletionType}`);

      return data.id;
    } catch (error) {
      console.error('Error requesting data deletion:', error);
      return null;
    }
  }

  /**
   * Get data deletion requests
   */
  static async getDataDeletionRequests(): Promise<DataDeletionRequest[]> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        throw new Error('User not authenticated');
      }

      const { data, error } = await supabase
        .from('data_deletion_requests')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false });

      if (error) {
        throw error;
      }

      return data || [];
    } catch (error) {
      console.error('Error fetching deletion requests:', error);
      return [];
    }
  }

  /**
   * Cancel data deletion request
   */
  static async cancelDataDeletion(requestId: string): Promise<boolean> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        throw new Error('User not authenticated');
      }

      const { error } = await supabase
        .from('data_deletion_requests')
        .update({ 
          status: 'cancelled',
          updated_at: new Date().toISOString()
        })
        .eq('id', requestId)
        .eq('user_id', user.id)
        .in('status', ['pending', 'approved']);

      if (error) {
        throw error;
      }

      // Log the cancellation
      await this.logDataAccess('modify', 'deletion_request', `Data deletion cancelled: ${requestId}`);

      return true;
    } catch (error) {
      console.error('Error cancelling deletion request:', error);
      return false;
    }
  }

  /**
   * Get available data categories
   */
  static async getDataCategories(): Promise<DataCategory[]> {
    try {
      const { data, error } = await supabase
        .from('data_categories')
        .select('*')
        .order('name');

      if (error) {
        throw error;
      }

      return data || [];
    } catch (error) {
      console.error('Error fetching data categories:', error);
      return [];
    }
  }

  /**
   * Get user's consent history
   */
  static async getConsentHistory(): Promise<ConsentRecord[]> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        throw new Error('User not authenticated');
      }

      const { data, error } = await supabase
        .from('consent_log')
        .select('consent_type, consent_given, consent_version, consent_method, created_at, expires_at')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false });

      if (error) {
        throw error;
      }

      return data || [];
    } catch (error) {
      console.error('Error fetching consent history:', error);
      return [];
    }
  }

  /**
   * Record user consent
   */
  static async recordConsent(
    consentType: string,
    consentGiven: boolean,
    consentVersion: string = '1.0',
    consentMethod: string = 'explicit',
    expiresAt?: Date
  ): Promise<boolean> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        throw new Error('User not authenticated');
      }

      const { error } = await supabase
        .from('consent_log')
        .insert({
          user_id: user.id,
          consent_type: consentType,
          consent_given: consentGiven,
          consent_version: consentVersion,
          consent_method: consentMethod,
          expires_at: expiresAt?.toISOString()
        });

      if (error) {
        throw error;
      }

      return true;
    } catch (error) {
      console.error('Error recording consent:', error);
      return false;
    }
  }

  /**
   * Get default privacy settings
   */
  private static getDefaultPrivacySettings(): PrivacySettings {
    return {
      allow_analytics: true,
      allow_performance_tracking: true,
      allow_crash_reporting: true,
      allow_usage_statistics: true,
      share_location: false,
      location_precision: 'city',
      allow_location_history: false,
      public_profile: false,
      show_real_name: false,
      show_email: false,
      show_statistics: true,
      show_achievements: true,
      show_collection: false,
      allow_marketing_emails: false,
      allow_research_participation: false,
      allow_community_contact: true,
      allow_scientific_research: false,
      allow_conservation_efforts: false,
      allow_third_party_integrations: false,
      watermark_images: false,
      strip_metadata: true,
      allow_image_analysis: true,
      data_retention_period_months: 24,
      require_explicit_consent: false,
      opt_out_of_ai_training: false
    };
  }

  /**
   * Log data access for GDPR compliance
   */
  private static async logDataAccess(
    accessType: string,
    dataCategory: string,
    purpose: string
  ): Promise<void> {
    try {
      await supabase.rpc('log_data_access', {
        target_user_id: (await supabase.auth.getUser()).data.user?.id,
        access_type_param: accessType,
        data_category_param: dataCategory,
        purpose_param: purpose
      });
    } catch (error) {
      console.error('Error logging data access:', error);
    }
  }

  /**
   * Log consent change
   */
  private static async logConsentChange(
    consentType: string,
    consentGiven: boolean,
    version: string,
    method: string
  ): Promise<void> {
    try {
      await this.recordConsent(consentType, consentGiven, version, method);
    } catch (error) {
      console.error('Error logging consent change:', error);
    }
  }
}
