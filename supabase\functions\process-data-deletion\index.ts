import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

const supabaseClient = createClient(
  Deno.env.get('SUPABASE_URL') ?? '',
  Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
)

interface DeletionRequest {
  id: string;
  user_id: string;
  deletion_type: string;
  data_categories: string[];
  status: string;
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const { deletionRequestId } = await req.json()

    // Get deletion request details
    const { data: deletionRequest, error: requestError } = await supabaseClient
      .from('data_deletion_requests')
      .select('*')
      .eq('id', deletionRequestId)
      .single()

    if (requestError || !deletionRequest) {
      throw new Error('Deletion request not found')
    }

    // Verify request is approved and ready for processing
    if (deletionRequest.status !== 'approved') {
      throw new Error('Deletion request not approved')
    }

    // Update status to processing
    await supabaseClient
      .from('data_deletion_requests')
      .update({ status: 'processing' })
      .eq('id', deletionRequestId)

    // Process deletion based on type
    const deletionResult = await processDeletion(deletionRequest)

    // Update deletion request status
    await supabaseClient
      .from('data_deletion_requests')
      .update({
        status: 'completed',
        completed_at: new Date().toISOString()
      })
      .eq('id', deletionRequestId)

    // Log the deletion completion
    await supabaseClient
      .from('data_access_log')
      .insert({
        user_id: deletionRequest.user_id,
        access_type: 'delete',
        data_category: 'deletion_completion',
        purpose: `Data deletion completed: ${deletionRequest.deletion_type}`,
        legal_basis: 'user_request'
      })

    // Send notification to user (if account still exists)
    if (deletionRequest.deletion_type !== 'account_deletion') {
      await supabaseClient
        .from('notifications')
        .insert({
          user_id: deletionRequest.user_id,
          type: 'system',
          title: 'Data Deletion Completed',
          message: 'Your requested data has been permanently deleted from our systems.',
          data: {
            deletion_id: deletionRequestId,
            categories_deleted: deletionRequest.data_categories,
            completion_date: new Date().toISOString()
          }
        })
    }

    return new Response(
      JSON.stringify({ 
        success: true, 
        deletionId: deletionRequestId,
        deletedCategories: deletionRequest.data_categories,
        deletionType: deletionRequest.deletion_type
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      }
    )

  } catch (error: any) {
    console.error('Error processing data deletion:', error)

    // Update deletion request status to failed
    const { deletionRequestId } = await req.json().catch(() => ({}))
    if (deletionRequestId) {
      await supabaseClient
        .from('data_deletion_requests')
        .update({
          status: 'failed',
          error_message: error.message
        })
        .eq('id', deletionRequestId)
    }

    return new Response(
      JSON.stringify({ error: error.message }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400,
      }
    )
  }
})

async function processDeletion(deletionRequest: DeletionRequest): Promise<any> {
  const userId = deletionRequest.user_id
  const categories = deletionRequest.data_categories
  const deletionType = deletionRequest.deletion_type

  const deletionResults: any = {
    deleted_categories: [],
    preserved_categories: [],
    errors: []
  }

  try {
    // Get data category mappings
    const { data: dataCategories } = await supabaseClient
      .from('data_categories')
      .select('*')
      .in('name', categories)

    if (!dataCategories) {
      throw new Error('No data categories found')
    }

    // Process each category
    for (const category of dataCategories) {
      try {
        // Skip essential data for selective deletion
        if (deletionType === 'selective_deletion' && category.is_essential) {
          deletionResults.preserved_categories.push({
            category: category.name,
            reason: 'Essential data cannot be deleted while account is active'
          })
          continue
        }

        // Delete data from associated tables
        const deletedCount = await deleteDataFromTables(userId, category.table_names)
        
        deletionResults.deleted_categories.push({
          category: category.name,
          tables: category.table_names,
          records_deleted: deletedCount
        })

      } catch (error: any) {
        deletionResults.errors.push({
          category: category.name,
          error: error.message
        })
      }
    }

    // For account deletion, also delete the user account
    if (deletionType === 'account_deletion') {
      await deleteUserAccount(userId)
      deletionResults.account_deleted = true
    }

    return deletionResults

  } catch (error) {
    console.error('Error in processDeletion:', error)
    throw error
  }
}

async function deleteDataFromTables(userId: string, tableNames: string[]): Promise<number> {
  let totalDeleted = 0

  for (const tableName of tableNames) {
    try {
      // Skip auth.users table - handled separately
      if (tableName === 'auth.users') {
        continue
      }

      // Delete records from the table
      const { count, error } = await supabaseClient
        .from(tableName)
        .delete({ count: 'exact' })
        .eq('user_id', userId)

      if (error) {
        console.error(`Error deleting from ${tableName}:`, error)
        continue
      }

      totalDeleted += count || 0

      // Also delete media files if this is media_uploads table
      if (tableName === 'media_uploads') {
        await deleteMediaFiles(userId)
      }

    } catch (error) {
      console.error(`Error processing table ${tableName}:`, error)
    }
  }

  return totalDeleted
}

async function deleteMediaFiles(userId: string): Promise<void> {
  try {
    // List all files in user's media folder
    const { data: files, error: listError } = await supabaseClient.storage
      .from('media')
      .list(`${userId}/`, {
        limit: 1000,
        sortBy: { column: 'name', order: 'asc' }
      })

    if (listError) {
      console.error('Error listing media files:', listError)
      return
    }

    if (files && files.length > 0) {
      // Delete all files
      const filePaths = files.map(file => `${userId}/${file.name}`)
      
      const { error: deleteError } = await supabaseClient.storage
        .from('media')
        .remove(filePaths)

      if (deleteError) {
        console.error('Error deleting media files:', deleteError)
      }
    }

    // Also delete any export files
    const { data: exportFiles } = await supabaseClient.storage
      .from('data-exports')
      .list(`exports/${userId}/`, {
        limit: 1000
      })

    if (exportFiles && exportFiles.length > 0) {
      const exportPaths = exportFiles.map(file => `exports/${userId}/${file.name}`)
      
      await supabaseClient.storage
        .from('data-exports')
        .remove(exportPaths)
    }

  } catch (error) {
    console.error('Error deleting media files:', error)
  }
}

async function deleteUserAccount(userId: string): Promise<void> {
  try {
    // First, delete all user data from all tables
    const allTables = [
      'user_profiles',
      'user_privacy_settings',
      'species_identifications',
      'media_uploads',
      'user_locations',
      'user_subscriptions',
      'payment_transactions',
      'usage_tracking',
      'notifications',
      'consent_log',
      'data_access_log',
      'data_export_requests',
      'data_deletion_requests',
      'fraud_detection_logs'
    ]

    for (const table of allTables) {
      try {
        await supabaseClient
          .from(table)
          .delete()
          .eq('user_id', userId)
      } catch (error) {
        console.error(`Error deleting from ${table}:`, error)
      }
    }

    // Delete media files
    await deleteMediaFiles(userId)

    // Finally, delete the auth user (this will cascade to remaining references)
    const { error: authError } = await supabaseClient.auth.admin.deleteUser(userId)
    
    if (authError) {
      console.error('Error deleting auth user:', authError)
      throw authError
    }

  } catch (error) {
    console.error('Error deleting user account:', error)
    throw error
  }
}

// Helper function to anonymize data instead of deleting (for research purposes)
async function anonymizeUserData(userId: string, tableNames: string[]): Promise<number> {
  let totalAnonymized = 0

  for (const tableName of tableNames) {
    try {
      // Generate anonymous ID
      const anonymousId = `anon_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

      // Update records to use anonymous ID
      const { count, error } = await supabaseClient
        .from(tableName)
        .update({ 
          user_id: anonymousId,
          anonymized_at: new Date().toISOString()
        })
        .eq('user_id', userId)

      if (error) {
        console.error(`Error anonymizing ${tableName}:`, error)
        continue
      }

      totalAnonymized += count || 0

    } catch (error) {
      console.error(`Error processing table ${tableName}:`, error)
    }
  }

  return totalAnonymized
}
