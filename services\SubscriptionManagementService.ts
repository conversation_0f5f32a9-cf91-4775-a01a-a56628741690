import { supabase } from '../lib/supabase';
import { Alert } from 'react-native';

export interface SubscriptionPlan {
  id: string;
  name: string;
  description: string;
  price_monthly: number;
  price_yearly: number;
  features: string[];
  max_identifications_per_month: number;
  is_active: boolean;
  stripe_price_id_monthly?: string;
  stripe_price_id_yearly?: string;
  trial_period_days: number;
}

export interface UserSubscription {
  id: string;
  user_id: string;
  plan_id: string;
  stripe_subscription_id: string;
  status: 'active' | 'canceled' | 'past_due' | 'unpaid' | 'trialing' | 'incomplete' | 'incomplete_expired';
  current_period_start: string;
  current_period_end: string;
  trial_start?: string;
  trial_end?: string;
  cancel_at_period_end: boolean;
  canceled_at?: string;
  created_at: string;
  updated_at: string;
  plan?: SubscriptionPlan;
}

export interface SubscriptionUsage {
  feature_type: string;
  current_usage: number;
  usage_limit: number;
  usage_percentage: number;
  period_start: string;
  period_end: string;
}

export interface PlanChangePreview {
  new_plan: SubscriptionPlan;
  current_plan: SubscriptionPlan;
  proration_amount: number;
  effective_date: string;
  next_billing_date: string;
  billing_period: 'monthly' | 'yearly';
}

export class SubscriptionManagementService {
  
  /**
   * Get user's current active subscription
   */
  static async getCurrentSubscription(): Promise<UserSubscription | null> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        throw new Error('User not authenticated');
      }

      const { data, error } = await supabase
        .from('user_subscriptions')
        .select(`
          *,
          plan:subscription_plans(*)
        `)
        .eq('user_id', user.id)
        .in('status', ['active', 'trialing'])
        .order('created_at', { ascending: false })
        .limit(1)
        .single();

      if (error && error.code !== 'PGRST116') {
        throw error;
      }

      return data;
    } catch (error) {
      console.error('Error fetching current subscription:', error);
      return null;
    }
  }

  /**
   * Get all subscription plans
   */
  static async getAvailablePlans(): Promise<SubscriptionPlan[]> {
    try {
      const { data, error } = await supabase
        .from('subscription_plans')
        .select('*')
        .eq('is_active', true)
        .order('price_monthly', { ascending: true });

      if (error) {
        throw error;
      }

      return data || [];
    } catch (error) {
      console.error('Error fetching subscription plans:', error);
      throw error;
    }
  }

  /**
   * Get user's subscription usage statistics
   */
  static async getUsageStats(): Promise<SubscriptionUsage[]> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        throw new Error('User not authenticated');
      }

      const { data, error } = await supabase.rpc('get_user_usage_stats', {
        user_uuid: user.id
      });

      if (error) {
        throw error;
      }

      return data || [];
    } catch (error) {
      console.error('Error fetching usage stats:', error);
      return [];
    }
  }

  /**
   * Check if user can use a specific feature
   */
  static async canUseFeature(featureType: string): Promise<boolean> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        return false;
      }

      const { data, error } = await supabase.rpc('check_usage_limit', {
        user_uuid: user.id,
        feature_name: featureType
      });

      if (error) {
        console.error('Error checking feature usage:', error);
        return false;
      }

      return data === true;
    } catch (error) {
      console.error('Error checking feature usage:', error);
      return false;
    }
  }

  /**
   * Track feature usage
   */
  static async trackUsage(featureType: string, usageAmount: number = 1): Promise<void> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        throw new Error('User not authenticated');
      }

      const { error } = await supabase.rpc('track_feature_usage', {
        user_uuid: user.id,
        feature_name: featureType,
        usage_amount: usageAmount
      });

      if (error) {
        throw error;
      }
    } catch (error) {
      console.error('Error tracking usage:', error);
      // Don't throw error to avoid blocking user actions
    }
  }

  /**
   * Preview plan change with proration calculation
   */
  static async previewPlanChange(
    newPlanId: string,
    billingPeriod: 'monthly' | 'yearly'
  ): Promise<PlanChangePreview | null> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        throw new Error('User not authenticated');
      }

      const { data, error } = await supabase.functions.invoke('preview-plan-change', {
        body: {
          userId: user.id,
          newPlanId,
          billingPeriod
        }
      });

      if (error) {
        throw error;
      }

      return data;
    } catch (error) {
      console.error('Error previewing plan change:', error);
      return null;
    }
  }

  /**
   * Change subscription plan
   */
  static async changePlan(
    newPlanId: string,
    billingPeriod: 'monthly' | 'yearly',
    prorationBehavior: 'create_prorations' | 'none' = 'create_prorations'
  ): Promise<{ success: boolean; message: string }> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        throw new Error('User not authenticated');
      }

      const { data, error } = await supabase.functions.invoke('change-subscription-plan', {
        body: {
          userId: user.id,
          newPlanId,
          billingPeriod,
          prorationBehavior
        }
      });

      if (error) {
        throw error;
      }

      return {
        success: true,
        message: 'Subscription plan changed successfully'
      };
    } catch (error: any) {
      console.error('Error changing plan:', error);
      return {
        success: false,
        message: error.message || 'Failed to change subscription plan'
      };
    }
  }

  /**
   * Cancel subscription
   */
  static async cancelSubscription(
    cancelAtPeriodEnd: boolean = true,
    cancellationReason?: string
  ): Promise<{ success: boolean; message: string }> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        throw new Error('User not authenticated');
      }

      const currentSub = await this.getCurrentSubscription();
      if (!currentSub) {
        throw new Error('No active subscription found');
      }

      const { data, error } = await supabase.functions.invoke('cancel-subscription', {
        body: {
          subscriptionId: currentSub.stripe_subscription_id,
          cancelAtPeriodEnd,
          cancellationReason
        }
      });

      if (error) {
        throw error;
      }

      const message = cancelAtPeriodEnd 
        ? 'Subscription will be canceled at the end of the current billing period'
        : 'Subscription canceled immediately';

      return {
        success: true,
        message
      };
    } catch (error: any) {
      console.error('Error canceling subscription:', error);
      return {
        success: false,
        message: error.message || 'Failed to cancel subscription'
      };
    }
  }

  /**
   * Reactivate canceled subscription
   */
  static async reactivateSubscription(): Promise<{ success: boolean; message: string }> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        throw new Error('User not authenticated');
      }

      const { data, error } = await supabase.functions.invoke('reactivate-subscription', {
        body: {
          userId: user.id
        }
      });

      if (error) {
        throw error;
      }

      return {
        success: true,
        message: 'Subscription reactivated successfully'
      };
    } catch (error: any) {
      console.error('Error reactivating subscription:', error);
      return {
        success: false,
        message: error.message || 'Failed to reactivate subscription'
      };
    }
  }

  /**
   * Get subscription status with detailed information
   */
  static async getSubscriptionStatus(): Promise<{
    hasActiveSubscription: boolean;
    planName?: string;
    status?: string;
    currentPeriodEnd?: string;
    maxIdentifications?: number;
    isTrialing?: boolean;
    trialEndsAt?: string;
    cancelAtPeriodEnd?: boolean;
  }> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        return { hasActiveSubscription: false };
      }

      const { data, error } = await supabase.rpc('get_user_subscription_status', {
        user_uuid: user.id
      });

      if (error) {
        throw error;
      }

      if (!data || data.length === 0) {
        return { hasActiveSubscription: false };
      }

      const subscription = data[0];
      return {
        hasActiveSubscription: subscription.has_active_subscription,
        planName: subscription.plan_name,
        status: subscription.status,
        currentPeriodEnd: subscription.current_period_end,
        maxIdentifications: subscription.max_identifications,
        isTrialing: subscription.status === 'trialing',
        trialEndsAt: subscription.trial_end,
        cancelAtPeriodEnd: subscription.cancel_at_period_end
      };
    } catch (error) {
      console.error('Error getting subscription status:', error);
      return { hasActiveSubscription: false };
    }
  }
}
