import jwt from 'jsonwebtoken';
import crypto from 'crypto';
import { createClient } from 'redis';
import { prisma } from '../index';
import { logger } from './logger';

const redisClient = createClient({
  url: process.env.REDIS_URL || 'redis://localhost:6379'
});

redisClient.connect().catch(console.error);

// Token configuration as per requirements
export const TOKEN_CONFIG = {
  ACCESS_TOKEN_EXPIRY: '1h', // 1 hour
  REFRESH_TOKEN_EXPIRY: '7d', // 7 days
  ACCESS_TOKEN_EXPIRY_SECONDS: 3600, // 1 hour in seconds
  REFRESH_TOKEN_EXPIRY_SECONDS: 604800, // 7 days in seconds
};

export interface JWTPayload {
  id: string;
  email: string;
  role: string;
  sessionId: string;
  iat: number;
  exp: number;
}

export interface TokenPair {
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
}

/**
 * Generate JWT access token (stateless, 1-hour expiry)
 */
export function generateAccessToken(payload: {
  id: string;
  email: string;
  role: string;
  sessionId: string;
}): string {
  const jwtPayload = {
    id: payload.id,
    email: payload.email,
    role: payload.role,
    sessionId: payload.sessionId,
    type: 'access'
  };

  return jwt.sign(jwtPayload, process.env.JWT_SECRET!, {
    expiresIn: TOKEN_CONFIG.ACCESS_TOKEN_EXPIRY,
    issuer: 'bioscan-auth',
    audience: 'bioscan-app',
    algorithm: 'HS256'
  });
}

/**
 * Generate refresh token (stored in database, 7-day expiry)
 */
export async function generateRefreshToken(userId: string, deviceInfo?: {
  userAgent?: string;
  ipAddress?: string;
}): Promise<string> {
  const token = crypto.randomBytes(64).toString('hex');
  const expiresAt = new Date(Date.now() + TOKEN_CONFIG.REFRESH_TOKEN_EXPIRY_SECONDS * 1000);

  // Store refresh token in database
  await prisma.refreshToken.create({
    data: {
      userId,
      token,
      deviceInfo: deviceInfo?.userAgent,
      ipAddress: deviceInfo?.ipAddress,
      expiresAt
    }
  });

  return token;
}

/**
 * Generate complete token pair
 */
export async function generateTokenPair(
  userId: string,
  email: string,
  role: string,
  deviceInfo?: {
    userAgent?: string;
    ipAddress?: string;
  }
): Promise<TokenPair> {
  // Create session
  const session = await prisma.session.create({
    data: {
      userId,
      sessionToken: crypto.randomBytes(32).toString('hex'),
      deviceInfo: deviceInfo?.userAgent,
      ipAddress: deviceInfo?.ipAddress,
      userAgent: deviceInfo?.userAgent,
      expiresAt: new Date(Date.now() + TOKEN_CONFIG.ACCESS_TOKEN_EXPIRY_SECONDS * 1000)
    }
  });

  // Generate tokens
  const accessToken = generateAccessToken({
    id: userId,
    email,
    role,
    sessionId: session.id
  });

  const refreshToken = await generateRefreshToken(userId, deviceInfo);

  // Store session in Redis for quick access
  await redisClient.setEx(
    `session:${userId}`,
    TOKEN_CONFIG.ACCESS_TOKEN_EXPIRY_SECONDS,
    JSON.stringify({
      sessionId: session.id,
      userId,
      email,
      role,
      createdAt: new Date().toISOString()
    })
  );

  return {
    accessToken,
    refreshToken,
    expiresIn: TOKEN_CONFIG.ACCESS_TOKEN_EXPIRY_SECONDS
  };
}

/**
 * Verify JWT access token
 */
export function verifyAccessToken(token: string): JWTPayload {
  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET!, {
      issuer: 'bioscan-auth',
      audience: 'bioscan-app',
      algorithms: ['HS256']
    }) as JWTPayload;

    return decoded;
  } catch (error) {
    if (error instanceof jwt.TokenExpiredError) {
      throw new Error('Token expired');
    }
    if (error instanceof jwt.JsonWebTokenError) {
      throw new Error('Invalid token');
    }
    throw new Error('Token verification failed');
  }
}

/**
 * Verify refresh token
 */
export async function verifyRefreshToken(token: string): Promise<{
  userId: string;
  tokenId: string;
}> {
  const refreshToken = await prisma.refreshToken.findUnique({
    where: { token },
    include: { user: true }
  });

  if (!refreshToken) {
    throw new Error('Invalid refresh token');
  }

  if (refreshToken.isRevoked) {
    throw new Error('Refresh token has been revoked');
  }

  if (refreshToken.expiresAt < new Date()) {
    // Clean up expired token
    await prisma.refreshToken.delete({
      where: { id: refreshToken.id }
    });
    throw new Error('Refresh token expired');
  }

  if (refreshToken.user.status !== 'ACTIVE') {
    throw new Error('User account is not active');
  }

  return {
    userId: refreshToken.userId,
    tokenId: refreshToken.id
  };
}

/**
 * Refresh access token using refresh token
 */
export async function refreshAccessToken(refreshToken: string): Promise<TokenPair> {
  const { userId, tokenId } = await verifyRefreshToken(refreshToken);

  // Get user details
  const user = await prisma.user.findUnique({
    where: { id: userId }
  });

  if (!user) {
    throw new Error('User not found');
  }

  // Generate new token pair
  const tokenPair = await generateTokenPair(userId, user.email, user.role);

  // Revoke old refresh token
  await prisma.refreshToken.update({
    where: { id: tokenId },
    data: {
      isRevoked: true,
      revokedAt: new Date()
    }
  });

  return tokenPair;
}

/**
 * Blacklist access token
 */
export async function blacklistAccessToken(token: string): Promise<void> {
  try {
    const decoded = verifyAccessToken(token);
    const expiresAt = new Date(decoded.exp * 1000);

    // Store in database
    await prisma.blacklistedToken.create({
      data: {
        token,
        tokenType: 'ACCESS',
        expiresAt
      }
    });

    // Store in Redis for quick lookup
    const ttl = Math.max(0, Math.floor((expiresAt.getTime() - Date.now()) / 1000));
    if (ttl > 0) {
      await redisClient.setEx(`blacklist:${token}`, ttl, 'true');
    }

    logger.info('Access token blacklisted', { tokenId: decoded.id });
  } catch (error) {
    logger.error('Failed to blacklist token:', error);
    throw new Error('Failed to blacklist token');
  }
}

/**
 * Revoke refresh token
 */
export async function revokeRefreshToken(token: string): Promise<void> {
  await prisma.refreshToken.updateMany({
    where: { token },
    data: {
      isRevoked: true,
      revokedAt: new Date()
    }
  });

  logger.info('Refresh token revoked');
}

/**
 * Revoke all user tokens (logout from all devices)
 */
export async function revokeAllUserTokens(userId: string): Promise<void> {
  // Revoke all refresh tokens
  await prisma.refreshToken.updateMany({
    where: { userId },
    data: {
      isRevoked: true,
      revokedAt: new Date()
    }
  });

  // Deactivate all sessions
  await prisma.session.updateMany({
    where: { userId },
    data: { isActive: false }
  });

  // Remove session from Redis
  await redisClient.del(`session:${userId}`);

  logger.info('All user tokens revoked', { userId });
}

/**
 * Check if token is blacklisted
 */
export async function isTokenBlacklisted(token: string): Promise<boolean> {
  // Check Redis first (faster)
  const redisResult = await redisClient.get(`blacklist:${token}`);
  if (redisResult) {
    return true;
  }

  // Check database
  const dbResult = await prisma.blacklistedToken.findUnique({
    where: { token }
  });

  return !!dbResult;
}

/**
 * Clean up expired tokens (should be run periodically)
 */
export async function cleanupExpiredTokens(): Promise<void> {
  const now = new Date();

  // Clean up expired refresh tokens
  await prisma.refreshToken.deleteMany({
    where: {
      expiresAt: { lt: now }
    }
  });

  // Clean up expired sessions
  await prisma.session.deleteMany({
    where: {
      expiresAt: { lt: now }
    }
  });

  // Clean up expired blacklisted tokens
  await prisma.blacklistedToken.deleteMany({
    where: {
      expiresAt: { lt: now }
    }
  });

  logger.info('Expired tokens cleaned up');
}

/**
 * Get user sessions
 */
export async function getUserSessions(userId: string) {
  return await prisma.session.findMany({
    where: {
      userId,
      isActive: true,
      expiresAt: { gt: new Date() }
    },
    orderBy: { createdAt: 'desc' }
  });
}
