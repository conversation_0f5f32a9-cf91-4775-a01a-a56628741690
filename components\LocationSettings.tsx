import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Switch,
  Alert,
  Modal,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import {
  MapPin,
  Shield,
  Eye,
  EyeOff,
  Clock,
  Target,
  Bell,
  Settings,
  Activity,
  Database,
  Navigation,
  Compass,
  AlertTriangle,
  CheckCircle,
  Info,
} from 'lucide-react-native';
import { LocationService } from '../services/LocationService';
import { AdvancedPrivacyService } from '../services/AdvancedPrivacyService';

interface LocationSettingsProps {
  onSettingsChange?: (settings: any) => void;
}

interface LocationPreferences {
  location_tracking_enabled: boolean;
  background_tracking_enabled: boolean;
  high_accuracy_mode: boolean;
  auto_save_locations: boolean;
  share_locations_publicly: boolean;
  location_precision_level: 'exact' | 'approximate' | 'city' | 'region' | 'country';
  location_history_retention: string;
  geofence_notifications: boolean;
  discovery_notifications: boolean;
  nearby_species_alerts: boolean;
  location_analytics_enabled: boolean;
}

export const LocationSettings: React.FC<LocationSettingsProps> = ({
  onSettingsChange,
}) => {
  const [preferences, setPreferences] = useState<LocationPreferences>({
    location_tracking_enabled: false,
    background_tracking_enabled: false,
    high_accuracy_mode: true,
    auto_save_locations: true,
    share_locations_publicly: false,
    location_precision_level: 'exact',
    location_history_retention: '1 year',
    geofence_notifications: true,
    discovery_notifications: true,
    nearby_species_alerts: true,
    location_analytics_enabled: true,
  });

  const [locationPermission, setLocationPermission] = useState<string>('undetermined');
  const [backgroundPermission, setBackgroundPermission] = useState<string>('undetermined');
  const [isTracking, setIsTracking] = useState(false);
  const [loading, setLoading] = useState(true);
  const [showPrecisionModal, setShowPrecisionModal] = useState(false);

  const locationService = LocationService.getInstance();
  const privacyService = AdvancedPrivacyService.getInstance();

  useEffect(() => {
    loadLocationSettings();
    checkPermissions();
  }, []);

  const loadLocationSettings = async () => {
    try {
      setLoading(true);
      
      // Load privacy preferences related to location
      const privacyPrefs = await privacyService.getAdvancedPrivacyPreferences();
      
      if (privacyPrefs) {
        setPreferences(prev => ({
          ...prev,
          location_precision_level: privacyPrefs.location_precision_level,
          location_history_retention: privacyPrefs.location_history_retention,
          share_locations_publicly: privacyPrefs.allow_third_party_sharing,
          location_analytics_enabled: privacyPrefs.allow_behavioral_analytics,
        }));
      }

      // Load location-specific settings from storage
      // This would typically come from AsyncStorage or user preferences
      
    } catch (error) {
      console.error('Error loading location settings:', error);
    } finally {
      setLoading(false);
    }
  };

  const checkPermissions = async () => {
    try {
      // Check location permissions
      const { status: foregroundStatus } = await locationService.getForegroundPermissionsAsync();
      setLocationPermission(foregroundStatus);

      const { status: backgroundStatus } = await locationService.getBackgroundPermissionsAsync();
      setBackgroundPermission(backgroundStatus);

      // Check if tracking is active
      setIsTracking(locationService.isTracking);
    } catch (error) {
      console.error('Error checking permissions:', error);
    }
  };

  const updatePreference = async (key: keyof LocationPreferences, value: any) => {
    const updatedPreferences = { ...preferences, [key]: value };
    setPreferences(updatedPreferences);

    try {
      // Update privacy service for location-related preferences
      if (['location_precision_level', 'location_history_retention', 'share_locations_publicly', 'location_analytics_enabled'].includes(key)) {
        await privacyService.updateAdvancedPrivacyPreferences({
          [key]: value,
        });
      }

      // Handle specific preference changes
      if (key === 'location_tracking_enabled') {
        if (value) {
          await enableLocationTracking();
        } else {
          await disableLocationTracking();
        }
      }

      if (key === 'background_tracking_enabled') {
        if (value) {
          await enableBackgroundTracking();
        } else {
          await disableBackgroundTracking();
        }
      }

      if (onSettingsChange) {
        onSettingsChange(updatedPreferences);
      }
    } catch (error) {
      console.error('Error updating preference:', error);
      // Revert on error
      setPreferences(preferences);
      Alert.alert('Error', 'Failed to update location setting');
    }
  };

  const enableLocationTracking = async () => {
    try {
      const initialized = await locationService.initialize();
      if (!initialized) {
        Alert.alert(
          'Permission Required',
          'Location permission is required to enable tracking.',
          [
            { text: 'Cancel', style: 'cancel' },
            { text: 'Settings', onPress: () => requestLocationPermission() }
          ]
        );
        setPreferences(prev => ({ ...prev, location_tracking_enabled: false }));
        return;
      }

      setIsTracking(true);
    } catch (error) {
      Alert.alert('Error', 'Failed to enable location tracking');
      setPreferences(prev => ({ ...prev, location_tracking_enabled: false }));
    }
  };

  const disableLocationTracking = async () => {
    try {
      await locationService.stopLocationTracking();
      setIsTracking(false);
    } catch (error) {
      console.error('Error disabling location tracking:', error);
    }
  };

  const enableBackgroundTracking = async () => {
    if (backgroundPermission !== 'granted') {
      Alert.alert(
        'Background Permission Required',
        'Background location permission is required for continuous tracking.',
        [
          { text: 'Cancel', style: 'cancel' },
          { text: 'Grant Permission', onPress: () => requestBackgroundPermission() }
        ]
      );
      setPreferences(prev => ({ ...prev, background_tracking_enabled: false }));
      return;
    }

    try {
      const success = await locationService.startLocationTracking();
      if (!success) {
        throw new Error('Failed to start background tracking');
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to enable background tracking');
      setPreferences(prev => ({ ...prev, background_tracking_enabled: false }));
    }
  };

  const disableBackgroundTracking = async () => {
    try {
      await locationService.stopLocationTracking();
    } catch (error) {
      console.error('Error disabling background tracking:', error);
    }
  };

  const requestLocationPermission = async () => {
    try {
      const { status } = await locationService.requestForegroundPermissionsAsync();
      setLocationPermission(status);
      
      if (status === 'granted') {
        await enableLocationTracking();
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to request location permission');
    }
  };

  const requestBackgroundPermission = async () => {
    try {
      const { status } = await locationService.requestBackgroundPermissionsAsync();
      setBackgroundPermission(status);
      
      if (status === 'granted') {
        await enableBackgroundTracking();
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to request background permission');
    }
  };

  const clearLocationHistory = async () => {
    Alert.alert(
      'Clear Location History',
      'This will permanently delete all your saved location data. This action cannot be undone.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Clear',
          style: 'destructive',
          onPress: async () => {
            try {
              // This would call a service method to clear location history
              Alert.alert('Success', 'Location history cleared successfully');
            } catch (error) {
              Alert.alert('Error', 'Failed to clear location history');
            }
          }
        }
      ]
    );
  };

  const getPermissionStatus = (status: string) => {
    switch (status) {
      case 'granted': return { text: 'Granted', color: '#10B981', icon: CheckCircle };
      case 'denied': return { text: 'Denied', color: '#EF4444', icon: AlertTriangle };
      case 'undetermined': return { text: 'Not Set', color: '#6B7280', icon: Info };
      default: return { text: 'Unknown', color: '#6B7280', icon: Info };
    }
  };

  const precisionLevels = [
    { key: 'exact', label: 'Exact Location', description: 'GPS coordinates with full precision' },
    { key: 'approximate', label: 'Approximate', description: 'Location with ~500m accuracy' },
    { key: 'city', label: 'City Level', description: 'Location rounded to city level' },
    { key: 'region', label: 'Region Level', description: 'Location rounded to region level' },
    { key: 'country', label: 'Country Level', description: 'Location rounded to country level' },
  ];

  const renderPrecisionModal = () => (
    <Modal
      visible={showPrecisionModal}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={() => setShowPrecisionModal(false)}
    >
      <View style={styles.modalContainer}>
        <View style={styles.modalHeader}>
          <Text style={styles.modalTitle}>Location Precision</Text>
          <TouchableOpacity onPress={() => setShowPrecisionModal(false)}>
            <Text style={styles.closeButton}>Done</Text>
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.modalContent}>
          <Text style={styles.modalDescription}>
            Choose how precise your location data should be. Lower precision provides better privacy.
          </Text>

          {precisionLevels.map((level) => (
            <TouchableOpacity
              key={level.key}
              style={[
                styles.precisionOption,
                preferences.location_precision_level === level.key && styles.selectedPrecisionOption
              ]}
              onPress={() => {
                updatePreference('location_precision_level', level.key as any);
                setShowPrecisionModal(false);
              }}
            >
              <View style={styles.precisionInfo}>
                <Text style={styles.precisionLabel}>{level.label}</Text>
                <Text style={styles.precisionDescription}>{level.description}</Text>
              </View>
              {preferences.location_precision_level === level.key && (
                <CheckCircle size={20} color="#10B981" />
              )}
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>
    </Modal>
  );

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <MapPin size={32} color="#3B82F6" />
        <Text style={styles.loadingText}>Loading location settings...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Header */}
      <LinearGradient
        colors={['#10B981', '#059669']}
        style={styles.header}
      >
        <MapPin size={32} color="white" />
        <Text style={styles.headerTitle}>Location Settings</Text>
        <Text style={styles.headerSubtitle}>
          Control how your location data is collected and used
        </Text>
      </LinearGradient>

      <ScrollView style={styles.content}>
        {/* Permission Status */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Permission Status</Text>
          
          <View style={styles.permissionCard}>
            <View style={styles.permissionInfo}>
              <Text style={styles.permissionLabel}>Location Access</Text>
              <Text style={styles.permissionDescription}>
                Required for species identification and discovery
              </Text>
            </View>
            <View style={styles.permissionStatus}>
              {(() => {
                const status = getPermissionStatus(locationPermission);
                const Icon = status.icon;
                return (
                  <>
                    <Icon size={16} color={status.color} />
                    <Text style={[styles.permissionStatusText, { color: status.color }]}>
                      {status.text}
                    </Text>
                  </>
                );
              })()}
            </View>
          </View>

          <View style={styles.permissionCard}>
            <View style={styles.permissionInfo}>
              <Text style={styles.permissionLabel}>Background Location</Text>
              <Text style={styles.permissionDescription}>
                Enables automatic location tracking
              </Text>
            </View>
            <View style={styles.permissionStatus}>
              {(() => {
                const status = getPermissionStatus(backgroundPermission);
                const Icon = status.icon;
                return (
                  <>
                    <Icon size={16} color={status.color} />
                    <Text style={[styles.permissionStatusText, { color: status.color }]}>
                      {status.text}
                    </Text>
                  </>
                );
              })()}
            </View>
          </View>
        </View>

        {/* Tracking Settings */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Location Tracking</Text>
          
          <SettingRow
            icon={Navigation}
            title="Location Tracking"
            description="Enable location-based features and species discovery"
            value={preferences.location_tracking_enabled}
            onValueChange={(value) => updatePreference('location_tracking_enabled', value)}
          />

          <SettingRow
            icon={Activity}
            title="Background Tracking"
            description="Continue tracking location when app is in background"
            value={preferences.background_tracking_enabled}
            onValueChange={(value) => updatePreference('background_tracking_enabled', value)}
            disabled={!preferences.location_tracking_enabled}
          />

          <SettingRow
            icon={Target}
            title="High Accuracy Mode"
            description="Use GPS for more precise location data"
            value={preferences.high_accuracy_mode}
            onValueChange={(value) => updatePreference('high_accuracy_mode', value)}
          />
        </View>

        {/* Privacy Settings */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Privacy & Data</Text>
          
          <TouchableOpacity
            style={styles.settingRow}
            onPress={() => setShowPrecisionModal(true)}
          >
            <Shield size={20} color="#3B82F6" />
            <View style={styles.settingContent}>
              <Text style={styles.settingTitle}>Location Precision</Text>
              <Text style={styles.settingDescription}>
                Current: {precisionLevels.find(l => l.key === preferences.location_precision_level)?.label}
              </Text>
            </View>
            <Text style={styles.settingValue}>Configure</Text>
          </TouchableOpacity>

          <SettingRow
            icon={Database}
            title="Auto-Save Locations"
            description="Automatically save visited locations"
            value={preferences.auto_save_locations}
            onValueChange={(value) => updatePreference('auto_save_locations', value)}
          />

          <SettingRow
            icon={Eye}
            title="Share Publicly"
            description="Allow sharing location data with community"
            value={preferences.share_locations_publicly}
            onValueChange={(value) => updatePreference('share_locations_publicly', value)}
          />

          <SettingRow
            icon={Activity}
            title="Location Analytics"
            description="Enable location-based analytics and insights"
            value={preferences.location_analytics_enabled}
            onValueChange={(value) => updatePreference('location_analytics_enabled', value)}
          />
        </View>

        {/* Notification Settings */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Location Notifications</Text>
          
          <SettingRow
            icon={Bell}
            title="Geofence Alerts"
            description="Notify when entering/leaving marked areas"
            value={preferences.geofence_notifications}
            onValueChange={(value) => updatePreference('geofence_notifications', value)}
          />

          <SettingRow
            icon={Compass}
            title="Discovery Notifications"
            description="Notify about new discoveries in your area"
            value={preferences.discovery_notifications}
            onValueChange={(value) => updatePreference('discovery_notifications', value)}
          />

          <SettingRow
            icon={MapPin}
            title="Nearby Species Alerts"
            description="Notify about species sightings nearby"
            value={preferences.nearby_species_alerts}
            onValueChange={(value) => updatePreference('nearby_species_alerts', value)}
          />
        </View>

        {/* Data Management */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Data Management</Text>
          
          <View style={styles.dataInfo}>
            <Text style={styles.dataLabel}>Location History Retention</Text>
            <Text style={styles.dataValue}>{preferences.location_history_retention}</Text>
          </View>

          <TouchableOpacity
            style={styles.dangerButton}
            onPress={clearLocationHistory}
          >
            <AlertTriangle size={20} color="#EF4444" />
            <Text style={styles.dangerButtonText}>Clear Location History</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>

      {/* Precision Modal */}
      {renderPrecisionModal()}
    </View>
  );
};

interface SettingRowProps {
  icon: any;
  title: string;
  description: string;
  value: boolean;
  onValueChange: (value: boolean) => void;
  disabled?: boolean;
}

const SettingRow: React.FC<SettingRowProps> = ({
  icon: Icon,
  title,
  description,
  value,
  onValueChange,
  disabled = false,
}) => (
  <View style={[styles.settingRow, disabled && styles.disabledRow]}>
    <Icon size={20} color={disabled ? '#9CA3AF' : '#3B82F6'} />
    <View style={styles.settingContent}>
      <Text style={[styles.settingTitle, disabled && styles.disabledText]}>{title}</Text>
      <Text style={[styles.settingDescription, disabled && styles.disabledText]}>
        {description}
      </Text>
    </View>
    <Switch
      value={value}
      onValueChange={onValueChange}
      disabled={disabled}
      trackColor={{ false: '#E5E7EB', true: '#10B981' }}
      thumbColor={value ? '#FFFFFF' : '#F3F4F6'}
    />
  </View>
);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#F9FAFB',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#6B7280',
  },
  header: {
    padding: 24,
    paddingTop: 60,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: 'white',
    marginTop: 8,
  },
  headerSubtitle: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.8)',
    marginTop: 4,
    textAlign: 'center',
  },
  content: {
    flex: 1,
    padding: 20,
  },
  section: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 16,
  },
  permissionCard: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
  },
  permissionInfo: {
    flex: 1,
  },
  permissionLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: '#111827',
    marginBottom: 2,
  },
  permissionDescription: {
    fontSize: 14,
    color: '#6B7280',
  },
  permissionStatus: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  permissionStatusText: {
    fontSize: 14,
    fontWeight: '500',
  },
  settingRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
    gap: 12,
  },
  disabledRow: {
    opacity: 0.5,
  },
  settingContent: {
    flex: 1,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#111827',
    marginBottom: 2,
  },
  settingDescription: {
    fontSize: 14,
    color: '#6B7280',
    lineHeight: 18,
  },
  disabledText: {
    color: '#9CA3AF',
  },
  settingValue: {
    fontSize: 14,
    color: '#3B82F6',
    fontWeight: '500',
  },
  dataInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
  },
  dataLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: '#111827',
  },
  dataValue: {
    fontSize: 14,
    color: '#6B7280',
  },
  dangerButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FEF2F2',
    padding: 16,
    borderRadius: 8,
    marginTop: 12,
    gap: 8,
  },
  dangerButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#EF4444',
  },
  modalContainer: {
    flex: 1,
    backgroundColor: 'white',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827',
  },
  closeButton: {
    fontSize: 16,
    color: '#10B981',
    fontWeight: '500',
  },
  modalContent: {
    flex: 1,
    padding: 20,
  },
  modalDescription: {
    fontSize: 16,
    color: '#6B7280',
    lineHeight: 22,
    marginBottom: 24,
  },
  precisionOption: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#E5E7EB',
    marginBottom: 12,
  },
  selectedPrecisionOption: {
    borderColor: '#10B981',
    backgroundColor: '#ECFDF5',
  },
  precisionInfo: {
    flex: 1,
  },
  precisionLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: '#111827',
    marginBottom: 4,
  },
  precisionDescription: {
    fontSize: 14,
    color: '#6B7280',
  },
});

export default LocationSettings;
