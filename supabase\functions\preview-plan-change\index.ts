import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import Stripe from 'https://esm.sh/stripe@12.0.0?target=deno'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

const stripe = new Stripe(Deno.env.get('STRIPE_SECRET_KEY') || '', {
  apiVersion: '2023-10-16',
})

const supabaseClient = createClient(
  Deno.env.get('SUPABASE_URL') ?? '',
  Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
)

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const authHeader = req.headers.get('Authorization')!
    const token = authHeader.replace('Bearer ', '')

    const { data: { user }, error: authError } = await supabaseClient.auth.getUser(token)
    if (authError || !user) {
      throw new Error('Unauthorized')
    }

    const { userId, newPlanId, billingPeriod } = await req.json()

    // Get current subscription
    const { data: currentSub, error: subError } = await supabaseClient
      .from('user_subscriptions')
      .select(`
        *,
        plan:subscription_plans(*)
      `)
      .eq('user_id', userId)
      .in('status', ['active', 'trialing'])
      .single()

    if (subError || !currentSub) {
      throw new Error('No active subscription found')
    }

    // Get new plan details
    const { data: newPlan, error: planError } = await supabaseClient
      .from('subscription_plans')
      .select('*')
      .eq('id', newPlanId)
      .single()

    if (planError || !newPlan) {
      throw new Error('New plan not found')
    }

    // Calculate proration using Stripe's preview
    const stripeSubscription = await stripe.subscriptions.retrieve(currentSub.stripe_subscription_id)
    
    const newPriceId = billingPeriod === 'yearly' 
      ? newPlan.stripe_price_id_yearly 
      : newPlan.stripe_price_id_monthly

    if (!newPriceId) {
      throw new Error('Price ID not configured for the selected plan and billing period')
    }

    // Preview the subscription change
    const upcomingInvoice = await stripe.invoices.retrieveUpcoming({
      customer: stripeSubscription.customer as string,
      subscription: stripeSubscription.id,
      subscription_items: [{
        id: stripeSubscription.items.data[0].id,
        price: newPriceId,
      }],
      subscription_proration_behavior: 'create_prorations',
    })

    // Calculate the proration amount
    const prorationAmount = upcomingInvoice.amount_due / 100 // Convert from cents

    // Determine next billing date
    const nextBillingDate = new Date(stripeSubscription.current_period_end * 1000)

    // Calculate savings/cost difference
    const currentPrice = billingPeriod === 'yearly' 
      ? currentSub.plan.price_yearly 
      : currentSub.plan.price_monthly

    const newPrice = billingPeriod === 'yearly' 
      ? newPlan.price_yearly 
      : newPlan.price_monthly

    const priceDifference = newPrice - currentPrice

    // Feature comparison
    const featureComparison = {
      current_features: currentSub.plan.features || [],
      new_features: newPlan.features || [],
      added_features: (newPlan.features || []).filter(
        (feature: string) => !(currentSub.plan.features || []).includes(feature)
      ),
      removed_features: (currentSub.plan.features || []).filter(
        (feature: string) => !(newPlan.features || []).includes(feature)
      )
    }

    // Usage limit changes
    const usageLimitChange = {
      current_limit: currentSub.plan.max_identifications_per_month,
      new_limit: newPlan.max_identifications_per_month,
      is_upgrade: newPlan.max_identifications_per_month > currentSub.plan.max_identifications_per_month ||
                  newPlan.max_identifications_per_month === -1
    }

    const preview = {
      current_plan: {
        id: currentSub.plan.id,
        name: currentSub.plan.name,
        price: currentPrice,
        features: currentSub.plan.features,
        max_identifications: currentSub.plan.max_identifications_per_month
      },
      new_plan: {
        id: newPlan.id,
        name: newPlan.name,
        price: newPrice,
        features: newPlan.features,
        max_identifications: newPlan.max_identifications_per_month
      },
      billing_period: billingPeriod,
      proration_amount: prorationAmount,
      price_difference: priceDifference,
      effective_date: new Date().toISOString(),
      next_billing_date: nextBillingDate.toISOString(),
      current_period_end: new Date(stripeSubscription.current_period_end * 1000).toISOString(),
      feature_comparison: featureComparison,
      usage_limit_change: usageLimitChange,
      is_upgrade: newPrice > currentPrice,
      is_downgrade: newPrice < currentPrice,
      immediate_charge: prorationAmount > 0,
      immediate_credit: prorationAmount < 0,
      trial_info: {
        is_in_trial: stripeSubscription.status === 'trialing',
        trial_ends_at: stripeSubscription.trial_end 
          ? new Date(stripeSubscription.trial_end * 1000).toISOString() 
          : null
      }
    }

    return new Response(
      JSON.stringify(preview),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      }
    )

  } catch (error: any) {
    console.error('Error previewing plan change:', error)
    return new Response(
      JSON.stringify({ error: error.message }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400,
      }
    )
  }
})

// Helper function to format currency
function formatCurrency(amount: number, currency: string = 'USD'): string {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency,
  }).format(amount)
}

// Helper function to calculate time remaining in period
function calculateTimeRemaining(periodEnd: number): {
  days: number;
  hours: number;
  percentage: number;
} {
  const now = Date.now()
  const endTime = periodEnd * 1000
  const remaining = endTime - now

  if (remaining <= 0) {
    return { days: 0, hours: 0, percentage: 0 }
  }

  const days = Math.floor(remaining / (1000 * 60 * 60 * 24))
  const hours = Math.floor((remaining % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
  
  // Calculate percentage of period remaining (assuming 30-day period)
  const totalPeriodMs = 30 * 24 * 60 * 60 * 1000
  const percentage = Math.min(100, (remaining / totalPeriodMs) * 100)

  return { days, hours, percentage }
}
