import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
  Modal,
  Image,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import * as ImagePicker from 'expo-image-picker';
import * as DocumentPicker from 'expo-document-picker';
import {
  MessageSquare,
  Bug,
  Lightbulb,
  Users,
  Activity,
  Camera,
  Paperclip,
  X,
  Send,
  Star,
  AlertTriangle,
  CheckCircle,
} from 'lucide-react-native';
import { BetaTestingService, BetaProgram, BetaParticipant } from '../services/BetaTestingService';

interface BetaFeedbackFormProps {
  visible: boolean;
  onClose: () => void;
  programId?: string;
  onSubmitted?: () => void;
}

export const BetaFeedbackForm: React.FC<BetaFeedbackFormProps> = ({
  visible,
  onClose,
  programId,
  onSubmitted,
}) => {
  const [feedbackType, setFeedbackType] = useState<string>('general');
  const [category, setCategory] = useState<string>('');
  const [priority, setPriority] = useState<string>('medium');
  const [title, setTitle] = useState<string>('');
  const [description, setDescription] = useState<string>('');
  const [stepsToReproduce, setStepsToReproduce] = useState<string>('');
  const [expectedBehavior, setExpectedBehavior] = useState<string>('');
  const [actualBehavior, setActualBehavior] = useState<string>('');
  const [screenshots, setScreenshots] = useState<string[]>([]);
  const [attachments, setAttachments] = useState<any[]>([]);
  const [submitting, setSubmitting] = useState(false);
  const [programs, setPrograms] = useState<BetaProgram[]>([]);
  const [selectedProgramId, setSelectedProgramId] = useState<string>(programId || '');

  const betaTestingService = BetaTestingService.getInstance();

  useEffect(() => {
    if (visible) {
      loadUserPrograms();
      resetForm();
    }
  }, [visible]);

  const loadUserPrograms = async () => {
    try {
      const participations = await betaTestingService.getUserParticipations();
      const activeParticipations = participations.filter(p => p.status === 'active');
      
      // In a real implementation, you'd fetch the program details
      // For now, we'll create mock program data
      const mockPrograms = activeParticipations.map(p => ({
        id: p.program_id,
        program_key: `program_${p.program_id}`,
        program_name: `Beta Program ${p.program_id.slice(0, 8)}`,
        description: 'Beta testing program',
        program_type: 'feature' as const,
        status: 'active' as const,
        current_participants: 0,
        auto_approve: false,
        eligibility_criteria: {},
        target_audience: {},
        feature_flags: {},
        app_version_requirements: {},
        platform_restrictions: [],
        feedback_enabled: true,
        feedback_frequency: 'weekly' as const,
        required_feedback_types: [],
        tags: [],
        priority: 0,
      }));
      
      setPrograms(mockPrograms);
      
      if (!selectedProgramId && mockPrograms.length > 0) {
        setSelectedProgramId(mockPrograms[0].id);
      }
    } catch (error) {
      console.error('Error loading user programs:', error);
    }
  };

  const resetForm = () => {
    setFeedbackType('general');
    setCategory('');
    setPriority('medium');
    setTitle('');
    setDescription('');
    setStepsToReproduce('');
    setExpectedBehavior('');
    setActualBehavior('');
    setScreenshots([]);
    setAttachments([]);
  };

  const handleSubmit = async () => {
    if (!selectedProgramId) {
      Alert.alert('Error', 'Please select a beta program');
      return;
    }

    if (!title.trim() || !description.trim()) {
      Alert.alert('Error', 'Please provide a title and description');
      return;
    }

    try {
      setSubmitting(true);

      await betaTestingService.submitFeedback(selectedProgramId, {
        feedback_type: feedbackType,
        category: category || undefined,
        priority,
        title: title.trim(),
        description: description.trim(),
        steps_to_reproduce: stepsToReproduce.trim() || undefined,
        expected_behavior: expectedBehavior.trim() || undefined,
        actual_behavior: actualBehavior.trim() || undefined,
        screenshots,
        videos: [], // Would handle video uploads
        logs: attachments,
      });

      Alert.alert(
        'Feedback Submitted',
        'Thank you for your feedback! It has been submitted to the beta program team.',
        [{ text: 'OK', onPress: () => {
          onClose();
          onSubmitted?.();
        }}]
      );
    } catch (error) {
      Alert.alert('Error', 'Failed to submit feedback. Please try again.');
    } finally {
      setSubmitting(false);
    }
  };

  const handleAddScreenshot = async () => {
    try {
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Permission Required', 'Please grant camera roll permissions to add screenshots');
        return;
      }

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        setScreenshots(prev => [...prev, result.assets[0].uri]);
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to add screenshot');
    }
  };

  const handleAddAttachment = async () => {
    try {
      const result = await DocumentPicker.getDocumentAsync({
        type: '*/*',
        copyToCacheDirectory: true,
      });

      if (!result.canceled && result.assets[0]) {
        setAttachments(prev => [...prev, result.assets[0]]);
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to add attachment');
    }
  };

  const removeScreenshot = (index: number) => {
    setScreenshots(prev => prev.filter((_, i) => i !== index));
  };

  const removeAttachment = (index: number) => {
    setAttachments(prev => prev.filter((_, i) => i !== index));
  };

  const feedbackTypes = [
    { key: 'bug_report', label: 'Bug Report', icon: Bug, color: '#EF4444' },
    { key: 'feature_request', label: 'Feature Request', icon: Lightbulb, color: '#F59E0B' },
    { key: 'usability', label: 'Usability Issue', icon: Users, color: '#3B82F6' },
    { key: 'performance', label: 'Performance Issue', icon: Activity, color: '#8B5CF6' },
    { key: 'general', label: 'General Feedback', icon: MessageSquare, color: '#10B981' },
  ];

  const priorities = [
    { key: 'low', label: 'Low', color: '#6B7280' },
    { key: 'medium', label: 'Medium', color: '#3B82F6' },
    { key: 'high', label: 'High', color: '#F59E0B' },
    { key: 'critical', label: 'Critical', color: '#EF4444' },
  ];

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <View style={styles.container}>
        {/* Header */}
        <LinearGradient
          colors={['#10B981', '#059669']}
          style={styles.header}
        >
          <View style={styles.headerContent}>
            <Text style={styles.headerTitle}>Submit Feedback</Text>
            <TouchableOpacity onPress={onClose} style={styles.closeButton}>
              <X size={24} color="white" />
            </TouchableOpacity>
          </View>
        </LinearGradient>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {/* Program Selection */}
          {programs.length > 1 && (
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Beta Program</Text>
              <View style={styles.programSelector}>
                {programs.map((program) => (
                  <TouchableOpacity
                    key={program.id}
                    style={[
                      styles.programOption,
                      selectedProgramId === program.id && styles.selectedProgramOption
                    ]}
                    onPress={() => setSelectedProgramId(program.id)}
                  >
                    <Text style={[
                      styles.programOptionText,
                      selectedProgramId === program.id && styles.selectedProgramOptionText
                    ]}>
                      {program.program_name}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>
          )}

          {/* Feedback Type */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Feedback Type</Text>
            <View style={styles.typeSelector}>
              {feedbackTypes.map((type) => {
                const Icon = type.icon;
                return (
                  <TouchableOpacity
                    key={type.key}
                    style={[
                      styles.typeOption,
                      feedbackType === type.key && styles.selectedTypeOption
                    ]}
                    onPress={() => setFeedbackType(type.key)}
                  >
                    <Icon 
                      size={20} 
                      color={feedbackType === type.key ? 'white' : type.color} 
                    />
                    <Text style={[
                      styles.typeOptionText,
                      feedbackType === type.key && styles.selectedTypeOptionText
                    ]}>
                      {type.label}
                    </Text>
                  </TouchableOpacity>
                );
              })}
            </View>
          </View>

          {/* Priority */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Priority</Text>
            <View style={styles.prioritySelector}>
              {priorities.map((priorityOption) => (
                <TouchableOpacity
                  key={priorityOption.key}
                  style={[
                    styles.priorityOption,
                    { borderColor: priorityOption.color },
                    priority === priorityOption.key && { backgroundColor: priorityOption.color }
                  ]}
                  onPress={() => setPriority(priorityOption.key)}
                >
                  <Text style={[
                    styles.priorityOptionText,
                    { color: priority === priorityOption.key ? 'white' : priorityOption.color }
                  ]}>
                    {priorityOption.label}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>

          {/* Title */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Title *</Text>
            <TextInput
              style={styles.textInput}
              value={title}
              onChangeText={setTitle}
              placeholder="Brief summary of your feedback"
              placeholderTextColor="#9CA3AF"
            />
          </View>

          {/* Description */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Description *</Text>
            <TextInput
              style={[styles.textInput, styles.multilineInput]}
              value={description}
              onChangeText={setDescription}
              placeholder="Detailed description of your feedback"
              placeholderTextColor="#9CA3AF"
              multiline
              numberOfLines={4}
            />
          </View>

          {/* Bug Report Specific Fields */}
          {feedbackType === 'bug_report' && (
            <>
              <View style={styles.section}>
                <Text style={styles.sectionTitle}>Steps to Reproduce</Text>
                <TextInput
                  style={[styles.textInput, styles.multilineInput]}
                  value={stepsToReproduce}
                  onChangeText={setStepsToReproduce}
                  placeholder="1. Go to...\n2. Click on...\n3. See error"
                  placeholderTextColor="#9CA3AF"
                  multiline
                  numberOfLines={3}
                />
              </View>

              <View style={styles.section}>
                <Text style={styles.sectionTitle}>Expected Behavior</Text>
                <TextInput
                  style={[styles.textInput, styles.multilineInput]}
                  value={expectedBehavior}
                  onChangeText={setExpectedBehavior}
                  placeholder="What should have happened?"
                  placeholderTextColor="#9CA3AF"
                  multiline
                  numberOfLines={2}
                />
              </View>

              <View style={styles.section}>
                <Text style={styles.sectionTitle}>Actual Behavior</Text>
                <TextInput
                  style={[styles.textInput, styles.multilineInput]}
                  value={actualBehavior}
                  onChangeText={setActualBehavior}
                  placeholder="What actually happened?"
                  placeholderTextColor="#9CA3AF"
                  multiline
                  numberOfLines={2}
                />
              </View>
            </>
          )}

          {/* Screenshots */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Screenshots</Text>
            <TouchableOpacity style={styles.addButton} onPress={handleAddScreenshot}>
              <Camera size={20} color="#10B981" />
              <Text style={styles.addButtonText}>Add Screenshot</Text>
            </TouchableOpacity>
            
            {screenshots.length > 0 && (
              <View style={styles.attachmentsList}>
                {screenshots.map((uri, index) => (
                  <View key={index} style={styles.screenshotItem}>
                    <Image source={{ uri }} style={styles.screenshotImage} />
                    <TouchableOpacity
                      style={styles.removeButton}
                      onPress={() => removeScreenshot(index)}
                    >
                      <X size={16} color="white" />
                    </TouchableOpacity>
                  </View>
                ))}
              </View>
            )}
          </View>

          {/* Attachments */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Attachments</Text>
            <TouchableOpacity style={styles.addButton} onPress={handleAddAttachment}>
              <Paperclip size={20} color="#10B981" />
              <Text style={styles.addButtonText}>Add File</Text>
            </TouchableOpacity>
            
            {attachments.length > 0 && (
              <View style={styles.attachmentsList}>
                {attachments.map((file, index) => (
                  <View key={index} style={styles.attachmentItem}>
                    <Text style={styles.attachmentName} numberOfLines={1}>
                      {file.name}
                    </Text>
                    <TouchableOpacity
                      style={styles.removeAttachmentButton}
                      onPress={() => removeAttachment(index)}
                    >
                      <X size={16} color="#EF4444" />
                    </TouchableOpacity>
                  </View>
                ))}
              </View>
            )}
          </View>

          {/* Submit Button */}
          <TouchableOpacity
            style={[
              styles.submitButton,
              (!title.trim() || !description.trim() || submitting) && styles.disabledButton
            ]}
            onPress={handleSubmit}
            disabled={!title.trim() || !description.trim() || submitting}
          >
            <Send size={20} color="white" />
            <Text style={styles.submitButtonText}>
              {submitting ? 'Submitting...' : 'Submit Feedback'}
            </Text>
          </TouchableOpacity>
        </ScrollView>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  header: {
    paddingTop: 60,
    paddingBottom: 20,
    paddingHorizontal: 20,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: 'white',
  },
  closeButton: {
    padding: 4,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 12,
  },
  programSelector: {
    gap: 8,
  },
  programOption: {
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 8,
    padding: 12,
    backgroundColor: 'white',
  },
  selectedProgramOption: {
    borderColor: '#10B981',
    backgroundColor: '#ECFDF5',
  },
  programOptionText: {
    fontSize: 14,
    color: '#6B7280',
  },
  selectedProgramOptionText: {
    color: '#10B981',
    fontWeight: '500',
  },
  typeSelector: {
    gap: 8,
  },
  typeOption: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 8,
    padding: 12,
    backgroundColor: 'white',
    gap: 8,
  },
  selectedTypeOption: {
    backgroundColor: '#10B981',
    borderColor: '#10B981',
  },
  typeOptionText: {
    fontSize: 14,
    color: '#6B7280',
  },
  selectedTypeOptionText: {
    color: 'white',
    fontWeight: '500',
  },
  prioritySelector: {
    flexDirection: 'row',
    gap: 8,
  },
  priorityOption: {
    flex: 1,
    borderWidth: 1,
    borderRadius: 8,
    padding: 8,
    alignItems: 'center',
    backgroundColor: 'white',
  },
  priorityOptionText: {
    fontSize: 12,
    fontWeight: '500',
  },
  textInput: {
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    color: '#111827',
    backgroundColor: 'white',
  },
  multilineInput: {
    textAlignVertical: 'top',
    minHeight: 80,
  },
  addButton: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#10B981',
    borderRadius: 8,
    padding: 12,
    backgroundColor: 'white',
    gap: 8,
  },
  addButtonText: {
    fontSize: 14,
    color: '#10B981',
    fontWeight: '500',
  },
  attachmentsList: {
    marginTop: 12,
    gap: 8,
  },
  screenshotItem: {
    position: 'relative',
  },
  screenshotImage: {
    width: 100,
    height: 100,
    borderRadius: 8,
  },
  removeButton: {
    position: 'absolute',
    top: 4,
    right: 4,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    borderRadius: 12,
    padding: 4,
  },
  attachmentItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 12,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  attachmentName: {
    flex: 1,
    fontSize: 14,
    color: '#111827',
  },
  removeAttachmentButton: {
    padding: 4,
  },
  submitButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#10B981',
    borderRadius: 8,
    padding: 16,
    gap: 8,
    marginBottom: 40,
  },
  disabledButton: {
    backgroundColor: '#9CA3AF',
  },
  submitButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: 'white',
  },
});

export default BetaFeedbackForm;
