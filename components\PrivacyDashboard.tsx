import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Switch,
  Alert,
  RefreshControl,
  ActivityIndicator,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import {
  Shield,
  Download,
  Trash2,
  Eye,
  EyeOff,
  MapPin,
  Mail,
  Users,
  Database,
  FileText,
  Settings,
  Lock,
  AlertTriangle,
  CheckCircle,
  Clock,
  Info,
} from 'lucide-react-native';
import {
  PrivacyManagementService,
  PrivacySettings,
  DataExportRequest,
  DataDeletionRequest,
  DataCategory,
} from '../services/PrivacyManagementService';

interface PrivacyDashboardProps {
  onSettingsChange?: (settings: PrivacySettings) => void;
}

export const PrivacyDashboard: React.FC<PrivacyDashboardProps> = ({
  onSettingsChange,
}) => {
  const [privacySettings, setPrivacySettings] = useState<PrivacySettings | null>(null);
  const [exportRequests, setExportRequests] = useState<DataExportRequest[]>([]);
  const [deletionRequests, setDeletionRequests] = useState<DataDeletionRequest[]>([]);
  const [dataCategories, setDataCategories] = useState<DataCategory[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [activeTab, setActiveTab] = useState<'settings' | 'export' | 'deletion'>('settings');

  useEffect(() => {
    loadPrivacyData();
  }, []);

  const loadPrivacyData = async () => {
    try {
      const [settings, exports, deletions, categories] = await Promise.all([
        PrivacyManagementService.getPrivacySettings(),
        PrivacyManagementService.getDataExportRequests(),
        PrivacyManagementService.getDataDeletionRequests(),
        PrivacyManagementService.getDataCategories(),
      ]);

      setPrivacySettings(settings);
      setExportRequests(exports);
      setDeletionRequests(deletions);
      setDataCategories(categories);
    } catch (error) {
      console.error('Error loading privacy data:', error);
      Alert.alert('Error', 'Failed to load privacy settings');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const onRefresh = () => {
    setRefreshing(true);
    loadPrivacyData();
  };

  const updatePrivacySetting = async (key: keyof PrivacySettings, value: any) => {
    if (!privacySettings) return;

    const updatedSettings = { ...privacySettings, [key]: value };
    setPrivacySettings(updatedSettings);

    const success = await PrivacyManagementService.updatePrivacySettings({ [key]: value });
    
    if (success) {
      if (onSettingsChange) {
        onSettingsChange(updatedSettings);
      }
    } else {
      // Revert on failure
      setPrivacySettings(privacySettings);
      Alert.alert('Error', 'Failed to update privacy setting');
    }
  };

  const requestDataExport = async (type: DataExportRequest['request_type']) => {
    Alert.alert(
      'Request Data Export',
      `This will create a ${type} export of your data. You'll be notified when it's ready for download.`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Request Export',
          onPress: async () => {
            const exportId = await PrivacyManagementService.requestDataExport(type);
            if (exportId) {
              Alert.alert('Success', 'Data export requested. You\'ll be notified when it\'s ready.');
              loadPrivacyData();
            } else {
              Alert.alert('Error', 'Failed to request data export');
            }
          }
        }
      ]
    );
  };

  const requestDataDeletion = async () => {
    Alert.alert(
      'Request Data Deletion',
      'This will permanently delete selected data from your account. This action cannot be undone.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Continue',
          style: 'destructive',
          onPress: () => {
            // Navigate to data deletion selection screen
            // For now, show a simple category selection
            showDataDeletionOptions();
          }
        }
      ]
    );
  };

  const showDataDeletionOptions = () => {
    const nonEssentialCategories = dataCategories.filter(cat => !cat.is_essential);
    
    Alert.alert(
      'Select Data to Delete',
      'Choose which categories of data you want to delete:',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete Identifications',
          onPress: () => confirmDeletion(['identification_data'])
        },
        {
          text: 'Delete Media',
          onPress: () => confirmDeletion(['media_data'])
        },
        {
          text: 'Delete All Non-Essential',
          style: 'destructive',
          onPress: () => confirmDeletion(nonEssentialCategories.map(cat => cat.name))
        }
      ]
    );
  };

  const confirmDeletion = async (categories: string[]) => {
    const deletionId = await PrivacyManagementService.requestDataDeletion(
      'selective_deletion',
      categories,
      'User requested deletion via privacy dashboard'
    );

    if (deletionId) {
      Alert.alert(
        'Deletion Requested',
        'Your data deletion request has been submitted and will be processed within 30 days.'
      );
      loadPrivacyData();
    } else {
      Alert.alert('Error', 'Failed to request data deletion');
    }
  };

  const downloadExport = async (exportRequest: DataExportRequest) => {
    const success = await PrivacyManagementService.downloadExportedData(exportRequest.id);
    if (!success) {
      Alert.alert('Download Failed', 'Unable to download the export file');
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return '#10B981';
      case 'processing': return '#F59E0B';
      case 'failed': return '#EF4444';
      default: return '#6B7280';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed': return CheckCircle;
      case 'processing': return Clock;
      case 'failed': return AlertTriangle;
      default: return Info;
    }
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#3B82F6" />
        <Text style={styles.loadingText}>Loading privacy settings...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Header */}
      <LinearGradient
        colors={['#3B82F6', '#1D4ED8']}
        style={styles.header}
      >
        <Shield size={32} color="white" />
        <Text style={styles.headerTitle}>Privacy & Data</Text>
        <Text style={styles.headerSubtitle}>
          Control your data and privacy settings
        </Text>
      </LinearGradient>

      {/* Tab Navigation */}
      <View style={styles.tabContainer}>
        {[
          { key: 'settings', label: 'Settings', icon: Settings },
          { key: 'export', label: 'Export', icon: Download },
          { key: 'deletion', label: 'Deletion', icon: Trash2 },
        ].map(({ key, label, icon: Icon }) => (
          <TouchableOpacity
            key={key}
            style={[styles.tab, activeTab === key && styles.activeTab]}
            onPress={() => setActiveTab(key as any)}
          >
            <Icon size={20} color={activeTab === key ? '#3B82F6' : '#6B7280'} />
            <Text style={[styles.tabText, activeTab === key && styles.activeTabText]}>
              {label}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      <ScrollView
        style={styles.content}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {activeTab === 'settings' && (
          <View style={styles.settingsContainer}>
            {/* Data Collection */}
            <View style={styles.section}>
              <View style={styles.sectionHeader}>
                <Database size={20} color="#3B82F6" />
                <Text style={styles.sectionTitle}>Data Collection</Text>
              </View>
              
              <SettingRow
                title="Analytics"
                description="Help improve the app with usage analytics"
                value={privacySettings?.allow_analytics ?? true}
                onValueChange={(value) => updatePrivacySetting('allow_analytics', value)}
              />
              
              <SettingRow
                title="Performance Tracking"
                description="Monitor app performance and crashes"
                value={privacySettings?.allow_performance_tracking ?? true}
                onValueChange={(value) => updatePrivacySetting('allow_performance_tracking', value)}
              />
              
              <SettingRow
                title="Usage Statistics"
                description="Collect anonymous usage patterns"
                value={privacySettings?.allow_usage_statistics ?? true}
                onValueChange={(value) => updatePrivacySetting('allow_usage_statistics', value)}
              />
            </View>

            {/* Location Privacy */}
            <View style={styles.section}>
              <View style={styles.sectionHeader}>
                <MapPin size={20} color="#3B82F6" />
                <Text style={styles.sectionTitle}>Location Privacy</Text>
              </View>
              
              <SettingRow
                title="Share Location"
                description="Include location data with identifications"
                value={privacySettings?.share_location ?? false}
                onValueChange={(value) => updatePrivacySetting('share_location', value)}
              />
              
              <SettingRow
                title="Location History"
                description="Keep a history of your identification locations"
                value={privacySettings?.allow_location_history ?? false}
                onValueChange={(value) => updatePrivacySetting('allow_location_history', value)}
              />
            </View>

            {/* Profile Privacy */}
            <View style={styles.section}>
              <View style={styles.sectionHeader}>
                <Users size={20} color="#3B82F6" />
                <Text style={styles.sectionTitle}>Profile Privacy</Text>
              </View>
              
              <SettingRow
                title="Public Profile"
                description="Make your profile visible to other users"
                value={privacySettings?.public_profile ?? false}
                onValueChange={(value) => updatePrivacySetting('public_profile', value)}
              />
              
              <SettingRow
                title="Show Real Name"
                description="Display your real name on your profile"
                value={privacySettings?.show_real_name ?? false}
                onValueChange={(value) => updatePrivacySetting('show_real_name', value)}
              />
              
              <SettingRow
                title="Show Collection"
                description="Allow others to see your species collection"
                value={privacySettings?.show_collection ?? false}
                onValueChange={(value) => updatePrivacySetting('show_collection', value)}
              />
            </View>

            {/* Communication */}
            <View style={styles.section}>
              <View style={styles.sectionHeader}>
                <Mail size={20} color="#3B82F6" />
                <Text style={styles.sectionTitle}>Communication</Text>
              </View>
              
              <SettingRow
                title="Marketing Emails"
                description="Receive promotional and marketing emails"
                value={privacySettings?.allow_marketing_emails ?? false}
                onValueChange={(value) => updatePrivacySetting('allow_marketing_emails', value)}
              />
              
              <SettingRow
                title="Research Participation"
                description="Allow your data to be used for scientific research"
                value={privacySettings?.allow_research_participation ?? false}
                onValueChange={(value) => updatePrivacySetting('allow_research_participation', value)}
              />
            </View>
          </View>
        )}

        {activeTab === 'export' && (
          <View style={styles.exportContainer}>
            <Text style={styles.sectionDescription}>
              Download your data in various formats. Export files are available for 7 days.
            </Text>

            {/* Export Options */}
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Request New Export</Text>
              
              <TouchableOpacity
                style={styles.actionButton}
                onPress={() => requestDataExport('full_export')}
              >
                <Download size={20} color="#3B82F6" />
                <Text style={styles.actionButtonText}>Full Data Export</Text>
              </TouchableOpacity>
              
              <TouchableOpacity
                style={styles.actionButton}
                onPress={() => requestDataExport('identifications')}
              >
                <FileText size={20} color="#3B82F6" />
                <Text style={styles.actionButtonText}>Identifications Only</Text>
              </TouchableOpacity>
              
              <TouchableOpacity
                style={styles.actionButton}
                onPress={() => requestDataExport('media')}
              >
                <Database size={20} color="#3B82F6" />
                <Text style={styles.actionButtonText}>Media Files</Text>
              </TouchableOpacity>
            </View>

            {/* Export History */}
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Export History</Text>
              
              {exportRequests.length === 0 ? (
                <Text style={styles.emptyText}>No export requests yet</Text>
              ) : (
                exportRequests.map((request) => {
                  const StatusIcon = getStatusIcon(request.status);
                  const statusColor = getStatusColor(request.status);
                  
                  return (
                    <View key={request.id} style={styles.requestItem}>
                      <View style={styles.requestHeader}>
                        <StatusIcon size={16} color={statusColor} />
                        <Text style={styles.requestType}>
                          {request.request_type.replace('_', ' ').toUpperCase()}
                        </Text>
                        <Text style={[styles.requestStatus, { color: statusColor }]}>
                          {request.status.toUpperCase()}
                        </Text>
                      </View>
                      
                      <Text style={styles.requestDate}>
                        Requested: {new Date(request.created_at).toLocaleDateString()}
                      </Text>
                      
                      {request.status === 'completed' && request.file_url && (
                        <TouchableOpacity
                          style={styles.downloadButton}
                          onPress={() => downloadExport(request)}
                        >
                          <Download size={16} color="#10B981" />
                          <Text style={styles.downloadButtonText}>Download</Text>
                        </TouchableOpacity>
                      )}
                    </View>
                  );
                })
              )}
            </View>
          </View>
        )}

        {activeTab === 'deletion' && (
          <View style={styles.deletionContainer}>
            <View style={styles.warningBox}>
              <AlertTriangle size={20} color="#F59E0B" />
              <Text style={styles.warningText}>
                Data deletion is permanent and cannot be undone. Please review carefully.
              </Text>
            </View>

            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Request Data Deletion</Text>
              
              <TouchableOpacity
                style={[styles.actionButton, styles.dangerButton]}
                onPress={requestDataDeletion}
              >
                <Trash2 size={20} color="#EF4444" />
                <Text style={[styles.actionButtonText, styles.dangerText]}>
                  Delete Selected Data
                </Text>
              </TouchableOpacity>
            </View>

            {/* Deletion History */}
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Deletion Requests</Text>
              
              {deletionRequests.length === 0 ? (
                <Text style={styles.emptyText}>No deletion requests</Text>
              ) : (
                deletionRequests.map((request) => {
                  const StatusIcon = getStatusIcon(request.status);
                  const statusColor = getStatusColor(request.status);
                  
                  return (
                    <View key={request.id} style={styles.requestItem}>
                      <View style={styles.requestHeader}>
                        <StatusIcon size={16} color={statusColor} />
                        <Text style={styles.requestType}>
                          {request.deletion_type.replace('_', ' ').toUpperCase()}
                        </Text>
                        <Text style={[styles.requestStatus, { color: statusColor }]}>
                          {request.status.toUpperCase()}
                        </Text>
                      </View>
                      
                      <Text style={styles.requestDate}>
                        Requested: {new Date(request.created_at).toLocaleDateString()}
                      </Text>
                      
                      <Text style={styles.requestCategories}>
                        Categories: {request.data_categories.join(', ')}
                      </Text>
                    </View>
                  );
                })
              )}
            </View>
          </View>
        )}
      </ScrollView>
    </View>
  );
};

interface SettingRowProps {
  title: string;
  description: string;
  value: boolean;
  onValueChange: (value: boolean) => void;
}

const SettingRow: React.FC<SettingRowProps> = ({
  title,
  description,
  value,
  onValueChange,
}) => (
  <View style={styles.settingRow}>
    <View style={styles.settingContent}>
      <Text style={styles.settingTitle}>{title}</Text>
      <Text style={styles.settingDescription}>{description}</Text>
    </View>
    <Switch
      value={value}
      onValueChange={onValueChange}
      trackColor={{ false: '#E5E7EB', true: '#3B82F6' }}
      thumbColor={value ? '#FFFFFF' : '#F3F4F6'}
    />
  </View>
);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#F9FAFB',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#6B7280',
  },
  header: {
    padding: 24,
    paddingTop: 60,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: 'white',
    marginTop: 8,
  },
  headerSubtitle: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.8)',
    marginTop: 4,
    textAlign: 'center',
  },
  tabContainer: {
    flexDirection: 'row',
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  tab: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    gap: 8,
  },
  activeTab: {
    borderBottomWidth: 2,
    borderBottomColor: '#3B82F6',
  },
  tabText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#6B7280',
  },
  activeTabText: {
    color: '#3B82F6',
  },
  content: {
    flex: 1,
  },
  settingsContainer: {
    padding: 20,
  },
  section: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
    gap: 8,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827',
  },
  sectionDescription: {
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 16,
    lineHeight: 20,
  },
  settingRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
  },
  settingContent: {
    flex: 1,
    marginRight: 16,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#111827',
    marginBottom: 2,
  },
  settingDescription: {
    fontSize: 14,
    color: '#6B7280',
    lineHeight: 18,
  },
  exportContainer: {
    padding: 20,
  },
  deletionContainer: {
    padding: 20,
  },
  warningBox: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FEF3C7',
    padding: 16,
    borderRadius: 8,
    marginBottom: 20,
    gap: 12,
  },
  warningText: {
    flex: 1,
    fontSize: 14,
    color: '#92400E',
    lineHeight: 18,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F3F4F6',
    padding: 16,
    borderRadius: 8,
    marginBottom: 12,
    gap: 12,
  },
  actionButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#374151',
  },
  dangerButton: {
    backgroundColor: '#FEF2F2',
  },
  dangerText: {
    color: '#EF4444',
  },
  requestItem: {
    backgroundColor: '#F9FAFB',
    padding: 16,
    borderRadius: 8,
    marginBottom: 12,
  },
  requestHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
    gap: 8,
  },
  requestType: {
    flex: 1,
    fontSize: 14,
    fontWeight: '500',
    color: '#111827',
  },
  requestStatus: {
    fontSize: 12,
    fontWeight: '600',
  },
  requestDate: {
    fontSize: 12,
    color: '#6B7280',
    marginBottom: 4,
  },
  requestCategories: {
    fontSize: 12,
    color: '#6B7280',
  },
  downloadButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F0FDF4',
    padding: 8,
    borderRadius: 6,
    marginTop: 8,
    gap: 6,
    alignSelf: 'flex-start',
  },
  downloadButtonText: {
    fontSize: 12,
    fontWeight: '500',
    color: '#10B981',
  },
  emptyText: {
    fontSize: 14,
    color: '#6B7280',
    textAlign: 'center',
    fontStyle: 'italic',
    paddingVertical: 20,
  },
});

export default PrivacyDashboard;
