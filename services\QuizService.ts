import { supabase } from '../lib/supabase';

export interface QuizQuestion {
  id: string;
  species_id: string;
  question_text: string;
  question_type: 'multiple_choice' | 'true_false' | 'identification';
  options: string[] | null;
  correct_answer: string;
  difficulty_level: number;
  explanation: string | null;
  created_at: string;
}

export interface QuizAttempt {
  id: string;
  user_id: string;
  question_id: string;
  user_answer: string;
  is_correct: boolean;
  time_taken: number;
  created_at: string;
}

export interface Quiz {
  id: string;
  title: string;
  description: string;
  questions: QuizQuestion[];
  difficulty_level: number;
  estimated_time: number;
}

export interface QuizResult {
  quiz_id: string;
  total_questions: number;
  correct_answers: number;
  score_percentage: number;
  time_taken: number;
  attempts: QuizAttempt[];
}

export interface UserQuizStats {
  total_quizzes_taken: number;
  average_score: number;
  total_time_spent: number;
  favorite_categories: string[];
  improvement_areas: string[];
}

export class QuizService {
  /**
   * Generate a personalized quiz based on user's identification history
   */
  async generatePersonalizedQuiz(
    difficulty: number = 2,
    questionCount: number = 10
  ): Promise<Quiz> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        throw new Error('User not authenticated');
      }

      // Get user's recent identifications to create relevant questions
      const { data: userIdentifications } = await supabase
        .from('species_identifications')
        .select(`
          species_id,
          species:species_id (
            id,
            common_name,
            scientific_name,
            category_id,
            description,
            habitat,
            conservation_status,
            physical_traits,
            interesting_facts
          )
        `)
        .eq('user_id', user.id)
        .eq('status', 'completed')
        .not('species_id', 'is', null)
        .order('created_at', { ascending: false })
        .limit(20);

      if (!userIdentifications || userIdentifications.length === 0) {
        // If no personal history, generate a general quiz
        return await this.generateGeneralQuiz(difficulty, questionCount);
      }

      // Extract unique species
      const uniqueSpecies = userIdentifications
        .filter((id, index, self) => 
          index === self.findIndex(s => s.species_id === id.species_id)
        )
        .map(id => id.species)
        .filter(species => species !== null);

      // Generate questions based on user's species
      const questions = await this.generateQuestionsFromSpecies(
        uniqueSpecies,
        difficulty,
        questionCount
      );

      return {
        id: `personalized_${Date.now()}`,
        title: 'Your Nature Knowledge Quiz',
        description: 'Test your knowledge on species you\'ve discovered!',
        questions,
        difficulty_level: difficulty,
        estimated_time: questionCount * 30, // 30 seconds per question
      };
    } catch (error) {
      console.error('Error generating personalized quiz:', error);
      throw error;
    }
  }

  /**
   * Generate a general quiz from the species database
   */
  async generateGeneralQuiz(
    difficulty: number = 2,
    questionCount: number = 10
  ): Promise<Quiz> {
    try {
      // Get random species from database
      const { data: species } = await supabase
        .from('species')
        .select(`
          id,
          common_name,
          scientific_name,
          category_id,
          description,
          habitat,
          conservation_status,
          physical_traits,
          interesting_facts,
          category:category_id (name)
        `)
        .limit(questionCount * 2); // Get more than needed for variety

      if (!species || species.length === 0) {
        throw new Error('No species data available for quiz generation');
      }

      // Shuffle and select species
      const shuffledSpecies = species.sort(() => Math.random() - 0.5);
      const selectedSpecies = shuffledSpecies.slice(0, questionCount);

      // Generate questions
      const questions = await this.generateQuestionsFromSpecies(
        selectedSpecies,
        difficulty,
        questionCount
      );

      return {
        id: `general_${Date.now()}`,
        title: 'Nature Knowledge Quiz',
        description: 'Test your knowledge of various species!',
        questions,
        difficulty_level: difficulty,
        estimated_time: questionCount * 30,
      };
    } catch (error) {
      console.error('Error generating general quiz:', error);
      throw error;
    }
  }

  /**
   * Generate quiz questions from species data
   */
  private async generateQuestionsFromSpecies(
    species: any[],
    difficulty: number,
    questionCount: number
  ): Promise<QuizQuestion[]> {
    const questions: QuizQuestion[] = [];
    const questionTypes = ['multiple_choice', 'true_false', 'identification'];

    for (let i = 0; i < Math.min(questionCount, species.length); i++) {
      const currentSpecies = species[i];
      const questionType = questionTypes[Math.floor(Math.random() * questionTypes.length)] as any;

      let question: QuizQuestion;

      switch (questionType) {
        case 'multiple_choice':
          question = await this.generateMultipleChoiceQuestion(currentSpecies, species, difficulty);
          break;
        case 'true_false':
          question = this.generateTrueFalseQuestion(currentSpecies, difficulty);
          break;
        case 'identification':
          question = await this.generateIdentificationQuestion(currentSpecies, species, difficulty);
          break;
        default:
          question = await this.generateMultipleChoiceQuestion(currentSpecies, species, difficulty);
      }

      questions.push(question);
    }

    return questions;
  }

  /**
   * Generate multiple choice question
   */
  private async generateMultipleChoiceQuestion(
    species: any,
    allSpecies: any[],
    difficulty: number
  ): Promise<QuizQuestion> {
    const questionTemplates = [
      {
        text: `What is the scientific name of ${species.common_name}?`,
        correct: species.scientific_name,
        getWrong: () => allSpecies
          .filter(s => s.id !== species.id)
          .map(s => s.scientific_name)
          .sort(() => Math.random() - 0.5)
          .slice(0, 3)
      },
      {
        text: `Which habitat is ${species.common_name} typically found in?`,
        correct: species.habitat,
        getWrong: () => [
          'Dense tropical rainforests',
          'Arctic tundra regions',
          'Deep ocean trenches',
          'Desert environments'
        ].filter(h => h !== species.habitat).slice(0, 3)
      },
      {
        text: `What is the conservation status of ${species.common_name}?`,
        correct: species.conservation_status,
        getWrong: () => [
          'Critically Endangered',
          'Vulnerable',
          'Near Threatened',
          'Least Concern',
          'Extinct in Wild'
        ].filter(s => s !== species.conservation_status).slice(0, 3)
      }
    ];

    const template = questionTemplates[Math.floor(Math.random() * questionTemplates.length)];
    const wrongAnswers = template.getWrong();
    const options = [template.correct, ...wrongAnswers].sort(() => Math.random() - 0.5);

    return {
      id: `mc_${species.id}_${Date.now()}`,
      species_id: species.id,
      question_text: template.text,
      question_type: 'multiple_choice',
      options,
      correct_answer: template.correct,
      difficulty_level: difficulty,
      explanation: `${species.common_name} (${species.scientific_name}) is ${template.correct}`,
      created_at: new Date().toISOString(),
    };
  }

  /**
   * Generate true/false question
   */
  private generateTrueFalseQuestion(species: any, difficulty: number): QuizQuestion {
    const facts = species.interesting_facts || [];
    const isTrue = Math.random() > 0.5;

    let questionText: string;
    let correctAnswer: string;

    if (isTrue && facts.length > 0) {
      // Use a real fact
      const fact = facts[Math.floor(Math.random() * facts.length)];
      questionText = `True or False: ${fact}`;
      correctAnswer = 'True';
    } else {
      // Create a false statement
      const falseStatements = [
        `${species.common_name} is extinct in the wild`,
        `${species.common_name} can only be found in Antarctica`,
        `${species.common_name} is the largest species in its category`,
        `${species.common_name} was discovered last year`,
      ];
      questionText = falseStatements[Math.floor(Math.random() * falseStatements.length)];
      correctAnswer = 'False';
    }

    return {
      id: `tf_${species.id}_${Date.now()}`,
      species_id: species.id,
      question_text: questionText,
      question_type: 'true_false',
      options: ['True', 'False'],
      correct_answer: correctAnswer,
      difficulty_level: difficulty,
      explanation: `This statement about ${species.common_name} is ${correctAnswer.toLowerCase()}.`,
      created_at: new Date().toISOString(),
    };
  }

  /**
   * Generate identification question
   */
  private async generateIdentificationQuestion(
    species: any,
    allSpecies: any[],
    difficulty: number
  ): Promise<QuizQuestion> {
    const description = species.description || '';
    const habitat = species.habitat || '';
    
    // Create a description-based identification question
    const questionText = `Based on this description: "${description.substring(0, 150)}...", which species is this?`;
    
    const wrongAnswers = allSpecies
      .filter(s => s.id !== species.id && s.category_id === species.category_id)
      .map(s => s.common_name)
      .sort(() => Math.random() - 0.5)
      .slice(0, 3);

    const options = [species.common_name, ...wrongAnswers].sort(() => Math.random() - 0.5);

    return {
      id: `id_${species.id}_${Date.now()}`,
      species_id: species.id,
      question_text: questionText,
      question_type: 'identification',
      options,
      correct_answer: species.common_name,
      difficulty_level: difficulty,
      explanation: `This description matches ${species.common_name} (${species.scientific_name}).`,
      created_at: new Date().toISOString(),
    };
  }

  /**
   * Submit quiz answer
   */
  async submitAnswer(
    questionId: string,
    userAnswer: string,
    timeTaken: number
  ): Promise<{ isCorrect: boolean; explanation: string }> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        throw new Error('User not authenticated');
      }

      // For generated quizzes, we need to validate against the question data
      // In a real implementation, you might store quiz questions temporarily
      // or validate against the original species data

      // This is a simplified implementation
      const isCorrect = true; // You would implement actual validation here
      const explanation = 'Great job!'; // You would provide actual explanation

      // Store the attempt
      const { error } = await supabase
        .from('quiz_attempts')
        .insert({
          user_id: user.id,
          question_id: questionId,
          user_answer: userAnswer,
          is_correct: isCorrect,
          time_taken: timeTaken,
        });

      if (error) {
        throw error;
      }

      return { isCorrect, explanation };
    } catch (error) {
      console.error('Error submitting answer:', error);
      throw error;
    }
  }

  /**
   * Get user quiz statistics
   */
  async getUserQuizStats(): Promise<UserQuizStats> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        throw new Error('User not authenticated');
      }

      const { data: attempts } = await supabase
        .from('quiz_attempts')
        .select('*')
        .eq('user_id', user.id);

      if (!attempts || attempts.length === 0) {
        return {
          total_quizzes_taken: 0,
          average_score: 0,
          total_time_spent: 0,
          favorite_categories: [],
          improvement_areas: [],
        };
      }

      const totalQuestions = attempts.length;
      const correctAnswers = attempts.filter(a => a.is_correct).length;
      const averageScore = (correctAnswers / totalQuestions) * 100;
      const totalTimeSpent = attempts.reduce((sum, a) => sum + a.time_taken, 0);

      return {
        total_quizzes_taken: totalQuestions,
        average_score: Math.round(averageScore),
        total_time_spent: totalTimeSpent,
        favorite_categories: [], // Would be calculated from species categories
        improvement_areas: [], // Would be calculated from wrong answers
      };
    } catch (error) {
      console.error('Error getting quiz stats:', error);
      throw error;
    }
  }

  /**
   * Get quiz leaderboard
   */
  async getLeaderboard(limit: number = 10): Promise<any[]> {
    try {
      // This would require a more complex query to calculate user scores
      // For now, return empty array
      return [];
    } catch (error) {
      console.error('Error getting leaderboard:', error);
      throw error;
    }
  }
}
