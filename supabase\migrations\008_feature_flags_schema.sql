-- Feature Flags and A/B Testing Schema
-- This migration creates comprehensive feature flag management and A/B testing capabilities

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Feature Flags Table
CREATE TABLE IF NOT EXISTS feature_flags (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    flag_type VARCHAR(20) NOT NULL DEFAULT 'boolean' CHECK (flag_type IN ('boolean', 'string', 'number', 'json')),
    
    -- Flag configuration
    is_enabled BOOLEAN NOT NULL DEFAULT false,
    default_value JSONB NOT NULL DEFAULT 'false',
    
    -- Targeting and rollout
    rollout_percentage DECIMAL(5,2) NOT NULL DEFAULT 0.00 CHECK (rollout_percentage >= 0 AND rollout_percentage <= 100),
    target_audience JSONB DEFAULT '{}', -- Targeting criteria
    
    -- Environment and versioning
    environment VARCHAR(20) NOT NULL DEFAULT 'production' CHECK (environment IN ('development', 'staging', 'production')),
    version VARCHAR(20) DEFAULT '1.0.0',
    
    -- A/B Testing
    is_ab_test BOOLEAN NOT NULL DEFAULT false,
    ab_test_config JSONB DEFAULT '{}', -- A/B test configuration
    
    -- Scheduling
    start_date TIMESTAMP WITH TIME ZONE,
    end_date TIMESTAMP WITH TIME ZONE,
    
    -- Metadata
    created_by UUID REFERENCES auth.users(id),
    tags TEXT[] DEFAULT '{}',
    category VARCHAR(50) DEFAULT 'general',
    
    -- Status tracking
    is_archived BOOLEAN NOT NULL DEFAULT false,
    last_evaluated_at TIMESTAMP WITH TIME ZONE,
    evaluation_count BIGINT DEFAULT 0,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Feature Flag Variants Table (for A/B testing)
CREATE TABLE IF NOT EXISTS feature_flag_variants (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    flag_id UUID NOT NULL REFERENCES feature_flags(id) ON DELETE CASCADE,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    value JSONB NOT NULL,
    weight DECIMAL(5,2) NOT NULL DEFAULT 50.00 CHECK (weight >= 0 AND weight <= 100),
    is_control BOOLEAN NOT NULL DEFAULT false,
    is_active BOOLEAN NOT NULL DEFAULT true,
    
    -- Metrics
    assignment_count BIGINT DEFAULT 0,
    conversion_count BIGINT DEFAULT 0,
    conversion_rate DECIMAL(5,2) DEFAULT 0.00,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(flag_id, name)
);

-- User Feature Flag Assignments Table
CREATE TABLE IF NOT EXISTS user_feature_assignments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    flag_id UUID NOT NULL REFERENCES feature_flags(id) ON DELETE CASCADE,
    variant_id UUID REFERENCES feature_flag_variants(id) ON DELETE SET NULL,
    
    -- Assignment details
    assigned_value JSONB NOT NULL,
    assignment_reason VARCHAR(50) NOT NULL CHECK (assignment_reason IN ('rollout', 'targeting', 'override', 'ab_test')),
    
    -- Context
    user_segment VARCHAR(50),
    device_type VARCHAR(20),
    app_version VARCHAR(20),
    location_country VARCHAR(3),
    
    -- Tracking
    first_assigned_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_evaluated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    evaluation_count INTEGER DEFAULT 1,
    
    -- Conversion tracking
    has_converted BOOLEAN DEFAULT false,
    converted_at TIMESTAMP WITH TIME ZONE,
    conversion_value DECIMAL(10,2),
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(user_id, flag_id)
);

-- Feature Flag Evaluations Log Table
CREATE TABLE IF NOT EXISTS feature_flag_evaluations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    flag_id UUID NOT NULL REFERENCES feature_flags(id) ON DELETE CASCADE,
    user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    
    -- Evaluation details
    evaluated_value JSONB NOT NULL,
    evaluation_reason VARCHAR(50) NOT NULL,
    variant_name VARCHAR(100),
    
    -- Context
    user_context JSONB DEFAULT '{}',
    device_context JSONB DEFAULT '{}',
    app_context JSONB DEFAULT '{}',
    
    -- Performance
    evaluation_time_ms INTEGER,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- A/B Test Results Table
CREATE TABLE IF NOT EXISTS ab_test_results (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    flag_id UUID NOT NULL REFERENCES feature_flags(id) ON DELETE CASCADE,
    variant_id UUID NOT NULL REFERENCES feature_flag_variants(id) ON DELETE CASCADE,
    
    -- Time period
    date DATE NOT NULL,
    
    -- Metrics
    unique_users INTEGER DEFAULT 0,
    total_evaluations INTEGER DEFAULT 0,
    conversions INTEGER DEFAULT 0,
    conversion_rate DECIMAL(5,2) DEFAULT 0.00,
    
    -- Statistical significance
    confidence_level DECIMAL(5,2) DEFAULT 0.00,
    p_value DECIMAL(10,8),
    is_statistically_significant BOOLEAN DEFAULT false,
    
    -- Revenue metrics (if applicable)
    total_revenue DECIMAL(12,2) DEFAULT 0.00,
    average_revenue_per_user DECIMAL(10,2) DEFAULT 0.00,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(flag_id, variant_id, date)
);

-- Feature Flag Dependencies Table
CREATE TABLE IF NOT EXISTS feature_flag_dependencies (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    flag_id UUID NOT NULL REFERENCES feature_flags(id) ON DELETE CASCADE,
    depends_on_flag_id UUID NOT NULL REFERENCES feature_flags(id) ON DELETE CASCADE,
    dependency_type VARCHAR(20) NOT NULL DEFAULT 'requires' CHECK (dependency_type IN ('requires', 'conflicts', 'implies')),
    required_value JSONB,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(flag_id, depends_on_flag_id),
    CHECK(flag_id != depends_on_flag_id)
);

-- Feature Flag Audit Log Table
CREATE TABLE IF NOT EXISTS feature_flag_audit_log (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    flag_id UUID NOT NULL REFERENCES feature_flags(id) ON DELETE CASCADE,
    user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    
    -- Change details
    action VARCHAR(20) NOT NULL CHECK (action IN ('created', 'updated', 'deleted', 'enabled', 'disabled', 'archived')),
    old_values JSONB,
    new_values JSONB,
    change_reason TEXT,
    
    -- Context
    ip_address INET,
    user_agent TEXT,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_feature_flags_name ON feature_flags(name);
CREATE INDEX IF NOT EXISTS idx_feature_flags_enabled ON feature_flags(is_enabled);
CREATE INDEX IF NOT EXISTS idx_feature_flags_environment ON feature_flags(environment);
CREATE INDEX IF NOT EXISTS idx_feature_flags_category ON feature_flags(category);
CREATE INDEX IF NOT EXISTS idx_feature_flags_ab_test ON feature_flags(is_ab_test);
CREATE INDEX IF NOT EXISTS idx_feature_flags_dates ON feature_flags(start_date, end_date);

CREATE INDEX IF NOT EXISTS idx_flag_variants_flag_id ON feature_flag_variants(flag_id);
CREATE INDEX IF NOT EXISTS idx_flag_variants_active ON feature_flag_variants(is_active);

CREATE INDEX IF NOT EXISTS idx_user_assignments_user_id ON user_feature_assignments(user_id);
CREATE INDEX IF NOT EXISTS idx_user_assignments_flag_id ON user_feature_assignments(flag_id);
CREATE INDEX IF NOT EXISTS idx_user_assignments_variant_id ON user_feature_assignments(variant_id);
CREATE INDEX IF NOT EXISTS idx_user_assignments_converted ON user_feature_assignments(has_converted);

CREATE INDEX IF NOT EXISTS idx_flag_evaluations_flag_id ON feature_flag_evaluations(flag_id);
CREATE INDEX IF NOT EXISTS idx_flag_evaluations_user_id ON feature_flag_evaluations(user_id);
CREATE INDEX IF NOT EXISTS idx_flag_evaluations_created_at ON feature_flag_evaluations(created_at);

CREATE INDEX IF NOT EXISTS idx_ab_test_results_flag_id ON ab_test_results(flag_id);
CREATE INDEX IF NOT EXISTS idx_ab_test_results_variant_id ON ab_test_results(variant_id);
CREATE INDEX IF NOT EXISTS idx_ab_test_results_date ON ab_test_results(date);

CREATE INDEX IF NOT EXISTS idx_flag_dependencies_flag_id ON feature_flag_dependencies(flag_id);
CREATE INDEX IF NOT EXISTS idx_flag_dependencies_depends_on ON feature_flag_dependencies(depends_on_flag_id);

CREATE INDEX IF NOT EXISTS idx_flag_audit_log_flag_id ON feature_flag_audit_log(flag_id);
CREATE INDEX IF NOT EXISTS idx_flag_audit_log_user_id ON feature_flag_audit_log(user_id);
CREATE INDEX IF NOT EXISTS idx_flag_audit_log_created_at ON feature_flag_audit_log(created_at);

-- Create updated_at triggers
CREATE TRIGGER update_feature_flags_updated_at BEFORE UPDATE ON feature_flags FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_feature_flag_variants_updated_at BEFORE UPDATE ON feature_flag_variants FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_user_feature_assignments_updated_at BEFORE UPDATE ON user_feature_assignments FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert default feature flags
INSERT INTO feature_flags (name, description, flag_type, default_value, category, environment) VALUES
('new_identification_ui', 'New species identification interface', 'boolean', 'false', 'ui', 'production'),
('premium_features_v2', 'Enhanced premium features', 'boolean', 'false', 'premium', 'production'),
('social_sharing', 'Social media sharing capabilities', 'boolean', 'false', 'social', 'production'),
('advanced_ar_mode', 'Advanced AR identification mode', 'boolean', 'false', 'ar', 'production'),
('community_challenges', 'Community identification challenges', 'boolean', 'false', 'community', 'production'),
('offline_mode', 'Offline species identification', 'boolean', 'false', 'core', 'production'),
('ai_model_v3', 'Third generation AI identification model', 'boolean', 'false', 'ai', 'production'),
('gamification_system', 'Points and achievements system', 'boolean', 'false', 'gamification', 'production')
ON CONFLICT (name) DO NOTHING;

-- Insert default variants for A/B tests
INSERT INTO feature_flag_variants (flag_id, name, description, value, weight, is_control) 
SELECT 
    ff.id,
    'control',
    'Control group - original experience',
    'false'::jsonb,
    50.00,
    true
FROM feature_flags ff 
WHERE ff.name IN ('new_identification_ui', 'premium_features_v2')
ON CONFLICT (flag_id, name) DO NOTHING;

INSERT INTO feature_flag_variants (flag_id, name, description, value, weight, is_control) 
SELECT 
    ff.id,
    'variant_a',
    'Test variant - new experience',
    'true'::jsonb,
    50.00,
    false
FROM feature_flags ff
WHERE ff.name IN ('new_identification_ui', 'premium_features_v2')
ON CONFLICT (flag_id, name) DO NOTHING;

-- Enable RLS on all feature flag tables
ALTER TABLE feature_flags ENABLE ROW LEVEL SECURITY;
ALTER TABLE feature_flag_variants ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_feature_assignments ENABLE ROW LEVEL SECURITY;
ALTER TABLE feature_flag_evaluations ENABLE ROW LEVEL SECURITY;
ALTER TABLE ab_test_results ENABLE ROW LEVEL SECURITY;
ALTER TABLE feature_flag_dependencies ENABLE ROW LEVEL SECURITY;
ALTER TABLE feature_flag_audit_log ENABLE ROW LEVEL SECURITY;

-- Feature Flags Policies
CREATE POLICY "Public can view active feature flags" ON feature_flags
    FOR SELECT USING (is_enabled = true AND NOT is_archived);

CREATE POLICY "Service role can manage feature flags" ON feature_flags
    FOR ALL USING (auth.role() = 'service_role');

CREATE POLICY "Admins can manage feature flags" ON feature_flags
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM user_profiles
            WHERE id = auth.uid()
            AND role IN ('admin', 'super_admin')
        )
    );

-- Feature Flag Variants Policies
CREATE POLICY "Public can view active variants" ON feature_flag_variants
    FOR SELECT USING (
        is_active = true
        AND EXISTS (
            SELECT 1 FROM feature_flags ff
            WHERE ff.id = flag_id
            AND ff.is_enabled = true
            AND NOT ff.is_archived
        )
    );

CREATE POLICY "Service role can manage variants" ON feature_flag_variants
    FOR ALL USING (auth.role() = 'service_role');

CREATE POLICY "Admins can manage variants" ON feature_flag_variants
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM user_profiles
            WHERE id = auth.uid()
            AND role IN ('admin', 'super_admin')
        )
    );

-- User Feature Assignments Policies
CREATE POLICY "Users can view own assignments" ON user_feature_assignments
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Service role can manage assignments" ON user_feature_assignments
    FOR ALL USING (auth.role() = 'service_role');

CREATE POLICY "Admins can view assignments" ON user_feature_assignments
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM user_profiles
            WHERE id = auth.uid()
            AND role IN ('admin', 'super_admin')
        )
    );
