import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import Stripe from 'https://esm.sh/stripe@12.0.0?target=deno'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

const stripe = new Stripe(Deno.env.get('STRIPE_SECRET_KEY') || '', {
  apiVersion: '2023-10-16',
})

const supabaseClient = createClient(
  Deno.env.get('SUPABASE_URL') ?? '',
  Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
)

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const authHeader = req.headers.get('Authorization')!
    const token = authHeader.replace('Bearer ', '')

    const { data: { user }, error: authError } = await supabaseClient.auth.getUser(token)
    if (authError || !user) {
      throw new Error('Unauthorized')
    }

    const { userId, newPlanId, billingPeriod, prorationBehavior = 'create_prorations' } = await req.json()

    // Get current subscription
    const { data: currentSub, error: subError } = await supabaseClient
      .from('user_subscriptions')
      .select(`
        *,
        plan:subscription_plans(*)
      `)
      .eq('user_id', userId)
      .in('status', ['active', 'trialing'])
      .single()

    if (subError || !currentSub) {
      throw new Error('No active subscription found')
    }

    // Get new plan details
    const { data: newPlan, error: planError } = await supabaseClient
      .from('subscription_plans')
      .select('*')
      .eq('id', newPlanId)
      .single()

    if (planError || !newPlan) {
      throw new Error('New plan not found')
    }

    // Determine new price ID
    const newPriceId = billingPeriod === 'yearly' 
      ? newPlan.stripe_price_id_yearly 
      : newPlan.stripe_price_id_monthly

    if (!newPriceId) {
      throw new Error('Price ID not configured for the selected plan and billing period')
    }

    // Get current Stripe subscription
    const stripeSubscription = await stripe.subscriptions.retrieve(currentSub.stripe_subscription_id)

    // Update subscription in Stripe
    const updatedSubscription = await stripe.subscriptions.update(currentSub.stripe_subscription_id, {
      items: [{
        id: stripeSubscription.items.data[0].id,
        price: newPriceId,
      }],
      proration_behavior: prorationBehavior,
      metadata: {
        user_id: userId,
        plan_id: newPlanId,
        billing_period: billingPeriod,
        changed_at: new Date().toISOString()
      }
    })

    // Update subscription in database
    const { error: updateError } = await supabaseClient
      .from('user_subscriptions')
      .update({
        plan_id: newPlanId,
        status: updatedSubscription.status,
        current_period_start: new Date(updatedSubscription.current_period_start * 1000).toISOString(),
        current_period_end: new Date(updatedSubscription.current_period_end * 1000).toISOString(),
        updated_at: new Date().toISOString()
      })
      .eq('id', currentSub.id)

    if (updateError) {
      console.error('Error updating subscription in database:', updateError)
      throw new Error('Failed to update subscription record')
    }

    // Send notification
    await supabaseClient
      .from('notifications')
      .insert({
        user_id: userId,
        type: 'subscription',
        title: 'Subscription Plan Changed',
        message: `Your subscription has been changed to ${newPlan.name}`,
        data: {
          old_plan: currentSub.plan?.name,
          new_plan: newPlan.name,
          billing_period: billingPeriod,
          effective_date: new Date().toISOString()
        }
      })

    // Log the change for analytics
    await supabaseClient
      .from('fraud_detection_logs')
      .insert({
        user_id: userId,
        risk_factors: {
          action: 'plan_change',
          old_plan_id: currentSub.plan_id,
          new_plan_id: newPlanId,
          billing_period: billingPeriod,
          proration_behavior: prorationBehavior
        },
        risk_score: 0,
        action_taken: 'allow',
        notes: `Plan changed from ${currentSub.plan?.name} to ${newPlan.name}`
      })

    return new Response(
      JSON.stringify({
        success: true,
        subscription: {
          id: updatedSubscription.id,
          status: updatedSubscription.status,
          current_period_end: updatedSubscription.current_period_end,
          plan_name: newPlan.name
        }
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      }
    )

  } catch (error: any) {
    console.error('Error changing subscription plan:', error)
    return new Response(
      JSON.stringify({ error: error.message }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400,
      }
    )
  }
})

// Helper function to calculate proration
async function calculateProration(
  currentSubscription: any,
  newPlan: any,
  billingPeriod: 'monthly' | 'yearly'
): Promise<number> {
  try {
    const currentPeriodStart = new Date(currentSubscription.current_period_start)
    const currentPeriodEnd = new Date(currentSubscription.current_period_end)
    const now = new Date()

    // Calculate remaining time in current period
    const totalPeriodTime = currentPeriodEnd.getTime() - currentPeriodStart.getTime()
    const remainingTime = currentPeriodEnd.getTime() - now.getTime()
    const remainingRatio = remainingTime / totalPeriodTime

    // Get current plan price
    const currentPlan = currentSubscription.plan
    const currentPrice = billingPeriod === 'yearly' 
      ? currentPlan.price_yearly 
      : currentPlan.price_monthly

    const newPrice = billingPeriod === 'yearly' 
      ? newPlan.price_yearly 
      : newPlan.price_monthly

    // Calculate proration amount
    const unusedAmount = currentPrice * remainingRatio
    const newPeriodAmount = newPrice * remainingRatio
    const prorationAmount = newPeriodAmount - unusedAmount

    return Math.round(prorationAmount * 100) / 100 // Round to 2 decimal places
  } catch (error) {
    console.error('Error calculating proration:', error)
    return 0
  }
}
