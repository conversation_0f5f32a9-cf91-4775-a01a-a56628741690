import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';

export type SubscriptionTier = 'free' | 'pro' | 'expert';

interface SubscriptionFeatures {
  dailyScans: number | 'unlimited';
  arFeatures: 'basic' | 'full' | 'advanced';
  collectionTools: 'limited' | 'full' | 'analytics';
  communityFeatures: 'read' | 'participate' | 'moderate';
  offlineAccess: string;
  ads: 'banner' | 'reduced' | 'none';
  prioritySupport: boolean;
  advancedFilters: boolean;
  exportData: boolean;
  customCategories: boolean;
}

interface SubscriptionData {
  tier: SubscriptionTier;
  expiryDate?: Date;
  isActive: boolean;
  features: SubscriptionFeatures;
  scanCount: number;
  lastResetDate: Date;
}

interface SubscriptionContextType {
  subscription: SubscriptionData;
  upgradeTier: (tier: SubscriptionTier) => Promise<void>;
  canPerformAction: (action: keyof SubscriptionFeatures) => boolean;
  incrementScanCount: () => Promise<boolean>;
  resetDailyScanCount: () => Promise<void>;
  isLoading: boolean;
}

const getFeaturesByTier = (tier: SubscriptionTier): SubscriptionFeatures => {
  switch (tier) {
    case 'free':
      return {
        dailyScans: 10,
        arFeatures: 'basic',
        collectionTools: 'limited',
        communityFeatures: 'read',
        offlineAccess: '24 hours',
        ads: 'banner',
        prioritySupport: false,
        advancedFilters: false,
        exportData: false,
        customCategories: false,
      };
    case 'pro':
      return {
        dailyScans: 'unlimited',
        arFeatures: 'full',
        collectionTools: 'full',
        communityFeatures: 'participate',
        offlineAccess: '7 days',
        ads: 'reduced',
        prioritySupport: true,
        advancedFilters: true,
        exportData: true,
        customCategories: false,
      };
    case 'expert':
      return {
        dailyScans: 'unlimited',
        arFeatures: 'advanced',
        collectionTools: 'analytics',
        communityFeatures: 'moderate',
        offlineAccess: '30 days',
        ads: 'none',
        prioritySupport: true,
        advancedFilters: true,
        exportData: true,
        customCategories: true,
      };
  }
};

const defaultSubscription: SubscriptionData = {
  tier: 'free',
  isActive: true,
  features: getFeaturesByTier('free'),
  scanCount: 0,
  lastResetDate: new Date(),
};

const SubscriptionContext = createContext<SubscriptionContextType | undefined>(undefined);

export const useSubscription = () => {
  const context = useContext(SubscriptionContext);
  if (!context) {
    throw new Error('useSubscription must be used within a SubscriptionProvider');
  }
  return context;
};

interface SubscriptionProviderProps {
  children: ReactNode;
}

export const SubscriptionProvider: React.FC<SubscriptionProviderProps> = ({ children }) => {
  const [subscription, setSubscription] = useState<SubscriptionData>(defaultSubscription);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    loadSubscription();
    checkDailyReset();
  }, []);

  const loadSubscription = async () => {
    try {
      const savedSubscription = await AsyncStorage.getItem('userSubscription');
      if (savedSubscription) {
        const parsed = JSON.parse(savedSubscription);
        // Convert date strings back to Date objects
        if (parsed.expiryDate) {
          parsed.expiryDate = new Date(parsed.expiryDate);
        }
        parsed.lastResetDate = new Date(parsed.lastResetDate);
        
        // Check if subscription is still active
        const isActive = !parsed.expiryDate || parsed.expiryDate > new Date();
        
        setSubscription({
          ...parsed,
          isActive,
          features: getFeaturesByTier(isActive ? parsed.tier : 'free'),
        });
      }
    } catch (error) {
      console.error('Failed to load subscription:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const saveSubscription = async (newSubscription: SubscriptionData) => {
    try {
      await AsyncStorage.setItem('userSubscription', JSON.stringify(newSubscription));
      setSubscription(newSubscription);
    } catch (error) {
      console.error('Failed to save subscription:', error);
    }
  };

  const checkDailyReset = async () => {
    const now = new Date();
    const lastReset = subscription.lastResetDate;
    
    // Check if it's a new day
    if (now.getDate() !== lastReset.getDate() || 
        now.getMonth() !== lastReset.getMonth() || 
        now.getFullYear() !== lastReset.getFullYear()) {
      await resetDailyScanCount();
    }
  };

  const upgradeTier = async (tier: SubscriptionTier) => {
    const expiryDate = tier !== 'free' ? new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) : undefined; // 30 days from now
    
    const newSubscription: SubscriptionData = {
      ...subscription,
      tier,
      expiryDate,
      isActive: true,
      features: getFeaturesByTier(tier),
    };
    
    await saveSubscription(newSubscription);
  };

  const canPerformAction = (action: keyof SubscriptionFeatures): boolean => {
    if (!subscription.isActive) return false;
    
    // Special handling for daily scans
    if (action === 'dailyScans') {
      const dailyLimit = subscription.features.dailyScans;
      if (dailyLimit === 'unlimited') return true;
      return subscription.scanCount < dailyLimit;
    }
    
    return true; // For other features, just check if subscription is active
  };

  const incrementScanCount = async (): Promise<boolean> => {
    if (!canPerformAction('dailyScans')) {
      return false;
    }
    
    const newSubscription = {
      ...subscription,
      scanCount: subscription.scanCount + 1,
    };
    
    await saveSubscription(newSubscription);
    return true;
  };

  const resetDailyScanCount = async () => {
    const newSubscription = {
      ...subscription,
      scanCount: 0,
      lastResetDate: new Date(),
    };
    
    await saveSubscription(newSubscription);
  };

  return (
    <SubscriptionContext.Provider
      value={{
        subscription,
        upgradeTier,
        canPerformAction,
        incrementScanCount,
        resetDailyScanCount,
        isLoading,
      }}>
      {children}
    </SubscriptionContext.Provider>
  );
};

export default SubscriptionContext;