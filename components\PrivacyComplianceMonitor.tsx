import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  RefreshControl,
  Modal,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import {
  Shield,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Clock,
  BarChart3,
  FileText,
  Users,
  Database,
  Activity,
  Eye,
  Download,
  Settings,
  Bell,
  TrendingUp,
  TrendingDown,
  Info,
  Zap,
} from 'lucide-react-native';
import { supabase } from '../lib/supabase';

interface ComplianceMetrics {
  totalUsers: number;
  activeConsents: number;
  expiredConsents: number;
  withdrawnConsents: number;
  dataAccessRequests: number;
  privacyBreaches: number;
  riskAssessments: number;
  highRiskActivities: number;
}

interface PrivacyBreach {
  id: string;
  incident_type: string;
  severity_level: string;
  affected_users_count: number;
  incident_discovered_at: string;
  incident_resolved_at?: string;
  notification_required: boolean;
  users_notified: boolean;
}

export const PrivacyComplianceMonitor: React.FC = () => {
  const [metrics, setMetrics] = useState<ComplianceMetrics | null>(null);
  const [breaches, setBreaches] = useState<PrivacyBreach[]>([]);
  const [riskAssessments, setRiskAssessments] = useState<any[]>([]);
  const [consentTrends, setConsentTrends] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [activeTab, setActiveTab] = useState<'overview' | 'breaches' | 'risks' | 'compliance'>('overview');
  const [selectedBreach, setSelectedBreach] = useState<PrivacyBreach | null>(null);
  const [modalVisible, setModalVisible] = useState(false);

  useEffect(() => {
    loadComplianceData();
  }, []);

  const loadComplianceData = async () => {
    try {
      setLoading(true);
      
      await Promise.all([
        loadMetrics(),
        loadBreaches(),
        loadRiskAssessments(),
        loadConsentTrends(),
      ]);
    } catch (error) {
      console.error('Error loading compliance data:', error);
      Alert.alert('Error', 'Failed to load compliance data');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const loadMetrics = async () => {
    try {
      // Load various compliance metrics
      const [
        { count: totalUsers },
        { data: consents },
        { data: accessRequests },
        { data: breachData },
        { data: riskData }
      ] = await Promise.all([
        supabase.from('user_profiles').select('*', { count: 'exact', head: true }),
        supabase.from('privacy_consents').select('consent_status, expires_at, withdrawn_at'),
        supabase.from('data_access_log').select('*', { count: 'exact', head: true }),
        supabase.from('privacy_breach_incidents').select('*', { count: 'exact', head: true }),
        supabase.from('privacy_risk_assessments').select('risk_level')
      ]);

      const activeConsents = consents?.filter(c => 
        c.consent_status && 
        (!c.expires_at || new Date(c.expires_at) > new Date()) &&
        !c.withdrawn_at
      ).length || 0;

      const expiredConsents = consents?.filter(c => 
        c.expires_at && new Date(c.expires_at) <= new Date()
      ).length || 0;

      const withdrawnConsents = consents?.filter(c => c.withdrawn_at).length || 0;

      const highRiskActivities = riskData?.filter(r => 
        r.risk_level === 'high' || r.risk_level === 'critical'
      ).length || 0;

      setMetrics({
        totalUsers: totalUsers || 0,
        activeConsents,
        expiredConsents,
        withdrawnConsents,
        dataAccessRequests: accessRequests || 0,
        privacyBreaches: breachData || 0,
        riskAssessments: riskData?.length || 0,
        highRiskActivities,
      });
    } catch (error) {
      console.error('Error loading metrics:', error);
    }
  };

  const loadBreaches = async () => {
    try {
      const { data, error } = await supabase
        .from('privacy_breach_incidents')
        .select('*')
        .order('incident_discovered_at', { ascending: false })
        .limit(20);

      if (error) throw error;
      setBreaches(data || []);
    } catch (error) {
      console.error('Error loading breaches:', error);
    }
  };

  const loadRiskAssessments = async () => {
    try {
      const { data, error } = await supabase
        .from('privacy_risk_assessments')
        .select('*')
        .order('assessment_date', { ascending: false })
        .limit(20);

      if (error) throw error;
      setRiskAssessments(data || []);
    } catch (error) {
      console.error('Error loading risk assessments:', error);
    }
  };

  const loadConsentTrends = async () => {
    try {
      // This would typically aggregate consent data by date
      // For now, we'll use a simplified version
      const { data, error } = await supabase
        .from('privacy_consents')
        .select('created_at, consent_type, consent_status')
        .order('created_at', { ascending: false })
        .limit(100);

      if (error) throw error;
      setConsentTrends(data || []);
    } catch (error) {
      console.error('Error loading consent trends:', error);
    }
  };

  const onRefresh = () => {
    setRefreshing(true);
    loadComplianceData();
  };

  const handleBreachDetails = (breach: PrivacyBreach) => {
    setSelectedBreach(breach);
    setModalVisible(true);
  };

  const generateComplianceReport = async () => {
    try {
      Alert.alert(
        'Generate Report',
        'This would generate a comprehensive compliance report including all metrics, breaches, and risk assessments.',
        [
          { text: 'Cancel', style: 'cancel' },
          { text: 'Generate', onPress: () => console.log('Generating report...') }
        ]
      );
    } catch (error) {
      Alert.alert('Error', 'Failed to generate compliance report');
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return '#EF4444';
      case 'high': return '#F59E0B';
      case 'medium': return '#3B82F6';
      case 'low': return '#10B981';
      default: return '#6B7280';
    }
  };

  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case 'critical': return XCircle;
      case 'high': return AlertTriangle;
      case 'medium': return Info;
      case 'low': return CheckCircle;
      default: return Info;
    }
  };

  const renderOverview = () => (
    <ScrollView style={styles.tabContent}>
      {/* Key Metrics */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Compliance Overview</Text>
        
        <View style={styles.metricsGrid}>
          <View style={styles.metricCard}>
            <Users size={24} color="#3B82F6" />
            <Text style={styles.metricValue}>{metrics?.totalUsers || 0}</Text>
            <Text style={styles.metricLabel}>Total Users</Text>
          </View>
          
          <View style={styles.metricCard}>
            <CheckCircle size={24} color="#10B981" />
            <Text style={styles.metricValue}>{metrics?.activeConsents || 0}</Text>
            <Text style={styles.metricLabel}>Active Consents</Text>
          </View>
          
          <View style={styles.metricCard}>
            <Clock size={24} color="#F59E0B" />
            <Text style={styles.metricValue}>{metrics?.expiredConsents || 0}</Text>
            <Text style={styles.metricLabel}>Expired Consents</Text>
          </View>
          
          <View style={styles.metricCard}>
            <XCircle size={24} color="#EF4444" />
            <Text style={styles.metricValue}>{metrics?.withdrawnConsents || 0}</Text>
            <Text style={styles.metricLabel}>Withdrawn</Text>
          </View>
        </View>
      </View>

      {/* Risk Summary */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Risk Summary</Text>
        
        <View style={styles.riskSummary}>
          <View style={styles.riskItem}>
            <AlertTriangle size={20} color="#EF4444" />
            <Text style={styles.riskText}>
              {metrics?.highRiskActivities || 0} High Risk Activities
            </Text>
          </View>
          
          <View style={styles.riskItem}>
            <Shield size={20} color="#3B82F6" />
            <Text style={styles.riskText}>
              {metrics?.riskAssessments || 0} Risk Assessments Completed
            </Text>
          </View>
          
          <View style={styles.riskItem}>
            <Activity size={20} color="#10B981" />
            <Text style={styles.riskText}>
              {metrics?.dataAccessRequests || 0} Data Access Requests
            </Text>
          </View>
        </View>
      </View>

      {/* Recent Breaches */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Recent Privacy Incidents</Text>
        
        {breaches.slice(0, 3).map((breach) => {
          const SeverityIcon = getSeverityIcon(breach.severity_level);
          const severityColor = getSeverityColor(breach.severity_level);
          
          return (
            <TouchableOpacity
              key={breach.id}
              style={styles.breachCard}
              onPress={() => handleBreachDetails(breach)}
            >
              <View style={styles.breachHeader}>
                <SeverityIcon size={20} color={severityColor} />
                <Text style={styles.breachType}>
                  {breach.incident_type.replace('_', ' ').toUpperCase()}
                </Text>
                <Text style={[styles.breachSeverity, { color: severityColor }]}>
                  {breach.severity_level.toUpperCase()}
                </Text>
              </View>
              
              <Text style={styles.breachDetails}>
                Affected Users: {breach.affected_users_count}
              </Text>
              <Text style={styles.breachDetails}>
                Discovered: {new Date(breach.incident_discovered_at).toLocaleDateString()}
              </Text>
            </TouchableOpacity>
          );
        })}
      </View>

      {/* Actions */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Compliance Actions</Text>
        
        <TouchableOpacity
          style={styles.actionButton}
          onPress={generateComplianceReport}
        >
          <Download size={20} color="#3B82F6" />
          <Text style={styles.actionButtonText}>Generate Compliance Report</Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );

  const renderBreaches = () => (
    <ScrollView style={styles.tabContent}>
      <Text style={styles.sectionDescription}>
        Monitor and manage privacy breach incidents
      </Text>

      {breaches.map((breach) => {
        const SeverityIcon = getSeverityIcon(breach.severity_level);
        const severityColor = getSeverityColor(breach.severity_level);
        
        return (
          <TouchableOpacity
            key={breach.id}
            style={styles.breachCard}
            onPress={() => handleBreachDetails(breach)}
          >
            <View style={styles.breachHeader}>
              <SeverityIcon size={20} color={severityColor} />
              <Text style={styles.breachType}>
                {breach.incident_type.replace('_', ' ').toUpperCase()}
              </Text>
              <Text style={[styles.breachSeverity, { color: severityColor }]}>
                {breach.severity_level.toUpperCase()}
              </Text>
            </View>
            
            <Text style={styles.breachDetails}>
              Affected Users: {breach.affected_users_count}
            </Text>
            <Text style={styles.breachDetails}>
              Discovered: {new Date(breach.incident_discovered_at).toLocaleDateString()}
            </Text>
            
            <View style={styles.breachStatus}>
              <View style={styles.statusItem}>
                <Text style={styles.statusLabel}>Notification Required:</Text>
                <Text style={[styles.statusValue, { color: breach.notification_required ? '#F59E0B' : '#10B981' }]}>
                  {breach.notification_required ? 'Yes' : 'No'}
                </Text>
              </View>
              
              <View style={styles.statusItem}>
                <Text style={styles.statusLabel}>Users Notified:</Text>
                <Text style={[styles.statusValue, { color: breach.users_notified ? '#10B981' : '#EF4444' }]}>
                  {breach.users_notified ? 'Yes' : 'No'}
                </Text>
              </View>
            </View>
          </TouchableOpacity>
        );
      })}
    </ScrollView>
  );

  const renderRisks = () => (
    <ScrollView style={styles.tabContent}>
      <Text style={styles.sectionDescription}>
        Privacy risk assessments and mitigation measures
      </Text>

      {riskAssessments.map((assessment, index) => {
        const SeverityIcon = getSeverityIcon(assessment.risk_level);
        const severityColor = getSeverityColor(assessment.risk_level);
        
        return (
          <View key={index} style={styles.riskCard}>
            <View style={styles.riskHeader}>
              <SeverityIcon size={20} color={severityColor} />
              <Text style={styles.riskType}>
                {assessment.assessment_type.replace('_', ' ').toUpperCase()}
              </Text>
              <Text style={[styles.riskLevel, { color: severityColor }]}>
                {assessment.risk_level.toUpperCase()}
              </Text>
            </View>
            
            <Text style={styles.riskScore}>
              Risk Score: {(assessment.risk_score * 100).toFixed(1)}%
            </Text>
            
            <Text style={styles.riskSensitivity}>
              Data Sensitivity: {assessment.data_sensitivity_level}
            </Text>
            
            <Text style={styles.riskDate}>
              Assessed: {new Date(assessment.assessment_date).toLocaleDateString()}
            </Text>
          </View>
        );
      })}
    </ScrollView>
  );

  const renderCompliance = () => (
    <ScrollView style={styles.tabContent}>
      <Text style={styles.sectionDescription}>
        Compliance status and regulatory requirements
      </Text>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>GDPR Compliance</Text>
        
        <View style={styles.complianceItem}>
          <CheckCircle size={20} color="#10B981" />
          <Text style={styles.complianceText}>Data Processing Records</Text>
          <Text style={styles.complianceStatus}>Compliant</Text>
        </View>
        
        <View style={styles.complianceItem}>
          <CheckCircle size={20} color="#10B981" />
          <Text style={styles.complianceText}>Consent Management</Text>
          <Text style={styles.complianceStatus}>Compliant</Text>
        </View>
        
        <View style={styles.complianceItem}>
          <AlertTriangle size={20} color="#F59E0B" />
          <Text style={styles.complianceText}>Data Retention Policies</Text>
          <Text style={styles.complianceStatus}>Review Required</Text>
        </View>
        
        <View style={styles.complianceItem}>
          <CheckCircle size={20} color="#10B981" />
          <Text style={styles.complianceText}>Breach Notification</Text>
          <Text style={styles.complianceStatus}>Compliant</Text>
        </View>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>CCPA Compliance</Text>
        
        <View style={styles.complianceItem}>
          <CheckCircle size={20} color="#10B981" />
          <Text style={styles.complianceText}>Right to Know</Text>
          <Text style={styles.complianceStatus}>Compliant</Text>
        </View>
        
        <View style={styles.complianceItem}>
          <CheckCircle size={20} color="#10B981" />
          <Text style={styles.complianceText}>Right to Delete</Text>
          <Text style={styles.complianceStatus}>Compliant</Text>
        </View>
        
        <View style={styles.complianceItem}>
          <CheckCircle size={20} color="#10B981" />
          <Text style={styles.complianceText}>Opt-Out Rights</Text>
          <Text style={styles.complianceStatus}>Compliant</Text>
        </View>
      </View>
    </ScrollView>
  );

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <Shield size={32} color="#3B82F6" />
        <Text style={styles.loadingText}>Loading compliance data...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Header */}
      <LinearGradient
        colors={['#7C3AED', '#5B21B6']}
        style={styles.header}
      >
        <Shield size={32} color="white" />
        <Text style={styles.headerTitle}>Privacy Compliance Monitor</Text>
        <Text style={styles.headerSubtitle}>
          Monitor compliance status and privacy incidents
        </Text>
      </LinearGradient>

      {/* Tab Navigation */}
      <View style={styles.tabContainer}>
        {[
          { key: 'overview', label: 'Overview', icon: BarChart3 },
          { key: 'breaches', label: 'Breaches', icon: AlertTriangle },
          { key: 'risks', label: 'Risks', icon: Shield },
          { key: 'compliance', label: 'Compliance', icon: CheckCircle },
        ].map(({ key, label, icon: Icon }) => (
          <TouchableOpacity
            key={key}
            style={[styles.tab, activeTab === key && styles.activeTab]}
            onPress={() => setActiveTab(key as any)}
          >
            <Icon size={18} color={activeTab === key ? '#7C3AED' : '#6B7280'} />
            <Text style={[styles.tabText, activeTab === key && styles.activeTabText]}>
              {label}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      {/* Content */}
      <View style={styles.content}>
        {activeTab === 'overview' && renderOverview()}
        {activeTab === 'breaches' && renderBreaches()}
        {activeTab === 'risks' && renderRisks()}
        {activeTab === 'compliance' && renderCompliance()}
      </View>

      {/* Breach Details Modal */}
      <Modal
        visible={modalVisible}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setModalVisible(false)}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Breach Details</Text>
            <TouchableOpacity onPress={() => setModalVisible(false)}>
              <XCircle size={24} color="#6B7280" />
            </TouchableOpacity>
          </View>

          {selectedBreach && (
            <ScrollView style={styles.modalContent}>
              <Text style={styles.modalSection}>
                Type: {selectedBreach.incident_type.replace('_', ' ')}
              </Text>
              <Text style={styles.modalSection}>
                Severity: {selectedBreach.severity_level}
              </Text>
              <Text style={styles.modalSection}>
                Affected Users: {selectedBreach.affected_users_count}
              </Text>
              <Text style={styles.modalSection}>
                Discovered: {new Date(selectedBreach.incident_discovered_at).toLocaleString()}
              </Text>
              {selectedBreach.incident_resolved_at && (
                <Text style={styles.modalSection}>
                  Resolved: {new Date(selectedBreach.incident_resolved_at).toLocaleString()}
                </Text>
              )}
            </ScrollView>
          )}
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#F9FAFB',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#6B7280',
  },
  header: {
    padding: 24,
    paddingTop: 60,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: 'white',
    marginTop: 8,
  },
  headerSubtitle: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.8)',
    marginTop: 4,
    textAlign: 'center',
  },
  tabContainer: {
    flexDirection: 'row',
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  tab: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    gap: 6,
  },
  activeTab: {
    borderBottomWidth: 2,
    borderBottomColor: '#7C3AED',
  },
  tabText: {
    fontSize: 12,
    fontWeight: '500',
    color: '#6B7280',
  },
  activeTabText: {
    color: '#7C3AED',
  },
  content: {
    flex: 1,
  },
  tabContent: {
    flex: 1,
    padding: 20,
  },
  sectionDescription: {
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 20,
    lineHeight: 20,
  },
  section: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 16,
  },
  metricsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  metricCard: {
    flex: 1,
    minWidth: '45%',
    backgroundColor: '#F9FAFB',
    borderRadius: 8,
    padding: 16,
    alignItems: 'center',
  },
  metricValue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#111827',
    marginTop: 8,
  },
  metricLabel: {
    fontSize: 12,
    color: '#6B7280',
    marginTop: 4,
    textAlign: 'center',
  },
  riskSummary: {
    gap: 12,
  },
  riskItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  riskText: {
    fontSize: 16,
    color: '#374151',
  },
  breachCard: {
    backgroundColor: '#F9FAFB',
    borderRadius: 8,
    padding: 16,
    marginBottom: 12,
  },
  breachHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
    gap: 8,
  },
  breachType: {
    flex: 1,
    fontSize: 16,
    fontWeight: '600',
    color: '#111827',
  },
  breachSeverity: {
    fontSize: 12,
    fontWeight: '600',
  },
  breachDetails: {
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 4,
  },
  breachStatus: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 8,
  },
  statusItem: {
    flex: 1,
  },
  statusLabel: {
    fontSize: 12,
    color: '#6B7280',
  },
  statusValue: {
    fontSize: 12,
    fontWeight: '600',
  },
  riskCard: {
    backgroundColor: '#F9FAFB',
    borderRadius: 8,
    padding: 16,
    marginBottom: 12,
  },
  riskHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
    gap: 8,
  },
  riskType: {
    flex: 1,
    fontSize: 16,
    fontWeight: '600',
    color: '#111827',
  },
  riskLevel: {
    fontSize: 12,
    fontWeight: '600',
  },
  riskScore: {
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 4,
  },
  riskSensitivity: {
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 4,
  },
  riskDate: {
    fontSize: 12,
    color: '#9CA3AF',
  },
  complianceItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
    gap: 12,
  },
  complianceText: {
    flex: 1,
    fontSize: 16,
    color: '#374151',
  },
  complianceStatus: {
    fontSize: 14,
    fontWeight: '500',
    color: '#10B981',
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#EBF8FF',
    padding: 16,
    borderRadius: 8,
    gap: 12,
  },
  actionButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#1E40AF',
  },
  modalContainer: {
    flex: 1,
    backgroundColor: 'white',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827',
  },
  modalContent: {
    flex: 1,
    padding: 20,
  },
  modalSection: {
    fontSize: 16,
    color: '#374151',
    marginBottom: 12,
    lineHeight: 24,
  },
});

export default PrivacyComplianceMonitor;
