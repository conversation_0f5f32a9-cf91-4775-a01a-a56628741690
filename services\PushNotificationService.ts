import { supabase } from '../lib/supabase';
import * as Notifications from 'expo-notifications';
import * as Device from 'expo-device';
import { Platform } from 'react-native';
import Constants from 'expo-constants';

export interface NotificationPreferences {
  notifications_enabled: boolean;
  push_enabled: boolean;
  email_enabled: boolean;
  in_app_enabled: boolean;
  daily_tips_enabled: boolean;
  achievements_enabled: boolean;
  reminders_enabled: boolean;
  social_enabled: boolean;
  system_enabled: boolean;
  marketing_enabled: boolean;
  educational_enabled: boolean;
  quiet_hours_enabled: boolean;
  quiet_hours_start: string;
  quiet_hours_end: string;
  timezone: string;
  max_daily_notifications: number;
  max_weekly_notifications: number;
  personalized_content: boolean;
  location_based_content: boolean;
  behavior_based_content: boolean;
}

export interface NotificationTemplate {
  id: string;
  name: string;
  category: string;
  title_template: string;
  body_template: string;
  action_url?: string;
  icon_url?: string;
  image_url?: string;
  personalization_enabled: boolean;
  is_active: boolean;
}

export interface PushToken {
  token: string;
  platform: 'ios' | 'android' | 'web';
  device_id?: string;
  device_name?: string;
  app_version?: string;
}

export interface NotificationData {
  title: string;
  body: string;
  data?: any;
  categoryId?: string;
  sound?: boolean;
  badge?: number;
  priority?: 'low' | 'normal' | 'high' | 'urgent';
}

export class PushNotificationService {
  private static instance: PushNotificationService;
  private isInitialized = false;

  static getInstance(): PushNotificationService {
    if (!PushNotificationService.instance) {
      PushNotificationService.instance = new PushNotificationService();
    }
    return PushNotificationService.instance;
  }

  /**
   * Initialize push notification service
   */
  async initialize(): Promise<boolean> {
    try {
      if (this.isInitialized) return true;

      // Configure notification behavior
      await Notifications.setNotificationHandler({
        handleNotification: async () => ({
          shouldShowAlert: true,
          shouldPlaySound: true,
          shouldSetBadge: true,
        }),
      });

      // Request permissions
      const { status: existingStatus } = await Notifications.getPermissionsAsync();
      let finalStatus = existingStatus;

      if (existingStatus !== 'granted') {
        const { status } = await Notifications.requestPermissionsAsync();
        finalStatus = status;
      }

      if (finalStatus !== 'granted') {
        console.warn('Push notification permissions not granted');
        return false;
      }

      // Register for push notifications and get token
      if (Device.isDevice) {
        const token = await this.registerForPushNotifications();
        if (token) {
          await this.savePushToken(token);
        }
      }

      // Set up notification listeners
      this.setupNotificationListeners();

      this.isInitialized = true;
      return true;
    } catch (error) {
      console.error('Error initializing push notifications:', error);
      return false;
    }
  }

  /**
   * Register for push notifications and get token
   */
  private async registerForPushNotifications(): Promise<string | null> {
    try {
      if (!Device.isDevice) {
        console.warn('Must use physical device for push notifications');
        return null;
      }

      const token = await Notifications.getExpoPushTokenAsync({
        projectId: Constants.expoConfig?.extra?.eas?.projectId,
      });

      return token.data;
    } catch (error) {
      console.error('Error getting push token:', error);
      return null;
    }
  }

  /**
   * Save push token to database
   */
  private async savePushToken(token: string): Promise<boolean> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        throw new Error('User not authenticated');
      }

      const deviceInfo = await Device.getDeviceTypeAsync();
      const deviceName = await Device.deviceName;

      const pushTokenData: PushToken = {
        token,
        platform: Platform.OS as 'ios' | 'android',
        device_id: Constants.sessionId,
        device_name: deviceName || `${Platform.OS} Device`,
        app_version: Constants.expoConfig?.version || '1.0.0',
      };

      const { error } = await supabase
        .from('push_notification_tokens')
        .upsert({
          user_id: user.id,
          ...pushTokenData,
          last_used_at: new Date().toISOString(),
        });

      if (error) {
        throw error;
      }

      return true;
    } catch (error) {
      console.error('Error saving push token:', error);
      return false;
    }
  }

  /**
   * Set up notification event listeners
   */
  private setupNotificationListeners(): void {
    // Handle notification received while app is in foreground
    Notifications.addNotificationReceivedListener((notification) => {
      console.log('Notification received:', notification);
      this.trackNotificationEvent('delivered', notification.request.identifier);
    });

    // Handle notification tapped
    Notifications.addNotificationResponseReceivedListener((response) => {
      console.log('Notification tapped:', response);
      this.trackNotificationEvent('opened', response.notification.request.identifier);
      
      // Handle notification action
      const actionUrl = response.notification.request.content.data?.actionUrl;
      if (actionUrl) {
        this.handleNotificationAction(actionUrl);
      }
    });
  }

  /**
   * Get user's notification preferences
   */
  async getNotificationPreferences(): Promise<NotificationPreferences | null> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        throw new Error('User not authenticated');
      }

      const { data, error } = await supabase
        .from('user_notification_preferences')
        .select('*')
        .eq('user_id', user.id)
        .single();

      if (error && error.code !== 'PGRST116') {
        throw error;
      }

      // Return default preferences if none exist
      if (!data) {
        return this.getDefaultPreferences();
      }

      return data;
    } catch (error) {
      console.error('Error fetching notification preferences:', error);
      return null;
    }
  }

  /**
   * Update user's notification preferences
   */
  async updateNotificationPreferences(preferences: Partial<NotificationPreferences>): Promise<boolean> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        throw new Error('User not authenticated');
      }

      const { error } = await supabase
        .from('user_notification_preferences')
        .upsert({
          user_id: user.id,
          ...preferences,
          updated_at: new Date().toISOString(),
        });

      if (error) {
        throw error;
      }

      // Update local notification settings based on preferences
      if (preferences.push_enabled === false) {
        await this.disablePushNotifications();
      }

      return true;
    } catch (error) {
      console.error('Error updating notification preferences:', error);
      return false;
    }
  }

  /**
   * Schedule a local notification
   */
  async scheduleLocalNotification(
    notificationData: NotificationData,
    trigger?: Notifications.NotificationTriggerInput
  ): Promise<string | null> {
    try {
      const identifier = await Notifications.scheduleNotificationAsync({
        content: {
          title: notificationData.title,
          body: notificationData.body,
          data: notificationData.data || {},
          sound: notificationData.sound !== false,
          badge: notificationData.badge,
          priority: this.mapPriorityToExpo(notificationData.priority || 'normal'),
        },
        trigger: trigger || null,
      });

      return identifier;
    } catch (error) {
      console.error('Error scheduling local notification:', error);
      return null;
    }
  }

  /**
   * Cancel a scheduled notification
   */
  async cancelNotification(identifier: string): Promise<boolean> {
    try {
      await Notifications.cancelScheduledNotificationAsync(identifier);
      return true;
    } catch (error) {
      console.error('Error cancelling notification:', error);
      return false;
    }
  }

  /**
   * Cancel all scheduled notifications
   */
  async cancelAllNotifications(): Promise<boolean> {
    try {
      await Notifications.cancelAllScheduledNotificationsAsync();
      return true;
    } catch (error) {
      console.error('Error cancelling all notifications:', error);
      return false;
    }
  }

  /**
   * Get notification templates
   */
  async getNotificationTemplates(category?: string): Promise<NotificationTemplate[]> {
    try {
      let query = supabase
        .from('notification_templates')
        .select('*')
        .eq('is_active', true);

      if (category) {
        query = query.eq('category', category);
      }

      const { data, error } = await query.order('name');

      if (error) {
        throw error;
      }

      return data || [];
    } catch (error) {
      console.error('Error fetching notification templates:', error);
      return [];
    }
  }

  /**
   * Send personalized notification
   */
  async sendPersonalizedNotification(
    templateName: string,
    personalizationData: any = {},
    scheduledFor?: Date
  ): Promise<boolean> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        throw new Error('User not authenticated');
      }

      // Check user preferences
      const preferences = await this.getNotificationPreferences();
      if (!preferences?.notifications_enabled || !preferences?.push_enabled) {
        return false;
      }

      // Get template
      const { data: template, error: templateError } = await supabase
        .from('notification_templates')
        .select('*')
        .eq('name', templateName)
        .eq('is_active', true)
        .single();

      if (templateError || !template) {
        throw new Error('Template not found');
      }

      // Check category preference
      const categoryEnabled = this.isCategoryEnabled(template.category, preferences);
      if (!categoryEnabled) {
        return false;
      }

      // Personalize content
      const personalizedTitle = this.personalizeContent(template.title_template, personalizationData);
      const personalizedBody = this.personalizeContent(template.body_template, personalizationData);

      // Queue notification
      const { error: queueError } = await supabase
        .from('notification_queue')
        .insert({
          user_id: user.id,
          template_id: template.id,
          title: personalizedTitle,
          body: personalizedBody,
          action_url: template.action_url,
          icon_url: template.icon_url,
          image_url: template.image_url,
          custom_data: personalizationData,
          scheduled_for: scheduledFor?.toISOString() || new Date().toISOString(),
        });

      if (queueError) {
        throw queueError;
      }

      return true;
    } catch (error) {
      console.error('Error sending personalized notification:', error);
      return false;
    }
  }

  /**
   * Track notification events for analytics
   */
  private async trackNotificationEvent(eventType: string, notificationId: string): Promise<void> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return;

      // Update delivery log
      await supabase
        .from('notification_delivery_log')
        .update({
          status: eventType,
          [`${eventType}_at`]: new Date().toISOString(),
        })
        .eq('queue_id', notificationId);

      // Update user engagement scores
      await this.updateUserEngagement(user.id, eventType);
    } catch (error) {
      console.error('Error tracking notification event:', error);
    }
  }

  /**
   * Update user engagement scores
   */
  private async updateUserEngagement(userId: string, eventType: string): Promise<void> {
    try {
      const updateData: any = {};

      switch (eventType) {
        case 'delivered':
          updateData.total_notifications_received = 'total_notifications_received + 1';
          break;
        case 'opened':
          updateData.total_notifications_opened = 'total_notifications_opened + 1';
          updateData.last_engagement_at = new Date().toISOString();
          break;
        case 'clicked':
          updateData.total_notifications_clicked = 'total_notifications_clicked + 1';
          updateData.last_engagement_at = new Date().toISOString();
          break;
      }

      await supabase.rpc('update_user_engagement', {
        user_uuid: userId,
        event_type: eventType,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Error updating user engagement:', error);
    }
  }

  /**
   * Handle notification action
   */
  private handleNotificationAction(actionUrl: string): void {
    // This would integrate with your navigation system
    console.log('Handling notification action:', actionUrl);
    // Example: navigate to specific screen based on actionUrl
  }

  /**
   * Disable push notifications
   */
  private async disablePushNotifications(): Promise<void> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return;

      // Deactivate all push tokens for this user
      await supabase
        .from('push_notification_tokens')
        .update({ is_active: false })
        .eq('user_id', user.id);

      // Cancel all scheduled notifications
      await this.cancelAllNotifications();
    } catch (error) {
      console.error('Error disabling push notifications:', error);
    }
  }

  /**
   * Check if category is enabled in user preferences
   */
  private isCategoryEnabled(category: string, preferences: NotificationPreferences): boolean {
    const categoryMap: { [key: string]: keyof NotificationPreferences } = {
      'daily_tip': 'daily_tips_enabled',
      'achievement': 'achievements_enabled',
      'reminder': 'reminders_enabled',
      'social': 'social_enabled',
      'system': 'system_enabled',
      'marketing': 'marketing_enabled',
      'educational': 'educational_enabled',
    };

    const preferenceKey = categoryMap[category];
    return preferenceKey ? preferences[preferenceKey] as boolean : true;
  }

  /**
   * Personalize notification content
   */
  private personalizeContent(template: string, data: any): string {
    let content = template;
    
    // Replace template variables
    Object.keys(data).forEach(key => {
      const placeholder = `{{${key}}}`;
      content = content.replace(new RegExp(placeholder, 'g'), data[key]);
    });

    return content;
  }

  /**
   * Map priority to Expo notification priority
   */
  private mapPriorityToExpo(priority: string): Notifications.AndroidNotificationPriority {
    switch (priority) {
      case 'low': return Notifications.AndroidNotificationPriority.LOW;
      case 'high': return Notifications.AndroidNotificationPriority.HIGH;
      case 'urgent': return Notifications.AndroidNotificationPriority.MAX;
      default: return Notifications.AndroidNotificationPriority.DEFAULT;
    }
  }

  /**
   * Get default notification preferences
   */
  private getDefaultPreferences(): NotificationPreferences {
    return {
      notifications_enabled: true,
      push_enabled: true,
      email_enabled: true,
      in_app_enabled: true,
      daily_tips_enabled: true,
      achievements_enabled: true,
      reminders_enabled: true,
      social_enabled: true,
      system_enabled: true,
      marketing_enabled: false,
      educational_enabled: true,
      quiet_hours_enabled: false,
      quiet_hours_start: '22:00:00',
      quiet_hours_end: '08:00:00',
      timezone: 'UTC',
      max_daily_notifications: 5,
      max_weekly_notifications: 20,
      personalized_content: true,
      location_based_content: false,
      behavior_based_content: true,
    };
  }
}
