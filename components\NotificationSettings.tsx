import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Switch,
  TouchableOpacity,
  Alert,
  Platform,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import DateTimePicker from '@react-native-community/datetimepicker';
import {
  Bell,
  BellOff,
  Clock,
  Mail,
  Smartphone,
  Trophy,
  MapPin,
  Users,
  BookOpen,
  Settings,
  Moon,
  Sun,
  Volume2,
  VolumeX,
} from 'lucide-react-native';
import {
  PushNotificationService,
  NotificationPreferences,
} from '../services/PushNotificationService';

interface NotificationSettingsProps {
  onPreferencesChange?: (preferences: NotificationPreferences) => void;
}

export const NotificationSettings: React.FC<NotificationSettingsProps> = ({
  onPreferencesChange,
}) => {
  const [preferences, setPreferences] = useState<NotificationPreferences | null>(null);
  const [loading, setLoading] = useState(true);
  const [showQuietStartPicker, setShowQuietStartPicker] = useState(false);
  const [showQuietEndPicker, setShowQuietEndPicker] = useState(false);

  const pushService = PushNotificationService.getInstance();

  useEffect(() => {
    loadPreferences();
  }, []);

  const loadPreferences = async () => {
    try {
      setLoading(true);
      const prefs = await pushService.getNotificationPreferences();
      setPreferences(prefs);
    } catch (error) {
      console.error('Error loading notification preferences:', error);
      Alert.alert('Error', 'Failed to load notification settings');
    } finally {
      setLoading(false);
    }
  };

  const updatePreference = async (key: keyof NotificationPreferences, value: any) => {
    if (!preferences) return;

    const updatedPreferences = { ...preferences, [key]: value };
    setPreferences(updatedPreferences);

    const success = await pushService.updateNotificationPreferences({ [key]: value });
    
    if (success) {
      if (onPreferencesChange) {
        onPreferencesChange(updatedPreferences);
      }
    } else {
      // Revert on failure
      setPreferences(preferences);
      Alert.alert('Error', 'Failed to update notification setting');
    }
  };

  const handleQuietHoursChange = (event: any, selectedTime?: Date) => {
    if (Platform.OS === 'android') {
      setShowQuietStartPicker(false);
      setShowQuietEndPicker(false);
    }

    if (selectedTime) {
      const timeString = selectedTime.toTimeString().slice(0, 8);
      
      if (showQuietStartPicker) {
        updatePreference('quiet_hours_start', timeString);
      } else if (showQuietEndPicker) {
        updatePreference('quiet_hours_end', timeString);
      }
    }
  };

  const formatTime = (timeString: string) => {
    const [hours, minutes] = timeString.split(':');
    const hour = parseInt(hours);
    const ampm = hour >= 12 ? 'PM' : 'AM';
    const displayHour = hour % 12 || 12;
    return `${displayHour}:${minutes} ${ampm}`;
  };

  const parseTime = (timeString: string) => {
    const [hours, minutes, seconds] = timeString.split(':');
    const date = new Date();
    date.setHours(parseInt(hours), parseInt(minutes), parseInt(seconds || '0'));
    return date;
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <Text style={styles.loadingText}>Loading notification settings...</Text>
      </View>
    );
  }

  if (!preferences) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>Failed to load notification settings</Text>
        <TouchableOpacity style={styles.retryButton} onPress={loadPreferences}>
          <Text style={styles.retryText}>Retry</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container}>
      {/* Header */}
      <LinearGradient
        colors={['#3B82F6', '#1D4ED8']}
        style={styles.header}
      >
        <Bell size={32} color="white" />
        <Text style={styles.headerTitle}>Notification Settings</Text>
        <Text style={styles.headerSubtitle}>
          Customize how and when you receive notifications
        </Text>
      </LinearGradient>

      {/* Global Settings */}
      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <Settings size={20} color="#3B82F6" />
          <Text style={styles.sectionTitle}>Global Settings</Text>
        </View>

        <SettingRow
          icon={preferences.notifications_enabled ? Bell : BellOff}
          title="All Notifications"
          description="Enable or disable all notifications"
          value={preferences.notifications_enabled}
          onValueChange={(value) => updatePreference('notifications_enabled', value)}
        />

        <SettingRow
          icon={Smartphone}
          title="Push Notifications"
          description="Receive notifications on your device"
          value={preferences.push_enabled}
          onValueChange={(value) => updatePreference('push_enabled', value)}
          disabled={!preferences.notifications_enabled}
        />

        <SettingRow
          icon={Mail}
          title="Email Notifications"
          description="Receive notifications via email"
          value={preferences.email_enabled}
          onValueChange={(value) => updatePreference('email_enabled', value)}
          disabled={!preferences.notifications_enabled}
        />
      </View>

      {/* Notification Categories */}
      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <Bell size={20} color="#3B82F6" />
          <Text style={styles.sectionTitle}>Notification Types</Text>
        </View>

        <SettingRow
          icon={BookOpen}
          title="Daily Tips"
          description="Receive daily nature tips and facts"
          value={preferences.daily_tips_enabled}
          onValueChange={(value) => updatePreference('daily_tips_enabled', value)}
          disabled={!preferences.notifications_enabled}
        />

        <SettingRow
          icon={Trophy}
          title="Achievements"
          description="Get notified about your accomplishments"
          value={preferences.achievements_enabled}
          onValueChange={(value) => updatePreference('achievements_enabled', value)}
          disabled={!preferences.notifications_enabled}
        />

        <SettingRow
          icon={Clock}
          title="Reminders"
          description="Helpful reminders to explore nature"
          value={preferences.reminders_enabled}
          onValueChange={(value) => updatePreference('reminders_enabled', value)}
          disabled={!preferences.notifications_enabled}
        />

        <SettingRow
          icon={Users}
          title="Social"
          description="Community activities and interactions"
          value={preferences.social_enabled}
          onValueChange={(value) => updatePreference('social_enabled', value)}
          disabled={!preferences.notifications_enabled}
        />

        <SettingRow
          icon={BookOpen}
          title="Educational"
          description="Learning opportunities and challenges"
          value={preferences.educational_enabled}
          onValueChange={(value) => updatePreference('educational_enabled', value)}
          disabled={!preferences.notifications_enabled}
        />

        <SettingRow
          icon={Mail}
          title="Marketing"
          description="Promotional offers and updates"
          value={preferences.marketing_enabled}
          onValueChange={(value) => updatePreference('marketing_enabled', value)}
          disabled={!preferences.notifications_enabled}
        />
      </View>

      {/* Quiet Hours */}
      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <Moon size={20} color="#3B82F6" />
          <Text style={styles.sectionTitle}>Quiet Hours</Text>
        </View>

        <SettingRow
          icon={preferences.quiet_hours_enabled ? Moon : Sun}
          title="Enable Quiet Hours"
          description="Pause notifications during specified hours"
          value={preferences.quiet_hours_enabled}
          onValueChange={(value) => updatePreference('quiet_hours_enabled', value)}
          disabled={!preferences.notifications_enabled}
        />

        {preferences.quiet_hours_enabled && (
          <>
            <TouchableOpacity
              style={styles.timePickerRow}
              onPress={() => setShowQuietStartPicker(true)}
              disabled={!preferences.notifications_enabled}
            >
              <View style={styles.timePickerContent}>
                <Text style={styles.timePickerLabel}>Start Time</Text>
                <Text style={styles.timePickerValue}>
                  {formatTime(preferences.quiet_hours_start)}
                </Text>
              </View>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.timePickerRow}
              onPress={() => setShowQuietEndPicker(true)}
              disabled={!preferences.notifications_enabled}
            >
              <View style={styles.timePickerContent}>
                <Text style={styles.timePickerLabel}>End Time</Text>
                <Text style={styles.timePickerValue}>
                  {formatTime(preferences.quiet_hours_end)}
                </Text>
              </View>
            </TouchableOpacity>
          </>
        )}
      </View>

      {/* Personalization */}
      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <Users size={20} color="#3B82F6" />
          <Text style={styles.sectionTitle}>Personalization</Text>
        </View>

        <SettingRow
          icon={Users}
          title="Personalized Content"
          description="Tailor notifications to your interests"
          value={preferences.personalized_content}
          onValueChange={(value) => updatePreference('personalized_content', value)}
          disabled={!preferences.notifications_enabled}
        />

        <SettingRow
          icon={MapPin}
          title="Location-Based Content"
          description="Notifications based on your location"
          value={preferences.location_based_content}
          onValueChange={(value) => updatePreference('location_based_content', value)}
          disabled={!preferences.notifications_enabled}
        />

        <SettingRow
          icon={BookOpen}
          title="Behavior-Based Content"
          description="Notifications based on your app usage"
          value={preferences.behavior_based_content}
          onValueChange={(value) => updatePreference('behavior_based_content', value)}
          disabled={!preferences.notifications_enabled}
        />
      </View>

      {/* Frequency Limits */}
      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <Clock size={20} color="#3B82F6" />
          <Text style={styles.sectionTitle}>Frequency Limits</Text>
        </View>

        <View style={styles.frequencyRow}>
          <Text style={styles.frequencyLabel}>Daily Limit</Text>
          <Text style={styles.frequencyValue}>
            {preferences.max_daily_notifications} notifications
          </Text>
        </View>

        <View style={styles.frequencyRow}>
          <Text style={styles.frequencyLabel}>Weekly Limit</Text>
          <Text style={styles.frequencyValue}>
            {preferences.max_weekly_notifications} notifications
          </Text>
        </View>
      </View>

      {/* Time Pickers */}
      {showQuietStartPicker && (
        <DateTimePicker
          value={parseTime(preferences.quiet_hours_start)}
          mode="time"
          is24Hour={false}
          display="default"
          onChange={handleQuietHoursChange}
        />
      )}

      {showQuietEndPicker && (
        <DateTimePicker
          value={parseTime(preferences.quiet_hours_end)}
          mode="time"
          is24Hour={false}
          display="default"
          onChange={handleQuietHoursChange}
        />
      )}
    </ScrollView>
  );
};

interface SettingRowProps {
  icon: any;
  title: string;
  description: string;
  value: boolean;
  onValueChange: (value: boolean) => void;
  disabled?: boolean;
}

const SettingRow: React.FC<SettingRowProps> = ({
  icon: Icon,
  title,
  description,
  value,
  onValueChange,
  disabled = false,
}) => (
  <View style={[styles.settingRow, disabled && styles.disabledRow]}>
    <Icon size={20} color={disabled ? '#9CA3AF' : '#3B82F6'} />
    <View style={styles.settingContent}>
      <Text style={[styles.settingTitle, disabled && styles.disabledText]}>
        {title}
      </Text>
      <Text style={[styles.settingDescription, disabled && styles.disabledText]}>
        {description}
      </Text>
    </View>
    <Switch
      value={value}
      onValueChange={onValueChange}
      disabled={disabled}
      trackColor={{ false: '#E5E7EB', true: '#3B82F6' }}
      thumbColor={value ? '#FFFFFF' : '#F3F4F6'}
    />
  </View>
);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#F9FAFB',
  },
  loadingText: {
    fontSize: 16,
    color: '#6B7280',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#F9FAFB',
  },
  errorText: {
    fontSize: 16,
    color: '#EF4444',
    marginBottom: 20,
    textAlign: 'center',
  },
  retryButton: {
    backgroundColor: '#3B82F6',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
  },
  retryText: {
    color: 'white',
    fontWeight: '600',
  },
  header: {
    padding: 24,
    paddingTop: 60,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: 'white',
    marginTop: 8,
  },
  headerSubtitle: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.8)',
    marginTop: 4,
    textAlign: 'center',
  },
  section: {
    backgroundColor: 'white',
    margin: 16,
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
    gap: 8,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827',
  },
  settingRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
    gap: 12,
  },
  disabledRow: {
    opacity: 0.5,
  },
  settingContent: {
    flex: 1,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#111827',
    marginBottom: 2,
  },
  settingDescription: {
    fontSize: 14,
    color: '#6B7280',
    lineHeight: 18,
  },
  disabledText: {
    color: '#9CA3AF',
  },
  timePickerRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
  },
  timePickerContent: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  timePickerLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: '#111827',
  },
  timePickerValue: {
    fontSize: 16,
    color: '#3B82F6',
    fontWeight: '600',
  },
  frequencyRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
  },
  frequencyLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: '#111827',
  },
  frequencyValue: {
    fontSize: 16,
    color: '#6B7280',
  },
});

export default NotificationSettings;
