import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

interface FeatureFlagRequest {
  action: 'evaluate_flag' | 'evaluate_flags' | 'assign_experiment' | 'track_event';
  data: any;
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_ANON_KEY') ?? '',
      {
        global: {
          headers: { Authorization: req.headers.get('Authorization')! },
        },
      }
    );

    const { action, data }: FeatureFlagRequest = await req.json();

    let result;
    switch (action) {
      case 'evaluate_flag':
        result = await evaluateFeatureFlag(supabaseClient, data);
        break;
      case 'evaluate_flags':
        result = await evaluateFeatureFlags(supabaseClient, data);
        break;
      case 'assign_experiment':
        result = await assignExperiment(supabaseClient, data);
        break;
      case 'track_event':
        result = await trackEvent(supabaseClient, data);
        break;
      default:
        throw new Error(`Unknown action: ${action}`);
    }

    return new Response(JSON.stringify(result), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });
  } catch (error) {
    console.error('Feature flags error:', error);
    return new Response(JSON.stringify({ error: error.message }), {
      status: 400,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });
  }
});

/**
 * Evaluate a single feature flag for a user
 */
async function evaluateFeatureFlag(supabaseClient: any, data: {
  flag_key: string;
  user_id: string;
  user_context?: any;
  default_value?: any;
}) {
  const { flag_key, user_id, user_context = {}, default_value } = data;

  try {
    // Call the database function to evaluate the flag
    const { data: result, error } = await supabaseClient.rpc('evaluate_feature_flag', {
      flag_key_param: flag_key,
      user_id_param: user_id,
      user_context_param: user_context,
    });

    if (error) throw error;

    if (result && result.length > 0) {
      const evaluation = result[0];
      
      // Track the evaluation event
      await trackFlagEvent(supabaseClient, {
        event_type: 'flag_evaluation',
        user_id,
        flag_key,
        flag_value: evaluation.flag_value,
        user_context,
      });

      return {
        flag_key: evaluation.flag_key,
        value: evaluation.flag_value,
        reason: evaluation.assignment_reason,
        experiment_id: evaluation.experiment_id,
        variant_key: evaluation.variant_key,
      };
    }

    // Return default value if flag not found
    return {
      flag_key,
      value: default_value || false,
      reason: 'default',
    };
  } catch (error) {
    console.error(`Error evaluating flag ${flag_key}:`, error);
    return {
      flag_key,
      value: default_value || false,
      reason: 'error',
      error: error.message,
    };
  }
}

/**
 * Evaluate multiple feature flags for a user
 */
async function evaluateFeatureFlags(supabaseClient: any, data: {
  flag_keys: string[];
  user_id: string;
  user_context?: any;
}) {
  const { flag_keys, user_id, user_context = {} } = data;

  const results: any[] = [];

  // Evaluate each flag
  for (const flag_key of flag_keys) {
    const result = await evaluateFeatureFlag(supabaseClient, {
      flag_key,
      user_id,
      user_context,
    });
    results.push(result);
  }

  return { flags: results };
}

/**
 * Assign user to experiment variant
 */
async function assignExperiment(supabaseClient: any, data: {
  experiment_key: string;
  user_id: string;
  user_context?: any;
}) {
  const { experiment_key, user_id, user_context = {} } = data;

  try {
    // Check if user already has an assignment
    const { data: existingAssignment } = await supabaseClient
      .from('user_experiment_assignments')
      .select(`
        *,
        experiment_variants!inner(variant_key),
        ab_experiments!inner(experiment_key)
      `)
      .eq('user_id', user_id)
      .eq('ab_experiments.experiment_key', experiment_key)
      .single();

    if (existingAssignment) {
      return {
        experiment_key,
        variant_key: existingAssignment.experiment_variants.variant_key,
        assignment_method: existingAssignment.assignment_method,
        existing: true,
      };
    }

    // Call the database function to assign variant
    const { data: assignment, error } = await supabaseClient.rpc('assign_experiment_variant', {
      experiment_key_param: experiment_key,
      user_id_param: user_id,
      user_context_param: user_context,
    });

    if (error) throw error;

    if (assignment && assignment.length > 0) {
      const result = assignment[0];
      
      // Create the assignment record
      await supabaseClient
        .from('user_experiment_assignments')
        .insert({
          user_id,
          experiment_id: result.experiment_id,
          variant_id: result.variant_id,
          assignment_method: 'deterministic',
          assignment_hash: result.assignment_hash,
          assignment_context: user_context,
        });

      // Track assignment event
      await trackExperimentEvent(supabaseClient, {
        event_type: 'assignment',
        user_id,
        experiment_id: result.experiment_id,
        variant_id: result.variant_id,
        user_context,
      });

      return {
        experiment_key,
        variant_key: result.variant_key,
        assignment_method: 'deterministic',
        existing: false,
      };
    }

    return {
      experiment_key,
      variant_key: null,
      reason: 'not_eligible',
    };
  } catch (error) {
    console.error(`Error assigning experiment ${experiment_key}:`, error);
    return {
      experiment_key,
      variant_key: null,
      error: error.message,
    };
  }
}

/**
 * Track feature flag or experiment event
 */
async function trackEvent(supabaseClient: any, data: {
  event_type: string;
  user_id: string;
  flag_key?: string;
  experiment_key?: string;
  event_data?: any;
  user_context?: any;
}) {
  const { event_type, user_id, flag_key, experiment_key, event_data = {}, user_context = {} } = data;

  try {
    if (flag_key) {
      await trackFlagEvent(supabaseClient, {
        event_type,
        user_id,
        flag_key,
        flag_value: event_data.flag_value,
        user_context,
      });
    }

    if (experiment_key) {
      // Get experiment and variant IDs
      const { data: assignment } = await supabaseClient
        .from('user_experiment_assignments')
        .select(`
          experiment_id,
          variant_id,
          ab_experiments!inner(experiment_key)
        `)
        .eq('user_id', user_id)
        .eq('ab_experiments.experiment_key', experiment_key)
        .single();

      if (assignment) {
        await trackExperimentEvent(supabaseClient, {
          event_type,
          user_id,
          experiment_id: assignment.experiment_id,
          variant_id: assignment.variant_id,
          event_data,
          user_context,
        });

        // Update conversion tracking if this is a conversion event
        if (event_type === 'conversion') {
          await supabaseClient
            .from('user_experiment_assignments')
            .update({
              has_converted: true,
              conversion_value: event_data.conversion_value || 0,
            })
            .eq('user_id', user_id)
            .eq('experiment_id', assignment.experiment_id);

          // Update variant conversion count
          await supabaseClient.rpc('increment_variant_conversion', {
            variant_id_param: assignment.variant_id,
          });
        }
      }
    }

    return { success: true };
  } catch (error) {
    console.error('Error tracking event:', error);
    return { success: false, error: error.message };
  }
}

/**
 * Track feature flag event
 */
async function trackFlagEvent(supabaseClient: any, data: {
  event_type: string;
  user_id: string;
  flag_key: string;
  flag_value?: any;
  user_context?: any;
}) {
  const { event_type, user_id, flag_key, flag_value, user_context = {} } = data;

  try {
    // Get flag ID
    const { data: flag } = await supabaseClient
      .from('feature_flags')
      .select('id')
      .eq('flag_key', flag_key)
      .single();

    await supabaseClient
      .from('feature_flag_events')
      .insert({
        event_type,
        user_id,
        feature_flag_id: flag?.id,
        flag_key,
        flag_value,
        user_context,
      });
  } catch (error) {
    console.error('Error tracking flag event:', error);
  }
}

/**
 * Track experiment event
 */
async function trackExperimentEvent(supabaseClient: any, data: {
  event_type: string;
  user_id: string;
  experiment_id: string;
  variant_id: string;
  event_data?: any;
  user_context?: any;
}) {
  const { event_type, user_id, experiment_id, variant_id, event_data = {}, user_context = {} } = data;

  try {
    await supabaseClient
      .from('experiment_events')
      .insert({
        event_type,
        user_id,
        experiment_id,
        variant_id,
        event_name: event_data.event_name,
        event_properties: event_data.event_properties || {},
        conversion_value: event_data.conversion_value || 0,
        user_context,
      });
  } catch (error) {
    console.error('Error tracking experiment event:', error);
  }
}
