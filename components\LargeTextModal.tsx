import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  Dimensions,
  Animated,
  Switch,
  ScrollView,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { X, Type, Eye, Smartphone, Check } from 'lucide-react-native';

const { width, height } = Dimensions.get('window');

interface LargeTextModalProps {
  visible: boolean;
  onClose: () => void;
  largeTextEnabled: boolean;
  onToggle: (enabled: boolean) => void;
}

const textSizeOptions = [
  { size: 'small', label: 'Small', multiplier: 0.9, description: 'Compact text for more content' },
  { size: 'normal', label: 'Normal', multiplier: 1.0, description: 'Standard text size' },
  { size: 'large', label: 'Large', multiplier: 1.2, description: 'Easier to read text' },
  { size: 'extra-large', label: 'Extra Large', multiplier: 1.4, description: 'Maximum readability' },
];

export default function LargeTextModal({
  visible,
  onClose,
  largeTextEnabled,
  onToggle,
}: LargeTextModalProps) {
  const [slideAnim] = useState(new Animated.Value(height));
  const [backdropAnim] = useState(new Animated.Value(0));
  const [selectedSize, setSelectedSize] = useState(largeTextEnabled ? 'large' : 'normal');

  useEffect(() => {
    if (visible) {
      Animated.parallel([
        Animated.timing(backdropAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: false,
        }),
        Animated.spring(slideAnim, {
          toValue: 0,
          tension: 100,
          friction: 8,
          useNativeDriver: false,
        }),
      ]).start();
    } else {
      Animated.parallel([
        Animated.timing(backdropAnim, {
          toValue: 0,
          duration: 200,
          useNativeDriver: false,
        }),
        Animated.timing(slideAnim, {
          toValue: height,
          duration: 250,
          useNativeDriver: false,
        }),
      ]).start();
    }
  }, [visible]);

  const handleSizeSelect = (size: string) => {
    setSelectedSize(size);
    const isLarge = size === 'large' || size === 'extra-large';
    onToggle(isLarge);
  };

  const getTextStyle = (multiplier: number) => ({
    fontSize: 16 * multiplier,
    lineHeight: 24 * multiplier,
  });

  return (
    <Modal
      visible={visible}
      transparent
      animationType="none"
      onRequestClose={onClose}>
      <Animated.View
        style={[
          styles.backdrop,
          {
            opacity: backdropAnim,
          },
        ]}>
        <TouchableOpacity
          style={styles.backdropTouch}
          activeOpacity={1}
          onPress={onClose}
        />
        
        <Animated.View
          style={[
            styles.container,
            {
              transform: [{ translateY: slideAnim }],
            },
          ]}>
          <View style={styles.card}>
            {/* Header */}
            <View style={styles.header}>
              <View style={styles.headerContent}>
                <View style={styles.headerIcon}>
                  <Type size={24} color="#3B82F6" />
                </View>
                <Text style={styles.headerTitle}>Text Size Settings</Text>
              </View>
              <TouchableOpacity style={styles.closeButton} onPress={onClose}>
                <X size={20} color="#6B7280" />
              </TouchableOpacity>
            </View>

            <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
              {/* Description */}
              <View style={styles.description}>
                <Text style={styles.descriptionText}>
                  Adjust the text size throughout the app for better readability. 
                  Changes will apply to all text elements including buttons, labels, and content.
                </Text>
              </View>

              {/* Quick Toggle */}
              <View style={styles.quickToggle}>
                <View style={styles.toggleHeader}>
                  <Eye size={20} color="#6B7280" />
                  <Text style={styles.toggleTitle}>Large Text Mode</Text>
                </View>
                <Switch
                  value={largeTextEnabled}
                  onValueChange={onToggle}
                  trackColor={{ false: '#E5E7EB', true: '#22C55E' }}
                  thumbColor={largeTextEnabled ? '#FFFFFF' : '#FFFFFF'}
                />
              </View>

              {/* Size Options */}
              <View style={styles.sizeOptions}>
                <Text style={styles.sectionTitle}>Text Size Options</Text>
                
                {textSizeOptions.map((option) => (
                  <TouchableOpacity
                    key={option.size}
                    style={[
                      styles.sizeOption,
                      selectedSize === option.size && styles.sizeOptionSelected,
                    ]}
                    onPress={() => handleSizeSelect(option.size)}>
                    
                    <View style={styles.sizeContent}>
                      <View style={styles.sizeLeft}>
                        <View style={[
                          styles.radioButton,
                          selectedSize === option.size && styles.radioButtonSelected,
                        ]}>
                          {selectedSize === option.size && (
                            <Check size={12} color="#FFFFFF" />
                          )}
                        </View>
                        
                        <View style={styles.sizeInfo}>
                          <Text style={[
                            styles.sizeLabel,
                            getTextStyle(option.multiplier),
                            selectedSize === option.size && styles.sizeLabelSelected,
                          ]}>
                            {option.label}
                          </Text>
                          <Text style={[
                            styles.sizeDescription,
                            selectedSize === option.size && styles.sizeDescriptionSelected,
                          ]}>
                            {option.description}
                          </Text>
                        </View>
                      </View>

                      {/* Preview Text */}
                      <View style={styles.previewContainer}>
                        <Text style={[
                          styles.previewText,
                          getTextStyle(option.multiplier),
                          { color: selectedSize === option.size ? '#1D4ED8' : '#6B7280' },
                        ]}>
                          Aa
                        </Text>
                      </View>
                    </View>
                  </TouchableOpacity>
                ))}
              </View>

              {/* Accessibility Info */}
              <View style={styles.accessibilityInfo}>
                <View style={styles.infoHeader}>
                  <Smartphone size={20} color="#22C55E" />
                  <Text style={styles.infoTitle}>Accessibility Features</Text>
                </View>
                <Text style={styles.infoText}>
                  • Large text improves readability for users with visual impairments{'\n'}
                  • Text scaling respects system accessibility settings{'\n'}
                  • All interface elements scale proportionally{'\n'}
                  • Compatible with screen readers and voice control
                </Text>
              </View>

              {/* Preview Section */}
              <View style={styles.previewSection}>
                <Text style={styles.sectionTitle}>Preview</Text>
                <View style={styles.previewCard}>
                  <Text style={[
                    styles.previewTitle,
                    getTextStyle(textSizeOptions.find(opt => opt.size === selectedSize)?.multiplier || 1),
                  ]}>
                    Sample Species Name
                  </Text>
                  <Text style={[
                    styles.previewSubtitle,
                    getTextStyle(textSizeOptions.find(opt => opt.size === selectedSize)?.multiplier || 1),
                  ]}>
                    Scientific name example
                  </Text>
                  <Text style={[
                    styles.previewBody,
                    getTextStyle(textSizeOptions.find(opt => opt.size === selectedSize)?.multiplier || 1),
                  ]}>
                    This is how regular text content will appear throughout the app with your selected text size setting.
                  </Text>
                </View>
              </View>
            </ScrollView>

            {/* Action Button */}
            <View style={styles.actions}>
              <TouchableOpacity style={styles.applyButton} onPress={onClose}>
                <LinearGradient
                  colors={['#22C55E', '#16A34A']}
                  style={styles.applyButtonGradient}>
                  <Check size={20} color="#FFFFFF" />
                  <Text style={styles.applyButtonText}>Apply Settings</Text>
                </LinearGradient>
              </TouchableOpacity>
            </View>
          </View>
        </Animated.View>
      </Animated.View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  backdrop: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  backdropTouch: {
    flex: 1,
  },
  container: {
    maxHeight: height * 0.9,
  },
  card: {
    backgroundColor: '#FFFFFF',
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    paddingTop: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 12,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 24,
    paddingVertical: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  headerIcon: {
    width: 40,
    height: 40,
    borderRadius: 12,
    backgroundColor: '#EBF8FF',
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerTitle: {
    fontSize: 20,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
  },
  closeButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#F3F4F6',
    alignItems: 'center',
    justifyContent: 'center',
  },
  content: {
    maxHeight: height * 0.6,
  },
  description: {
    paddingHorizontal: 24,
    paddingVertical: 16,
  },
  descriptionText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    lineHeight: 20,
  },
  quickToggle: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 24,
    paddingVertical: 16,
    backgroundColor: '#F8FAFC',
    marginHorizontal: 24,
    borderRadius: 12,
    marginBottom: 24,
  },
  toggleHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  toggleTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
  },
  sizeOptions: {
    paddingHorizontal: 24,
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    marginBottom: 16,
  },
  sizeOption: {
    borderWidth: 2,
    borderColor: '#E5E7EB',
    borderRadius: 16,
    padding: 16,
    marginBottom: 12,
    backgroundColor: '#FFFFFF',
  },
  sizeOptionSelected: {
    borderColor: '#3B82F6',
    backgroundColor: '#F8FAFF',
  },
  sizeContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  sizeLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
    flex: 1,
  },
  radioButton: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: '#D1D5DB',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#FFFFFF',
  },
  radioButtonSelected: {
    borderColor: '#3B82F6',
    backgroundColor: '#3B82F6',
  },
  sizeInfo: {
    flex: 1,
  },
  sizeLabel: {
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    marginBottom: 2,
  },
  sizeLabelSelected: {
    color: '#1D4ED8',
  },
  sizeDescription: {
    fontSize: 13,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    lineHeight: 18,
  },
  sizeDescriptionSelected: {
    color: '#3B82F6',
  },
  previewContainer: {
    width: 40,
    height: 40,
    borderRadius: 8,
    backgroundColor: '#F3F4F6',
    alignItems: 'center',
    justifyContent: 'center',
  },
  previewText: {
    fontFamily: 'Inter-Bold',
  },
  accessibilityInfo: {
    paddingHorizontal: 24,
    marginBottom: 24,
  },
  infoHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 12,
  },
  infoTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
  },
  infoText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    lineHeight: 20,
  },
  previewSection: {
    paddingHorizontal: 24,
    marginBottom: 24,
  },
  previewCard: {
    backgroundColor: '#F8FAFC',
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  previewTitle: {
    fontFamily: 'Inter-Bold',
    color: '#111827',
    marginBottom: 4,
  },
  previewSubtitle: {
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    fontStyle: 'italic',
    marginBottom: 8,
  },
  previewBody: {
    fontFamily: 'Inter-Regular',
    color: '#374151',
    lineHeight: 20,
  },
  actions: {
    paddingHorizontal: 24,
    paddingVertical: 20,
    borderTopWidth: 1,
    borderTopColor: '#F3F4F6',
  },
  applyButton: {
    borderRadius: 12,
    overflow: 'hidden',
  },
  applyButtonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    gap: 8,
  },
  applyButtonText: {
    fontSize: 16,
    fontFamily: 'Inter-Bold',
    color: '#FFFFFF',
  },
});