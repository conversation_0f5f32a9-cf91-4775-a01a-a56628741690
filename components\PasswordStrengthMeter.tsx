import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, Animated } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import zxcvbn from 'zxcvbn';

interface PasswordStrengthMeterProps {
  password: string;
  onStrengthChange?: (strength: PasswordStrength) => void;
  style?: any;
}

interface PasswordStrength {
  score: number;
  label: string;
  color: string;
  percentage: number;
  feedback: string[];
  isValid: boolean;
}

const PASSWORD_REQUIREMENTS = {
  minLength: 12,
  requireUppercase: true,
  requireLowercase: true,
  requireNumbers: true,
  requireSpecialChars: true,
};

export const PasswordStrengthMeter: React.FC<PasswordStrengthMeterProps> = ({
  password,
  onStrengthChange,
  style
}) => {
  const [strength, setStrength] = useState<PasswordStrength>({
    score: 0,
    label: 'Very Weak',
    color: '#ff4757',
    percentage: 0,
    feedback: [],
    isValid: false
  });

  const [animatedWidth] = useState(new Animated.Value(0));

  useEffect(() => {
    if (password) {
      const newStrength = calculatePasswordStrength(password);
      setStrength(newStrength);
      onStrengthChange?.(newStrength);

      // Animate progress bar
      Animated.timing(animatedWidth, {
        toValue: newStrength.percentage,
        duration: 300,
        useNativeDriver: false,
      }).start();
    } else {
      const emptyStrength = {
        score: 0,
        label: 'Very Weak',
        color: '#ff4757',
        percentage: 0,
        feedback: [],
        isValid: false
      };
      setStrength(emptyStrength);
      onStrengthChange?.(emptyStrength);
      
      Animated.timing(animatedWidth, {
        toValue: 0,
        duration: 300,
        useNativeDriver: false,
      }).start();
    }
  }, [password]);

  const calculatePasswordStrength = (pwd: string): PasswordStrength => {
    // Check basic requirements
    const requirements = {
      minLength: pwd.length >= PASSWORD_REQUIREMENTS.minLength,
      hasUppercase: /[A-Z]/.test(pwd),
      hasLowercase: /[a-z]/.test(pwd),
      hasNumbers: /\d/.test(pwd),
      hasSpecialChars: /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(pwd),
    };

    // Use zxcvbn for advanced analysis
    const zxcvbnResult = zxcvbn(pwd);

    // Generate feedback
    let feedback: string[] = [];
    
    if (!requirements.minLength) {
      feedback.push(`At least ${PASSWORD_REQUIREMENTS.minLength} characters`);
    }
    if (!requirements.hasUppercase) {
      feedback.push('Add uppercase letters (A-Z)');
    }
    if (!requirements.hasLowercase) {
      feedback.push('Add lowercase letters (a-z)');
    }
    if (!requirements.hasNumbers) {
      feedback.push('Add numbers (0-9)');
    }
    if (!requirements.hasSpecialChars) {
      feedback.push('Add special characters (!@#$...)');
    }

    // Add zxcvbn suggestions
    if (zxcvbnResult.feedback.suggestions.length > 0) {
      feedback.push(...zxcvbnResult.feedback.suggestions.slice(0, 2));
    }

    // Determine overall strength
    const basicRequirementsMet = Object.values(requirements).every(req => req);
    const isValid = basicRequirementsMet && zxcvbnResult.score >= 2;

    // Adjust score based on requirements
    let adjustedScore = zxcvbnResult.score;
    if (!basicRequirementsMet) {
      adjustedScore = Math.min(adjustedScore, 1);
    }

    const labels = ['Very Weak', 'Weak', 'Fair', 'Good', 'Strong'];
    const colors = ['#ff4757', '#ff6b7a', '#ffa502', '#2ed573', '#20bf6b'];
    const gradients = [
      ['#ff4757', '#ff3838'],
      ['#ff6b7a', '#ff5252'],
      ['#ffa502', '#ff8f00'],
      ['#2ed573', '#27ae60'],
      ['#20bf6b', '#16a085']
    ];

    return {
      score: adjustedScore,
      label: labels[adjustedScore] || 'Very Weak',
      color: colors[adjustedScore] || '#ff4757',
      percentage: Math.min(100, (adjustedScore / 4) * 100),
      feedback: feedback.slice(0, 3), // Limit to 3 suggestions
      isValid
    };
  };

  if (!password) {
    return null;
  }

  return (
    <View style={[styles.container, style]}>
      {/* Progress Bar */}
      <View style={styles.progressContainer}>
        <View style={styles.progressBackground}>
          <Animated.View
            style={[
              styles.progressBar,
              {
                width: animatedWidth.interpolate({
                  inputRange: [0, 100],
                  outputRange: ['0%', '100%'],
                  extrapolate: 'clamp',
                }),
              },
            ]}
          >
            <LinearGradient
              colors={[strength.color, strength.color]}
              style={styles.progressGradient}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 0 }}
            />
          </Animated.View>
        </View>
        
        {/* Strength Label */}
        <Text style={[styles.strengthLabel, { color: strength.color }]}>
          {strength.label}
        </Text>
      </View>

      {/* Feedback */}
      {strength.feedback.length > 0 && (
        <View style={styles.feedbackContainer}>
          {strength.feedback.map((item, index) => (
            <View key={index} style={styles.feedbackItem}>
              <Text style={styles.feedbackBullet}>•</Text>
              <Text style={styles.feedbackText}>{item}</Text>
            </View>
          ))}
        </View>
      )}

      {/* Requirements Checklist */}
      <View style={styles.requirementsContainer}>
        <RequirementItem
          text={`At least ${PASSWORD_REQUIREMENTS.minLength} characters`}
          met={password.length >= PASSWORD_REQUIREMENTS.minLength}
        />
        <RequirementItem
          text="Uppercase letter (A-Z)"
          met={/[A-Z]/.test(password)}
        />
        <RequirementItem
          text="Lowercase letter (a-z)"
          met={/[a-z]/.test(password)}
        />
        <RequirementItem
          text="Number (0-9)"
          met={/\d/.test(password)}
        />
        <RequirementItem
          text="Special character (!@#$...)"
          met={/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)}
        />
      </View>
    </View>
  );
};

interface RequirementItemProps {
  text: string;
  met: boolean;
}

const RequirementItem: React.FC<RequirementItemProps> = ({ text, met }) => (
  <View style={styles.requirementItem}>
    <Text style={[styles.requirementIcon, { color: met ? '#22C55E' : '#9CA3AF' }]}>
      {met ? '✓' : '○'}
    </Text>
    <Text style={[styles.requirementText, { color: met ? '#22C55E' : '#6B7280' }]}>
      {text}
    </Text>
  </View>
);

const styles = StyleSheet.create({
  container: {
    marginVertical: 8,
  },
  progressContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  progressBackground: {
    flex: 1,
    height: 6,
    backgroundColor: '#E5E7EB',
    borderRadius: 3,
    overflow: 'hidden',
    marginRight: 12,
  },
  progressBar: {
    height: '100%',
    borderRadius: 3,
  },
  progressGradient: {
    flex: 1,
    borderRadius: 3,
  },
  strengthLabel: {
    fontSize: 14,
    fontWeight: '600',
    minWidth: 60,
    textAlign: 'right',
  },
  feedbackContainer: {
    marginBottom: 12,
  },
  feedbackItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 4,
  },
  feedbackBullet: {
    color: '#F59E0B',
    fontSize: 14,
    marginRight: 8,
    marginTop: 1,
  },
  feedbackText: {
    flex: 1,
    fontSize: 13,
    color: '#6B7280',
    lineHeight: 18,
  },
  requirementsContainer: {
    backgroundColor: '#F9FAFB',
    borderRadius: 8,
    padding: 12,
  },
  requirementItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 6,
  },
  requirementIcon: {
    fontSize: 14,
    fontWeight: 'bold',
    marginRight: 8,
    width: 16,
    textAlign: 'center',
  },
  requirementText: {
    flex: 1,
    fontSize: 13,
    lineHeight: 18,
  },
});
