import { supabase } from '../lib/supabase';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Platform } from 'react-native';
import Constants from 'expo-constants';

export interface FeatureFlag {
  id: string;
  flag_key: string;
  flag_name: string;
  description?: string;
  flag_type: 'boolean' | 'string' | 'number' | 'json';
  default_value: any;
  is_enabled: boolean;
  rollout_percentage: number;
  environment: 'development' | 'staging' | 'production';
  platform: 'all' | 'ios' | 'android' | 'web';
  targeting_rules: any;
  user_segments: string[];
  status: 'draft' | 'active' | 'archived' | 'deprecated';
  start_date?: string;
  end_date?: string;
  tags: string[];
  owner_id?: string;
  team?: string;
}

export interface ABExperiment {
  id: string;
  experiment_key: string;
  experiment_name: string;
  description?: string;
  hypothesis?: string;
  experiment_type: 'ab_test' | 'multivariate' | 'feature_flag';
  status: 'draft' | 'running' | 'paused' | 'completed' | 'archived';
  traffic_allocation: number;
  targeting_rules: any;
  audience_segments: string[];
  environment: string;
  platform: string;
  start_date?: string;
  end_date?: string;
  primary_metric?: string;
  secondary_metrics: string[];
  conversion_goals: any[];
  results: any;
  winner_variant?: string;
  statistical_significance?: number;
}

export interface ExperimentVariant {
  id: string;
  experiment_id: string;
  variant_key: string;
  variant_name: string;
  description?: string;
  is_control: boolean;
  traffic_weight: number;
  parameters: any;
  feature_flags: any;
  exposure_count: number;
  conversion_count: number;
  conversion_rate: number;
}

export interface UserContext {
  user_id: string;
  email?: string;
  subscription_tier?: string;
  registration_date?: string;
  location?: {
    country?: string;
    region?: string;
    city?: string;
  };
  device?: {
    platform: string;
    os_version?: string;
    app_version?: string;
  };
  behavior?: {
    total_identifications?: number;
    days_active?: number;
    last_active?: string;
  };
  custom_attributes?: { [key: string]: any };
}

export interface FlagEvaluation {
  flag_key: string;
  value: any;
  reason: 'default' | 'rollout' | 'targeting' | 'override' | 'experiment';
  variant_key?: string;
  experiment_id?: string;
}

export class FeatureFlagService {
  private static instance: FeatureFlagService;
  private flagCache: Map<string, FeatureFlag> = new Map();
  private experimentCache: Map<string, ABExperiment> = new Map();
  private userContext: UserContext | null = null;
  private cacheExpiry: number = 0;
  private readonly CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

  static getInstance(): FeatureFlagService {
    if (!FeatureFlagService.instance) {
      FeatureFlagService.instance = new FeatureFlagService();
    }
    return FeatureFlagService.instance;
  }

  /**
   * Initialize the feature flag service
   */
  async initialize(): Promise<void> {
    try {
      await this.loadUserContext();
      await this.loadFeatureFlags();
      await this.loadExperiments();
      console.log('FeatureFlagService initialized successfully');
    } catch (error) {
      console.error('Error initializing FeatureFlagService:', error);
    }
  }

  /**
   * Get a feature flag value
   */
  async getFlag(flagKey: string, defaultValue?: any): Promise<any> {
    try {
      const evaluation = await this.evaluateFlag(flagKey, defaultValue);

      // Track flag evaluation
      await this.trackFlagEvent('flag_evaluation', flagKey, evaluation.value);

      return evaluation.value;
    } catch (error) {
      console.error(`Error getting flag ${flagKey}:`, error);
      return defaultValue;
    }
  }

  /**
   * Check if a feature flag is enabled (boolean flags)
   */
  async isEnabled(flagKey: string): Promise<boolean> {
    const value = await this.getFlag(flagKey, false);
    return Boolean(value);
  }

  /**
   * Get a string feature flag value
   */
  async getString(flagKey: string, defaultValue: string = ''): Promise<string> {
    const value = await this.getFlag(flagKey, defaultValue);
    return String(value);
  }

  /**
   * Get a number feature flag value
   */
  async getNumber(flagKey: string, defaultValue: number = 0): Promise<number> {
    const value = await this.getFlag(flagKey, defaultValue);
    return Number(value) || defaultValue;
  }

  /**
   * Get a JSON feature flag value
   */
  async getJSON(flagKey: string, defaultValue: any = {}): Promise<any> {
    const value = await this.getFlag(flagKey, defaultValue);
    try {
      return typeof value === 'string' ? JSON.parse(value) : value;
    } catch {
      return defaultValue;
    }
  }

  /**
   * Evaluate a feature flag with full context
   */
  async evaluateFlag(flagKey: string, defaultValue?: any): Promise<FlagEvaluation> {
    try {
      // Check cache first
      if (this.isCacheValid()) {
        const cachedFlag = this.flagCache.get(flagKey);
        if (cachedFlag) {
          return this.evaluateFlagWithRules(cachedFlag, defaultValue);
        }
      }

      // Load fresh flags if cache is stale
      await this.loadFeatureFlags();

      const flag = this.flagCache.get(flagKey);
      if (!flag) {
        return {
          flag_key: flagKey,
          value: defaultValue,
          reason: 'default',
        };
      }

      return this.evaluateFlagWithRules(flag, defaultValue);
    } catch (error) {
      console.error(`Error evaluating flag ${flagKey}:`, error);
      return {
        flag_key: flagKey,
        value: defaultValue,
        reason: 'default',
      };
    }
  }

  /**
   * Evaluate multiple flags at once
   */
  async evaluateFlags(flagNames: string[]): Promise<{ [key: string]: any }> {
    const results: { [key: string]: any } = {};
    
    await Promise.all(
      flagNames.map(async (flagName) => {
        results[flagName] = await this.evaluateFlag(flagName);
      })
    );

    return results;
  }

  /**
   * Check if a feature is enabled (boolean flags only)
   */
  async isFeatureEnabled(flagName: string): Promise<boolean> {
    const value = await this.evaluateFlag(flagName, false);
    return Boolean(value);
  }

  /**
   * Get feature flag configuration value (string/number/json flags)
   */
  async getFeatureConfig<T = any>(flagName: string, defaultValue: T): Promise<T> {
    return await this.evaluateFlag(flagName, defaultValue);
  }

  /**
   * Track conversion for A/B test
   */
  async trackConversion(flagName: string, conversionValue?: number): Promise<void> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return;

      // Get user's assignment for this flag
      const { data: assignment } = await supabase
        .from('user_feature_assignments')
        .select('*')
        .eq('user_id', user.id)
        .eq('flag_id', flagName)
        .single();

      if (assignment && !assignment.has_converted) {
        // Update assignment with conversion
        await supabase
          .from('user_feature_assignments')
          .update({
            has_converted: true,
            converted_at: new Date().toISOString(),
            conversion_value: conversionValue || 0
          })
          .eq('id', assignment.id);

        // Update variant conversion count
        if (assignment.variant_id) {
          await supabase.rpc('increment_variant_conversions', {
            variant_uuid: assignment.variant_id
          });
        }
      }
    } catch (error) {
      console.error('Error tracking conversion:', error);
    }
  }

  /**
   * Override a flag value for the current user (for testing)
   */
  async overrideFlag(flagName: string, value: any): Promise<void> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return;

      const flag = await this.getFlag(flagName);
      if (!flag) return;

      await supabase
        .from('user_feature_assignments')
        .upsert({
          user_id: user.id,
          flag_id: flag.id,
          assigned_value: value,
          assignment_reason: 'override',
          ...this.evaluationContext
        });

      // Clear cache
      this.cache.delete(`flag_${flagName}`);
    } catch (error) {
      console.error('Error overriding flag:', error);
    }
  }

  /**
   * Clear flag override for the current user
   */
  async clearOverride(flagName: string): Promise<void> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return;

      const flag = await this.getFlag(flagName);
      if (!flag) return;

      await supabase
        .from('user_feature_assignments')
        .delete()
        .eq('user_id', user.id)
        .eq('flag_id', flag.id)
        .eq('assignment_reason', 'override');

      // Clear cache
      this.cache.delete(`flag_${flagName}`);
    } catch (error) {
      console.error('Error clearing flag override:', error);
    }
  }

  /**
   * Get all active flags for the current user
   */
  async getAllFlags(): Promise<{ [key: string]: any }> {
    try {
      const { data: flags } = await supabase
        .from('feature_flags')
        .select('name')
        .eq('is_enabled', true)
        .eq('is_archived', false);

      if (!flags) return {};

      const flagNames = flags.map(f => f.name);
      return await this.evaluateFlags(flagNames);
    } catch (error) {
      console.error('Error getting all flags:', error);
      return {};
    }
  }

  /**
   * Refresh cache for all flags
   */
  async refreshCache(): Promise<void> {
    this.cache.clear();
    this.cacheExpiry.clear();
    await this.preloadCriticalFlags();
  }

  // Private methods

  private async setupEvaluationContext(): Promise<void> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      
      this.evaluationContext = {
        userId: user?.id,
        deviceType: Platform.OS,
        appVersion: '1.0.0', // This would come from your app config
      };

      // Get user segment if available
      if (user) {
        const { data: profile } = await supabase
          .from('user_profiles')
          .select('subscription_tier')
          .eq('id', user.id)
          .single();

        if (profile) {
          this.evaluationContext.userSegment = profile.subscription_tier || 'free';
        }
      }
    } catch (error) {
      console.error('Error setting up evaluation context:', error);
    }
  }

  private async preloadCriticalFlags(): Promise<void> {
    const criticalFlags = [
      'new_identification_ui',
      'premium_features_v2',
      'offline_mode',
      'ai_model_v3'
    ];

    await Promise.all(
      criticalFlags.map(flagName => this.evaluateFlag(flagName))
    );
  }

  private setupCacheRefresh(): void {
    // Refresh cache every 5 minutes
    setInterval(() => {
      this.refreshCache();
    }, 5 * 60 * 1000);
  }

  private async getFlag(flagName: string): Promise<FeatureFlag | null> {
    try {
      const { data, error } = await supabase
        .from('feature_flags')
        .select('*')
        .eq('name', flagName)
        .single();

      if (error || !data) return null;
      return data;
    } catch (error) {
      console.error(`Error getting flag ${flagName}:`, error);
      return null;
    }
  }

  private isWithinDateRange(flag: FeatureFlag): boolean {
    const now = new Date();
    
    if (flag.start_date && new Date(flag.start_date) > now) {
      return false;
    }
    
    if (flag.end_date && new Date(flag.end_date) < now) {
      return false;
    }
    
    return true;
  }

  private async evaluateStandardFlag(flag: FeatureFlag): Promise<{ value: any; reason: string }> {
    // Check for user override first
    const override = await this.getUserOverride(flag.id);
    if (override) {
      return { value: override.assigned_value, reason: 'override' };
    }

    // Check targeting rules
    if (!this.matchesTargetAudience(flag.target_audience)) {
      return { value: flag.default_value, reason: 'targeting_mismatch' };
    }

    // Check rollout percentage
    if (!this.isInRollout(flag.rollout_percentage)) {
      return { value: flag.default_value, reason: 'rollout_excluded' };
    }

    return { value: flag.default_value, reason: 'rollout_included' };
  }

  private async evaluateABTest(flag: FeatureFlag): Promise<{ value: any; reason: string }> {
    // Check for existing assignment
    const existingAssignment = await this.getUserAssignment(flag.id);
    if (existingAssignment) {
      return { value: existingAssignment.assigned_value, reason: 'existing_assignment' };
    }

    // Get variants
    const { data: variants } = await supabase
      .from('feature_flag_variants')
      .select('*')
      .eq('flag_id', flag.id)
      .eq('is_active', true)
      .order('weight', { ascending: false });

    if (!variants || variants.length === 0) {
      return { value: flag.default_value, reason: 'no_variants' };
    }

    // Assign user to variant based on weights
    const selectedVariant = this.selectVariantByWeight(variants);
    
    // Save assignment
    await this.saveUserAssignment(flag.id, selectedVariant);

    return { value: selectedVariant.value, reason: 'ab_test_assignment' };
  }

  private selectVariantByWeight(variants: FeatureFlagVariant[]): FeatureFlagVariant {
    const totalWeight = variants.reduce((sum, v) => sum + v.weight, 0);
    const random = Math.random() * totalWeight;
    
    let currentWeight = 0;
    for (const variant of variants) {
      currentWeight += variant.weight;
      if (random <= currentWeight) {
        return variant;
      }
    }
    
    return variants[0]; // Fallback
  }

  private async getUserOverride(flagId: string): Promise<UserAssignment | null> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return null;

      const { data } = await supabase
        .from('user_feature_assignments')
        .select('*')
        .eq('user_id', user.id)
        .eq('flag_id', flagId)
        .eq('assignment_reason', 'override')
        .single();

      return data;
    } catch (error) {
      return null;
    }
  }

  private async getUserAssignment(flagId: string): Promise<UserAssignment | null> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return null;

      const { data } = await supabase
        .from('user_feature_assignments')
        .select('*')
        .eq('user_id', user.id)
        .eq('flag_id', flagId)
        .single();

      return data;
    } catch (error) {
      return null;
    }
  }

  private async saveUserAssignment(flagId: string, variant: FeatureFlagVariant): Promise<void> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return;

      await supabase
        .from('user_feature_assignments')
        .upsert({
          user_id: user.id,
          flag_id: flagId,
          variant_id: variant.id,
          assigned_value: variant.value,
          assignment_reason: 'ab_test',
          ...this.evaluationContext
        });
    } catch (error) {
      console.error('Error saving user assignment:', error);
    }
  }

  private matchesTargetAudience(targetAudience: any): boolean {
    if (!targetAudience || Object.keys(targetAudience).length === 0) {
      return true; // No targeting rules means everyone matches
    }

    // Implement targeting logic based on your needs
    // Example: check user segment, device type, etc.
    return true;
  }

  private isInRollout(percentage: number): boolean {
    if (percentage >= 100) return true;
    if (percentage <= 0) return false;

    // Use consistent hash based on user ID for stable rollout
    const { userId } = this.evaluationContext;
    if (!userId) return Math.random() * 100 < percentage;

    // Simple hash function for consistent assignment
    let hash = 0;
    for (let i = 0; i < userId.length; i++) {
      const char = userId.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    
    const userPercentage = Math.abs(hash) % 100;
    return userPercentage < percentage;
  }

  private isCacheValid(key: string): boolean {
    const expiry = this.cacheExpiry.get(key);
    return expiry ? Date.now() < expiry : false;
  }

  private async logEvaluation(
    flagName: string,
    value: any,
    reason: string,
    evaluationTimeMs: number
  ): Promise<void> {
    try {
      // Only log in production for performance
      if (process.env.NODE_ENV !== 'production') return;

      const { data: { user } } = await supabase.auth.getUser();
      
      await supabase
        .from('feature_flag_evaluations')
        .insert({
          flag_id: flagName,
          user_id: user?.id,
          evaluated_value: value,
          evaluation_reason: reason,
          user_context: this.evaluationContext,
          evaluation_time_ms: evaluationTimeMs
        });
    } catch (error) {
      // Silently fail logging to not impact user experience
      console.debug('Error logging flag evaluation:', error);
    }
  }
}
