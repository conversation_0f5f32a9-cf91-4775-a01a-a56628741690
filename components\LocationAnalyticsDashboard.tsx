import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Dimensions,
  RefreshControl,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import {
  MapPin,
  TrendingUp,
  TrendingDown,
  BarChart3,
  Globe,
  Navigation,
  Clock,
  Target,
  Activity,
  Mountain,
  Compass,
  Calendar,
  Users,
  Eye,
  Camera,
} from 'lucide-react-native';
import { supabase } from '../lib/supabase';

const { width } = Dimensions.get('window');

interface LocationAnalytics {
  date: string;
  total_locations_recorded: number;
  unique_locations_visited: number;
  total_distance_traveled: number;
  average_accuracy: number;
  identifications_with_location: number;
  discoveries_made: number;
  public_locations_shared: number;
  countries_visited: string[];
  states_provinces_visited: string[];
  cities_visited: string[];
  habitat_types_explored: string[];
  elevation_range: { min: number; max: number };
  climate_zones_visited: string[];
}

interface LocationStats {
  totalLocations: number;
  uniqueLocations: number;
  totalDistance: number;
  averageAccuracy: number;
  countriesCount: number;
  citiesCount: number;
  habitatTypesCount: number;
  identificationLocations: number;
  publicShares: number;
  discoveries: number;
}

export const LocationAnalyticsDashboard: React.FC = () => {
  const [analytics, setAnalytics] = useState<LocationAnalytics[]>([]);
  const [stats, setStats] = useState<LocationStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [timeRange, setTimeRange] = useState<'week' | 'month' | 'year'>('month');
  const [selectedMetric, setSelectedMetric] = useState<'distance' | 'locations' | 'accuracy'>('distance');

  useEffect(() => {
    loadLocationAnalytics();
  }, [timeRange]);

  const loadLocationAnalytics = async () => {
    try {
      setLoading(true);
      
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      // Calculate date range
      const endDate = new Date();
      const startDate = new Date();
      
      switch (timeRange) {
        case 'week':
          startDate.setDate(endDate.getDate() - 7);
          break;
        case 'month':
          startDate.setMonth(endDate.getMonth() - 1);
          break;
        case 'year':
          startDate.setFullYear(endDate.getFullYear() - 1);
          break;
      }

      // Load analytics data
      const { data: analyticsData, error: analyticsError } = await supabase
        .from('location_analytics')
        .select('*')
        .eq('user_id', user.id)
        .gte('date', startDate.toISOString().split('T')[0])
        .lte('date', endDate.toISOString().split('T')[0])
        .order('date', { ascending: true });

      if (analyticsError) throw analyticsError;

      setAnalytics(analyticsData || []);

      // Calculate aggregate stats
      if (analyticsData && analyticsData.length > 0) {
        const aggregatedStats = calculateAggregatedStats(analyticsData);
        setStats(aggregatedStats);
      }

    } catch (error) {
      console.error('Error loading location analytics:', error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const calculateAggregatedStats = (data: LocationAnalytics[]): LocationStats => {
    const totalLocations = data.reduce((sum, day) => sum + day.total_locations_recorded, 0);
    const uniqueLocations = data.reduce((sum, day) => sum + day.unique_locations_visited, 0);
    const totalDistance = data.reduce((sum, day) => sum + day.total_distance_traveled, 0);
    const averageAccuracy = data.reduce((sum, day) => sum + (day.average_accuracy || 0), 0) / data.length;
    
    const allCountries = new Set<string>();
    const allCities = new Set<string>();
    const allHabitats = new Set<string>();
    
    data.forEach(day => {
      day.countries_visited?.forEach(country => allCountries.add(country));
      day.cities_visited?.forEach(city => allCities.add(city));
      day.habitat_types_explored?.forEach(habitat => allHabitats.add(habitat));
    });

    const identificationLocations = data.reduce((sum, day) => sum + day.identifications_with_location, 0);
    const publicShares = data.reduce((sum, day) => sum + day.public_locations_shared, 0);
    const discoveries = data.reduce((sum, day) => sum + day.discoveries_made, 0);

    return {
      totalLocations,
      uniqueLocations,
      totalDistance,
      averageAccuracy,
      countriesCount: allCountries.size,
      citiesCount: allCities.size,
      habitatTypesCount: allHabitats.size,
      identificationLocations,
      publicShares,
      discoveries,
    };
  };

  const onRefresh = () => {
    setRefreshing(true);
    loadLocationAnalytics();
  };

  const getMetricData = () => {
    switch (selectedMetric) {
      case 'distance':
        return analytics.map(day => day.total_distance_traveled);
      case 'locations':
        return analytics.map(day => day.total_locations_recorded);
      case 'accuracy':
        return analytics.map(day => day.average_accuracy || 0);
      default:
        return [];
    }
  };

  const getMetricLabel = () => {
    switch (selectedMetric) {
      case 'distance':
        return 'Distance (km)';
      case 'locations':
        return 'Locations';
      case 'accuracy':
        return 'Accuracy (m)';
      default:
        return '';
    }
  };

  const renderStatsGrid = () => (
    <View style={styles.statsGrid}>
      <View style={styles.statCard}>
        <MapPin size={24} color="#3B82F6" />
        <Text style={styles.statValue}>{stats?.totalLocations || 0}</Text>
        <Text style={styles.statLabel}>Total Locations</Text>
      </View>
      
      <View style={styles.statCard}>
        <Navigation size={24} color="#10B981" />
        <Text style={styles.statValue}>{stats?.totalDistance.toFixed(1) || '0.0'}</Text>
        <Text style={styles.statLabel}>Distance (km)</Text>
      </View>
      
      <View style={styles.statCard}>
        <Globe size={24} color="#F59E0B" />
        <Text style={styles.statValue}>{stats?.countriesCount || 0}</Text>
        <Text style={styles.statLabel}>Countries</Text>
      </View>
      
      <View style={styles.statCard}>
        <Target size={24} color="#EF4444" />
        <Text style={styles.statValue}>{stats?.averageAccuracy.toFixed(0) || '0'}</Text>
        <Text style={styles.statLabel}>Avg Accuracy (m)</Text>
      </View>
    </View>
  );

  const renderActivityStats = () => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>Activity Overview</Text>
      
      <View style={styles.activityGrid}>
        <View style={styles.activityCard}>
          <Camera size={20} color="#3B82F6" />
          <Text style={styles.activityValue}>{stats?.identificationLocations || 0}</Text>
          <Text style={styles.activityLabel}>ID Locations</Text>
        </View>
        
        <View style={styles.activityCard}>
          <Eye size={20} color="#10B981" />
          <Text style={styles.activityValue}>{stats?.discoveries || 0}</Text>
          <Text style={styles.activityLabel}>Discoveries</Text>
        </View>
        
        <View style={styles.activityCard}>
          <Users size={20} color="#F59E0B" />
          <Text style={styles.activityValue}>{stats?.publicShares || 0}</Text>
          <Text style={styles.activityLabel}>Public Shares</Text>
        </View>
        
        <View style={styles.activityCard}>
          <Mountain size={20} color="#8B5CF6" />
          <Text style={styles.activityValue}>{stats?.habitatTypesCount || 0}</Text>
          <Text style={styles.activityLabel}>Habitats</Text>
        </View>
      </View>
    </View>
  );

  const renderChart = () => {
    const data = getMetricData();
    const maxValue = Math.max(...data, 1);
    
    return (
      <View style={styles.section}>
        <View style={styles.chartHeader}>
          <Text style={styles.sectionTitle}>Trends</Text>
          <View style={styles.metricSelector}>
            {[
              { key: 'distance', label: 'Distance', icon: Navigation },
              { key: 'locations', label: 'Locations', icon: MapPin },
              { key: 'accuracy', label: 'Accuracy', icon: Target },
            ].map(({ key, label, icon: Icon }) => (
              <TouchableOpacity
                key={key}
                style={[
                  styles.metricButton,
                  selectedMetric === key && styles.activeMetricButton
                ]}
                onPress={() => setSelectedMetric(key as any)}
              >
                <Icon size={16} color={selectedMetric === key ? '#3B82F6' : '#6B7280'} />
                <Text style={[
                  styles.metricButtonText,
                  selectedMetric === key && styles.activeMetricButtonText
                ]}>
                  {label}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>
        
        <View style={styles.chart}>
          <View style={styles.chartYAxis}>
            <Text style={styles.chartAxisLabel}>{maxValue.toFixed(0)}</Text>
            <Text style={styles.chartAxisLabel}>{(maxValue * 0.5).toFixed(0)}</Text>
            <Text style={styles.chartAxisLabel}>0</Text>
          </View>
          
          <View style={styles.chartArea}>
            <View style={styles.chartBars}>
              {data.map((value, index) => (
                <View key={index} style={styles.chartBarContainer}>
                  <View
                    style={[
                      styles.chartBar,
                      {
                        height: `${(value / maxValue) * 100}%`,
                        backgroundColor: selectedMetric === 'distance' ? '#3B82F6' :
                                       selectedMetric === 'locations' ? '#10B981' : '#F59E0B',
                      }
                    ]}
                  />
                </View>
              ))}
            </View>
            
            <View style={styles.chartXAxis}>
              {analytics.slice(0, 7).map((day, index) => (
                <Text key={index} style={styles.chartXAxisLabel}>
                  {new Date(day.date).getDate()}
                </Text>
              ))}
            </View>
          </View>
        </View>
        
        <Text style={styles.chartLabel}>{getMetricLabel()}</Text>
      </View>
    );
  };

  const renderGeographicStats = () => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>Geographic Distribution</Text>
      
      <View style={styles.geographicGrid}>
        <View style={styles.geographicCard}>
          <Globe size={24} color="#3B82F6" />
          <Text style={styles.geographicValue}>{stats?.countriesCount || 0}</Text>
          <Text style={styles.geographicLabel}>Countries Visited</Text>
        </View>
        
        <View style={styles.geographicCard}>
          <MapPin size={24} color="#10B981" />
          <Text style={styles.geographicValue}>{stats?.citiesCount || 0}</Text>
          <Text style={styles.geographicLabel}>Cities Explored</Text>
        </View>
        
        <View style={styles.geographicCard}>
          <Mountain size={24} color="#F59E0B" />
          <Text style={styles.geographicValue}>{stats?.habitatTypesCount || 0}</Text>
          <Text style={styles.geographicLabel}>Habitat Types</Text>
        </View>
        
        <View style={styles.geographicCard}>
          <Compass size={24} color="#8B5CF6" />
          <Text style={styles.geographicValue}>{stats?.uniqueLocations || 0}</Text>
          <Text style={styles.geographicLabel}>Unique Locations</Text>
        </View>
      </View>
    </View>
  );

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <Activity size={32} color="#3B82F6" />
        <Text style={styles.loadingText}>Loading location analytics...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Header */}
      <LinearGradient
        colors={['#3B82F6', '#1D4ED8']}
        style={styles.header}
      >
        <BarChart3 size={32} color="white" />
        <Text style={styles.headerTitle}>Location Analytics</Text>
        <Text style={styles.headerSubtitle}>
          Track your exploration journey and discoveries
        </Text>
      </LinearGradient>

      {/* Time Range Selector */}
      <View style={styles.timeRangeContainer}>
        {[
          { key: 'week', label: 'Week' },
          { key: 'month', label: 'Month' },
          { key: 'year', label: 'Year' },
        ].map(({ key, label }) => (
          <TouchableOpacity
            key={key}
            style={[
              styles.timeRangeButton,
              timeRange === key && styles.activeTimeRangeButton
            ]}
            onPress={() => setTimeRange(key as any)}
          >
            <Text style={[
              styles.timeRangeButtonText,
              timeRange === key && styles.activeTimeRangeButtonText
            ]}>
              {label}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      {/* Content */}
      <ScrollView
        style={styles.content}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {/* Main Stats */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Overview</Text>
          {renderStatsGrid()}
        </View>

        {/* Activity Stats */}
        {renderActivityStats()}

        {/* Chart */}
        {renderChart()}

        {/* Geographic Stats */}
        {renderGeographicStats()}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#F9FAFB',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#6B7280',
  },
  header: {
    padding: 24,
    paddingTop: 60,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: 'white',
    marginTop: 8,
  },
  headerSubtitle: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.8)',
    marginTop: 4,
    textAlign: 'center',
  },
  timeRangeContainer: {
    flexDirection: 'row',
    backgroundColor: 'white',
    margin: 20,
    borderRadius: 12,
    padding: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  timeRangeButton: {
    flex: 1,
    paddingVertical: 8,
    alignItems: 'center',
    borderRadius: 8,
  },
  activeTimeRangeButton: {
    backgroundColor: '#3B82F6',
  },
  timeRangeButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#6B7280',
  },
  activeTimeRangeButtonText: {
    color: 'white',
  },
  content: {
    flex: 1,
  },
  section: {
    backgroundColor: 'white',
    margin: 20,
    marginTop: 0,
    borderRadius: 12,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 16,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  statCard: {
    flex: 1,
    minWidth: '45%',
    backgroundColor: '#F9FAFB',
    borderRadius: 8,
    padding: 16,
    alignItems: 'center',
  },
  statValue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#111827',
    marginTop: 8,
  },
  statLabel: {
    fontSize: 12,
    color: '#6B7280',
    marginTop: 4,
    textAlign: 'center',
  },
  activityGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  activityCard: {
    flex: 1,
    minWidth: '22%',
    backgroundColor: '#F9FAFB',
    borderRadius: 8,
    padding: 12,
    alignItems: 'center',
  },
  activityValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#111827',
    marginTop: 6,
  },
  activityLabel: {
    fontSize: 10,
    color: '#6B7280',
    marginTop: 4,
    textAlign: 'center',
  },
  chartHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  metricSelector: {
    flexDirection: 'row',
    gap: 4,
  },
  metricButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6,
    gap: 4,
  },
  activeMetricButton: {
    backgroundColor: '#EBF8FF',
  },
  metricButtonText: {
    fontSize: 12,
    color: '#6B7280',
  },
  activeMetricButtonText: {
    color: '#3B82F6',
  },
  chart: {
    flexDirection: 'row',
    height: 120,
    marginBottom: 8,
  },
  chartYAxis: {
    width: 30,
    justifyContent: 'space-between',
    alignItems: 'flex-end',
    paddingRight: 8,
  },
  chartAxisLabel: {
    fontSize: 10,
    color: '#6B7280',
  },
  chartArea: {
    flex: 1,
  },
  chartBars: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'flex-end',
    gap: 2,
  },
  chartBarContainer: {
    flex: 1,
    height: '100%',
    justifyContent: 'flex-end',
  },
  chartBar: {
    backgroundColor: '#3B82F6',
    borderRadius: 2,
    minHeight: 2,
  },
  chartXAxis: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginTop: 8,
  },
  chartXAxisLabel: {
    fontSize: 10,
    color: '#6B7280',
  },
  chartLabel: {
    fontSize: 12,
    color: '#6B7280',
    textAlign: 'center',
  },
  geographicGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  geographicCard: {
    flex: 1,
    minWidth: '45%',
    backgroundColor: '#F9FAFB',
    borderRadius: 8,
    padding: 16,
    alignItems: 'center',
  },
  geographicValue: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#111827',
    marginTop: 8,
  },
  geographicLabel: {
    fontSize: 12,
    color: '#6B7280',
    marginTop: 4,
    textAlign: 'center',
  },
});

export default LocationAnalyticsDashboard;
