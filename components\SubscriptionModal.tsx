import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  SafeAreaView,
  ScrollView,
  Dimensions,
  Platform,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import {
  X,
  Crown,
  Check,
  Zap,
  Camera,
  BookOpen,
  Users,
  Shield,
  Star,
  Sparkles,
  Lock,
  AlertTriangle,
} from 'lucide-react-native';
import { useSubscription, SubscriptionTier } from './SubscriptionContext';
import { PaymentService, SubscriptionPlan } from '../services/PaymentService';
import { FraudDetectionService } from '../services/FraudDetectionService';
import { SubscriptionManagementService } from '../services/SubscriptionManagementService';

const { width } = Dimensions.get('window');

interface SubscriptionModalProps {
  visible: boolean;
  onClose: () => void;
  onSubscribe?: (planId: string, billingPeriod: 'monthly' | 'yearly') => void;
}

export const SubscriptionModal: React.FC<SubscriptionModalProps> = ({
  visible,
  onClose,
  onSubscribe,
}) => {
  const [plans, setPlans] = useState<SubscriptionPlan[]>([]);
  const [loading, setLoading] = useState(true);
  const [subscribing, setSubscribing] = useState<string | null>(null);
  const [billingPeriod, setBillingPeriod] = useState<'monthly' | 'yearly'>('monthly');
  const [securityCheck, setSecurityCheck] = useState<boolean>(false);
  const [fraudStats, setFraudStats] = useState<any>(null);
  const { subscription } = useSubscription();

  useEffect(() => {
    if (visible) {
      loadPlans();
      checkSecurity();
    }
  }, [visible]);

  const loadPlans = async () => {
    try {
      setLoading(true);
      const paymentService = new PaymentService();
      const availablePlans = await paymentService.getSubscriptionPlans();
      setPlans(availablePlans);
    } catch (error) {
      console.error('Error loading plans:', error);
      Alert.alert('Error', 'Failed to load subscription plans');
    } finally {
      setLoading(false);
    }
  };

  const checkSecurity = async () => {
    try {
      const isAllowed = await FraudDetectionService.isPaymentAllowed();
      setSecurityCheck(isAllowed);

      const stats = await FraudDetectionService.getFraudStats();
      setFraudStats(stats);
    } catch (error) {
      console.error('Error checking security:', error);
      setSecurityCheck(true); // Allow on error
    }
  };

  const handleSubscribe = async (plan: SubscriptionPlan) => {
    if (!securityCheck) {
      Alert.alert(
        'Security Check Failed',
        'Payment is temporarily blocked for security reasons. Please try again later or contact support.',
        [{ text: 'OK' }]
      );
      return;
    }

    try {
      setSubscribing(plan.id);

      // Additional security validation for high-value plans
      if (plan.price_monthly > 10 || plan.price_yearly > 100) {
        const validation = await FraudDetectionService.validatePayment({
          paymentMethodId: 'preview', // This would be actual payment method in real implementation
          amount: billingPeriod === 'yearly' ? plan.price_yearly : plan.price_monthly,
          currency: 'USD'
        });

        if (validation.isBlocked) {
          Alert.alert('Payment Blocked', validation.message || 'Payment blocked for security reasons');
          return;
        }

        if (validation.requiresVerification) {
          const confirmed = await new Promise<boolean>((resolve) => {
            Alert.alert(
              'Additional Verification Required',
              'This payment requires additional verification for security. Continue?',
              [
                { text: 'Cancel', onPress: () => resolve(false) },
                { text: 'Continue', onPress: () => resolve(true) }
              ]
            );
          });

          if (!confirmed) return;
        }
      }

      const paymentService = new PaymentService();
      const { url } = await paymentService.createCheckoutSession(plan.id, billingPeriod);

      // In a real app, you'd open the Stripe checkout URL
      if (onSubscribe) {
        onSubscribe(plan.id, billingPeriod);
      }

      Alert.alert('Success', 'Redirecting to payment...');
      onClose();

    } catch (error: any) {
      console.error('Error subscribing:', error);
      Alert.alert('Error', error.message || 'Failed to start subscription process');
    } finally {
      setSubscribing(null);
    }
  };

  const formatPrice = (plan: SubscriptionPlan) => {
    const price = billingPeriod === 'yearly' ? plan.price_yearly : plan.price_monthly;
    const period = billingPeriod === 'yearly' ? 'year' : 'month';
    return `$${price.toFixed(2)}/${period}`;
  };

  const calculateSavings = (plan: SubscriptionPlan) => {
    const monthlyTotal = plan.price_monthly * 12;
    const yearlyPrice = plan.price_yearly;
    const savings = monthlyTotal - yearlyPrice;
    const percentage = (savings / monthlyTotal) * 100;
    return { amount: savings, percentage };
  };
  {
    tier: 'expert',
    name: 'Expert',
    price: '$9.99',
    period: 'per month',
    description: 'For professionals and educators',
    gradient: ['#8B5CF6', '#7C3AED'],
    icon: Crown,
    features: [
      'Everything in Pro',
      'Advanced AR features',
      'Collection analytics',
      'Community moderation',
      '30-day offline access',
      'Custom categories',
      'Bulk operations',
      'API access',
      'White-label options',
    ],
  },
];

interface SubscriptionModalProps {
  visible: boolean;
  onClose: () => void;
  initialTier?: SubscriptionTier;
}

export default function SubscriptionModal({
  visible,
  onClose,
  initialTier,
}: SubscriptionModalProps) {
  const { subscription, upgradeTier } = useSubscription();
  const [selectedTier, setSelectedTier] = useState<SubscriptionTier>(
    initialTier || subscription.tier
  );
  const [isUpgrading, setIsUpgrading] = useState(false);

  const handleUpgrade = async () => {
    if (selectedTier === subscription.tier) {
      onClose();
      return;
    }

    setIsUpgrading(true);
    try {
      await upgradeTier(selectedTier);
      onClose();
    } catch (error) {
      console.error('Failed to upgrade subscription:', error);
    } finally {
      setIsUpgrading(false);
    }
  };

  const renderPlan = (plan: SubscriptionPlan) => {
    const IconComponent = plan.icon;
    const isSelected = selectedTier === plan.tier;
    const isCurrent = subscription.tier === plan.tier;

    return (
      <TouchableOpacity
        key={plan.tier}
        style={[
          styles.planCard,
          isSelected && styles.planCardSelected,
          plan.popular && styles.planCardPopular,
        ]}
        onPress={() => setSelectedTier(plan.tier)}>
        {plan.popular && (
          <View style={styles.popularBadge}>
            <Star size={12} color="#FFFFFF" fill="#FFFFFF" />
            <Text style={styles.popularText}>Most Popular</Text>
          </View>
        )}

        <LinearGradient
          colors={isSelected ? plan.gradient : ['#FFFFFF', '#FFFFFF']}
          style={styles.planHeader}>
          <IconComponent
            size={32}
            color={isSelected ? '#FFFFFF' : plan.gradient[0]}
          />
          <Text
            style={[
              styles.planName,
              { color: isSelected ? '#FFFFFF' : '#111827' },
            ]}>
            {plan.name}
          </Text>
          {isCurrent && (
            <View style={styles.currentBadge}>
              <Text style={styles.currentText}>Current</Text>
            </View>
          )}
        </LinearGradient>

        <View style={styles.planContent}>
          <View style={styles.priceContainer}>
            <Text style={styles.price}>{plan.price}</Text>
            <Text style={styles.period}>{plan.period}</Text>
          </View>

          <Text style={styles.planDescription}>{plan.description}</Text>

          <View style={styles.featuresContainer}>
            {plan.features.map((feature, index) => (
              <View key={index} style={styles.featureItem}>
                <Check size={16} color="#22C55E" />
                <Text style={styles.featureText}>{feature}</Text>
              </View>
            ))}
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet">
      <SafeAreaView style={styles.container}>
        <LinearGradient
          colors={['#F8FAFC', '#F1F5F9']}
          style={styles.gradient}>
          {/* Header */}
          <View style={styles.header}>
            <TouchableOpacity style={styles.closeButton} onPress={onClose}>
              <X size={24} color="#6B7280" />
            </TouchableOpacity>
            <Text style={styles.headerTitle}>Choose Your Plan</Text>
            <View style={styles.headerSpacer} />
          </View>

          {/* Hero Section */}
          <View style={styles.heroSection}>
            <View style={styles.heroIcon}>
              <Sparkles size={40} color="#3B82F6" />
            </View>
            <Text style={styles.heroTitle}>Unlock the Full BioScan Experience</Text>
            <Text style={styles.heroSubtitle}>
              Get unlimited scans, advanced features, and join our community of nature enthusiasts
            </Text>
          </View>

          {/* Plans */}
          <ScrollView
            style={styles.scrollView}
            showsVerticalScrollIndicator={false}>
            <View style={styles.plansContainer}>
              {subscriptionPlans.map(renderPlan)}
            </View>

            {/* Benefits Section */}
            <View style={styles.benefitsSection}>
              <Text style={styles.benefitsTitle}>Why Upgrade?</Text>
              <View style={styles.benefitsGrid}>
                <View style={styles.benefitItem}>
                  <Camera size={24} color="#3B82F6" />
                  <Text style={styles.benefitText}>Unlimited Scanning</Text>
                </View>
                <View style={styles.benefitItem}>
                  <BookOpen size={24} color="#22C55E" />
                  <Text style={styles.benefitText}>Advanced Collection</Text>
                </View>
                <View style={styles.benefitItem}>
                  <Users size={24} color="#8B5CF6" />
                  <Text style={styles.benefitText}>Community Access</Text>
                </View>
                <View style={styles.benefitItem}>
                  <Shield size={24} color="#F59E0B" />
                  <Text style={styles.benefitText}>Priority Support</Text>
                </View>
              </View>
            </View>

            {/* Trust Indicators */}
            <View style={styles.trustSection}>
              <Text style={styles.trustTitle}>Trusted by Nature Lovers</Text>
              <View style={styles.trustStats}>
                <View style={styles.trustStat}>
                  <Text style={styles.trustNumber}>50K+</Text>
                  <Text style={styles.trustLabel}>Active Users</Text>
                </View>
                <View style={styles.trustStat}>
                  <Text style={styles.trustNumber}>1M+</Text>
                  <Text style={styles.trustLabel}>Species Identified</Text>
                </View>
                <View style={styles.trustStat}>
                  <Text style={styles.trustNumber}>4.9★</Text>
                  <Text style={styles.trustLabel}>App Store Rating</Text>
                </View>
              </View>
            </View>
          </ScrollView>

          {/* Action Button */}
          <View style={styles.actionContainer}>
            <TouchableOpacity
              style={[
                styles.upgradeButton,
                isUpgrading && styles.upgradeButtonDisabled,
              ]}
              onPress={handleUpgrade}
              disabled={isUpgrading}>
              <LinearGradient
                colors={
                  selectedTier === 'free'
                    ? ['#6B7280', '#4B5563']
                    : selectedTier === 'pro'
                    ? ['#3B82F6', '#1D4ED8']
                    : ['#8B5CF6', '#7C3AED']
                }
                style={styles.upgradeButtonGradient}>
                <Text style={styles.upgradeButtonText}>
                  {isUpgrading
                    ? 'Processing...'
                    : selectedTier === subscription.tier
                    ? 'Continue with Current Plan'
                    : selectedTier === 'free'
                    ? 'Downgrade to Free'
                    : `Upgrade to ${
                        subscriptionPlans.find(p => p.tier === selectedTier)?.name
                      }`}
                </Text>
              </LinearGradient>
            </TouchableOpacity>

            <Text style={styles.disclaimer}>
              Cancel anytime. No hidden fees. 7-day free trial for paid plans.
            </Text>
          </View>
        </LinearGradient>
      </SafeAreaView>
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  gradient: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  closeButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#F3F4F6',
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
  },
  headerSpacer: {
    width: 40,
  },
  heroSection: {
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 32,
  },
  heroIcon: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#EBF8FF',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 16,
  },
  heroTitle: {
    fontSize: 24,
    fontFamily: 'Inter-Bold',
    color: '#111827',
    textAlign: 'center',
    marginBottom: 8,
  },
  heroSubtitle: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    textAlign: 'center',
    lineHeight: 24,
  },
  scrollView: {
    flex: 1,
  },
  plansContainer: {
    paddingHorizontal: 20,
    gap: 16,
  },
  planCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    borderWidth: 2,
    borderColor: '#E5E7EB',
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  planCardSelected: {
    borderColor: '#3B82F6',
    shadowColor: '#3B82F6',
    shadowOpacity: 0.2,
  },
  planCardPopular: {
    position: 'relative',
  },
  popularBadge: {
    position: 'absolute',
    top: 12,
    right: 12,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F59E0B',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    gap: 4,
    zIndex: 1,
  },
  popularText: {
    fontSize: 10,
    fontFamily: 'Inter-Bold',
    color: '#FFFFFF',
  },
  planHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 20,
    gap: 12,
  },
  planName: {
    fontSize: 20,
    fontFamily: 'Inter-Bold',
    flex: 1,
  },
  currentBadge: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
  },
  currentText: {
    fontSize: 10,
    fontFamily: 'Inter-Bold',
    color: '#FFFFFF',
  },
  planContent: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  priceContainer: {
    flexDirection: 'row',
    alignItems: 'baseline',
    marginBottom: 8,
    gap: 4,
  },
  price: {
    fontSize: 32,
    fontFamily: 'Inter-Bold',
    color: '#111827',
  },
  period: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
  },
  planDescription: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    marginBottom: 16,
  },
  featuresContainer: {
    gap: 8,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  featureText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#374151',
    flex: 1,
  },
  benefitsSection: {
    paddingHorizontal: 20,
    paddingVertical: 32,
  },
  benefitsTitle: {
    fontSize: 20,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    textAlign: 'center',
    marginBottom: 20,
  },
  benefitsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    gap: 16,
  },
  benefitItem: {
    width: (width - 52) / 2,
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  benefitText: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    color: '#374151',
    textAlign: 'center',
    marginTop: 8,
  },
  trustSection: {
    paddingHorizontal: 20,
    paddingBottom: 32,
  },
  trustTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    textAlign: 'center',
    marginBottom: 16,
  },
  trustStats: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  trustStat: {
    alignItems: 'center',
  },
  trustNumber: {
    fontSize: 20,
    fontFamily: 'Inter-Bold',
    color: '#3B82F6',
    marginBottom: 4,
  },
  trustLabel: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    textAlign: 'center',
  },
  actionContainer: {
    paddingHorizontal: 20,
    paddingBottom: Platform.OS === 'ios' ? 40 : 20,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
  },
  upgradeButton: {
    borderRadius: 16,
    overflow: 'hidden',
    marginBottom: 12,
  },
  upgradeButtonDisabled: {
    opacity: 0.6,
  },
  upgradeButtonGradient: {
    paddingVertical: 16,
    paddingHorizontal: 24,
    alignItems: 'center',
  },
  upgradeButtonText: {
    fontSize: 16,
    fontFamily: 'Inter-Bold',
    color: '#FFFFFF',
  },
  disclaimer: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    textAlign: 'center',
    lineHeight: 16,
  },
});