import React, { useEffect, useState, useRef } from 'react';
import { AppState, AppStateStatus, Platform } from 'react-native';
import * as Notifications from 'expo-notifications';
import { PushNotificationService } from '../services/PushNotificationService';
import { supabase } from '../lib/supabase';

interface NotificationManagerProps {
  children: React.ReactNode;
  onNotificationReceived?: (notification: Notifications.Notification) => void;
  onNotificationTapped?: (response: Notifications.NotificationResponse) => void;
}

/**
 * NotificationManager - Handles all push notification logic for the app
 * This component should wrap your main app component to provide notification functionality
 */
export const NotificationManager: React.FC<NotificationManagerProps> = ({
  children,
  onNotificationReceived,
  onNotificationTapped,
}) => {
  const [isInitialized, setIsInitialized] = useState(false);
  const [notificationPermission, setNotificationPermission] = useState<string>('undetermined');
  const appState = useRef(AppState.currentState);
  const pushService = PushNotificationService.getInstance();

  useEffect(() => {
    initializeNotifications();
    setupAppStateListener();
    
    return () => {
      // Cleanup listeners when component unmounts
      AppState.removeEventListener?.('change', handleAppStateChange);
    };
  }, []);

  const initializeNotifications = async () => {
    try {
      console.log('🔔 Initializing push notifications...');
      
      // Initialize the push notification service
      const success = await pushService.initialize();
      
      if (success) {
        console.log('✅ Push notifications initialized successfully');
        setIsInitialized(true);
        
        // Check current permission status
        const { status } = await Notifications.getPermissionsAsync();
        setNotificationPermission(status);
        
        // Set up notification listeners
        setupNotificationListeners();
        
        // Schedule daily tips if user has them enabled
        await scheduleDailyNotifications();
        
      } else {
        console.warn('⚠️ Push notifications initialization failed');
      }
    } catch (error) {
      console.error('❌ Error initializing notifications:', error);
    }
  };

  const setupNotificationListeners = () => {
    // Handle notification received while app is in foreground
    const receivedSubscription = Notifications.addNotificationReceivedListener((notification) => {
      console.log('📱 Notification received:', notification.request.content.title);
      
      // Call custom handler if provided
      if (onNotificationReceived) {
        onNotificationReceived(notification);
      }
      
      // Track notification delivery
      trackNotificationEvent('delivered', notification.request.identifier);
    });

    // Handle notification tapped
    const responseSubscription = Notifications.addNotificationResponseReceivedListener((response) => {
      console.log('👆 Notification tapped:', response.notification.request.content.title);
      
      // Call custom handler if provided
      if (onNotificationTapped) {
        onNotificationTapped(response);
      }
      
      // Track notification interaction
      trackNotificationEvent('opened', response.notification.request.identifier);
      
      // Handle deep linking
      handleNotificationAction(response);
    });

    // Store subscriptions for cleanup
    return () => {
      receivedSubscription.remove();
      responseSubscription.remove();
    };
  };

  const setupAppStateListener = () => {
    const handleAppStateChange = (nextAppState: AppStateStatus) => {
      if (appState.current.match(/inactive|background/) && nextAppState === 'active') {
        // App has come to the foreground
        console.log('📱 App came to foreground');
        
        // Clear badge count when app becomes active
        Notifications.setBadgeCountAsync(0);
        
        // Refresh notification preferences
        refreshNotificationSettings();
      }
      
      appState.current = nextAppState;
    };

    AppState.addEventListener('change', handleAppStateChange);
  };

  const scheduleDailyNotifications = async () => {
    try {
      // Check if user has daily tips enabled
      const preferences = await pushService.getNotificationPreferences();
      
      if (!preferences?.daily_tips_enabled || !preferences?.notifications_enabled) {
        return;
      }

      // Cancel existing daily notifications
      await Notifications.cancelAllScheduledNotificationsAsync();

      // Schedule daily tip notification
      const dailyTipTime = new Date();
      dailyTipTime.setHours(9, 0, 0, 0); // 9 AM

      // If it's already past 9 AM today, schedule for tomorrow
      if (dailyTipTime <= new Date()) {
        dailyTipTime.setDate(dailyTipTime.getDate() + 1);
      }

      await Notifications.scheduleNotificationAsync({
        content: {
          title: "Daily Nature Tip 🌿",
          body: "Discover something amazing about nature today!",
          data: { 
            type: 'daily_tip',
            actionUrl: '/explore'
          },
          sound: true,
          badge: 1,
        },
        trigger: {
          hour: 9,
          minute: 0,
          repeats: true,
        },
      });

      console.log('📅 Daily notifications scheduled');
    } catch (error) {
      console.error('Error scheduling daily notifications:', error);
    }
  };

  const handleNotificationAction = async (response: Notifications.NotificationResponse) => {
    const { data } = response.notification.request.content;
    
    if (data?.actionUrl) {
      // Handle deep linking based on actionUrl
      console.log('🔗 Handling notification action:', data.actionUrl);
      
      // You can integrate with your navigation system here
      // Example: navigation.navigate(data.actionUrl);
      
      // Track conversion if it's an A/B test notification
      if (data?.flagName) {
        await pushService.trackConversion(data.flagName);
      }
    }
  };

  const trackNotificationEvent = async (eventType: string, notificationId: string) => {
    try {
      // This would typically be handled by the PushNotificationService
      // but we can add additional tracking here if needed
      console.log(`📊 Tracking ${eventType} for notification:`, notificationId);
    } catch (error) {
      console.error('Error tracking notification event:', error);
    }
  };

  const refreshNotificationSettings = async () => {
    try {
      // Refresh user preferences and update local settings
      const preferences = await pushService.getNotificationPreferences();
      
      if (preferences) {
        // Update notification categories based on preferences
        await updateNotificationCategories(preferences);
      }
    } catch (error) {
      console.error('Error refreshing notification settings:', error);
    }
  };

  const updateNotificationCategories = async (preferences: any) => {
    try {
      // Set up notification categories for iOS
      if (Platform.OS === 'ios') {
        await Notifications.setNotificationCategoryAsync('daily_tip', [
          {
            identifier: 'view',
            buttonTitle: 'View Tip',
            options: { opensAppToForeground: true },
          },
          {
            identifier: 'dismiss',
            buttonTitle: 'Dismiss',
            options: { opensAppToForeground: false },
          },
        ]);

        await Notifications.setNotificationCategoryAsync('achievement', [
          {
            identifier: 'view',
            buttonTitle: 'View Achievement',
            options: { opensAppToForeground: true },
          },
          {
            identifier: 'share',
            buttonTitle: 'Share',
            options: { opensAppToForeground: true },
          },
        ]);
      }
    } catch (error) {
      console.error('Error updating notification categories:', error);
    }
  };

  // Public methods that can be called from parent components
  const requestPermissions = async (): Promise<boolean> => {
    try {
      const { status } = await Notifications.requestPermissionsAsync();
      setNotificationPermission(status);
      
      if (status === 'granted') {
        // Re-initialize if permissions were just granted
        await initializeNotifications();
        return true;
      }
      
      return false;
    } catch (error) {
      console.error('Error requesting notification permissions:', error);
      return false;
    }
  };

  const sendTestNotification = async () => {
    try {
      if (!isInitialized) {
        console.warn('Notifications not initialized');
        return;
      }

      await pushService.scheduleLocalNotification({
        title: "Test Notification 🧪",
        body: "This is a test notification from Bioscan+",
        data: { type: 'test' },
      });

      console.log('🧪 Test notification sent');
    } catch (error) {
      console.error('Error sending test notification:', error);
    }
  };

  const sendAchievementNotification = async (achievementData: any) => {
    try {
      await pushService.sendPersonalizedNotification(
        'new_species_discovered',
        achievementData
      );
    } catch (error) {
      console.error('Error sending achievement notification:', error);
    }
  };

  const sendReminderNotification = async (reminderData: any) => {
    try {
      await pushService.sendPersonalizedNotification(
        'location_reminder',
        reminderData
      );
    } catch (error) {
      console.error('Error sending reminder notification:', error);
    }
  };

  // Expose notification manager methods through context or props
  const notificationManager = {
    isInitialized,
    notificationPermission,
    requestPermissions,
    sendTestNotification,
    sendAchievementNotification,
    sendReminderNotification,
    refreshSettings: refreshNotificationSettings,
  };

  // You can use React Context to provide these methods to child components
  return (
    <>
      {children}
    </>
  );
};

// Hook to use notification manager in child components
export const useNotificationManager = () => {
  const pushService = PushNotificationService.getInstance();
  
  return {
    sendAchievement: async (data: any) => {
      await pushService.sendPersonalizedNotification('new_species_discovered', data);
    },
    sendReminder: async (data: any) => {
      await pushService.sendPersonalizedNotification('location_reminder', data);
    },
    trackConversion: async (flagName: string, value?: number) => {
      await pushService.trackConversion(flagName, value);
    },
    getPreferences: async () => {
      return await pushService.getNotificationPreferences();
    },
    updatePreferences: async (preferences: any) => {
      return await pushService.updateNotificationPreferences(preferences);
    },
  };
};

export default NotificationManager;
