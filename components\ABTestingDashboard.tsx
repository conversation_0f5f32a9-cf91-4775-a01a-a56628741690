import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import {
  TestTube,
  TrendingUp,
  Users,
  Target,
  Play,
  Pause,
  Square,
  CheckCircle,
  AlertCircle,
  Clock,
  Award,
  Activity,
  Eye,
  MousePointer,
} from 'lucide-react-native';
import { ExperimentResults, ExperimentAssignment } from '../services/ABTestingService';

// Define ABExperiment interface locally since it's not exported
interface ABExperiment {
  id: string;
  experiment_key: string;
  experiment_name: string;
  description?: string;
  hypothesis?: string;
  experiment_type: 'ab_test' | 'multivariate' | 'feature_flag';
  status: 'draft' | 'running' | 'paused' | 'completed' | 'archived';
  traffic_allocation: number;
  targeting_rules: any;
  audience_segments: string[];
  environment: string;
  platform: string;
  start_date?: string;
  end_date?: string;
  primary_metric?: string;
  secondary_metrics: string[];
  conversion_goals: any[];
  results: any;
  winner_variant?: string;
  statistical_significance?: number;
  confidence_level?: number;
}

interface ABTestingDashboardProps {
  userRole?: 'admin' | 'developer' | 'user';
}

export const ABTestingDashboard: React.FC<ABTestingDashboardProps> = ({
  userRole = 'user', // Used for role-based access control in real implementation
}) => {
  const [experiments, setExperiments] = useState<ABExperiment[]>([]);
  const [experimentResults, setExperimentResults] = useState<{ [key: string]: ExperimentResults }>({});
  const [userAssignments, setUserAssignments] = useState<{ [key: string]: ExperimentAssignment }>({});
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [activeTab, setActiveTab] = useState<'running' | 'completed' | 'assignments'>('running');

  // Note: userRole and abTestingService would be used in a real implementation

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      
      await Promise.all([
        loadExperiments(),
        loadExperimentResults(),
        loadUserAssignments(),
      ]);
    } catch (error) {
      console.error('Error loading A/B testing dashboard data:', error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const loadExperiments = async () => {
    try {
      // This would typically call an admin API to get experiments
      // For now, we'll use mock data to demonstrate the interface
      const mockExperiments: ABExperiment[] = [
        {
          id: '1',
          experiment_key: 'new_identification_ui',
          experiment_name: 'New Identification UI',
          description: 'Testing new species identification interface design',
          hypothesis: 'New UI will increase identification completion rate by 15%',
          experiment_type: 'ab_test',
          status: 'running',
          traffic_allocation: 50,
          targeting_rules: {},
          audience_segments: ['premium', 'free'],
          environment: 'production',
          platform: 'all',
          start_date: '2024-01-01T00:00:00Z',
          end_date: '2024-02-01T00:00:00Z',
          primary_metric: 'identification_completion_rate',
          secondary_metrics: ['time_to_complete', 'user_satisfaction'],
          conversion_goals: [{ name: 'complete_identification', value: 1 }],
          results: {},
          confidence_level: 0.95,
        },
        {
          id: '2',
          experiment_key: 'premium_onboarding_flow',
          experiment_name: 'Premium Onboarding Flow',
          description: 'Testing different premium subscription onboarding flows',
          hypothesis: 'Simplified onboarding will increase conversion by 25%',
          experiment_type: 'ab_test',
          status: 'completed',
          traffic_allocation: 100,
          targeting_rules: {},
          audience_segments: ['free'],
          environment: 'production',
          platform: 'all',
          start_date: '2023-12-01T00:00:00Z',
          end_date: '2023-12-31T00:00:00Z',
          primary_metric: 'subscription_conversion_rate',
          secondary_metrics: ['trial_signup_rate'],
          conversion_goals: [{ name: 'subscribe_premium', value: 1 }],
          results: {},
          winner_variant: 'simplified_flow',
          statistical_significance: 0.032,
          confidence_level: 0.95,
        },
      ];
      
      setExperiments(mockExperiments);
    } catch (error) {
      console.error('Error loading experiments:', error);
    }
  };

  const loadExperimentResults = async () => {
    try {
      const results: { [key: string]: ExperimentResults } = {};
      
      // Mock results for demonstration
      results['new_identification_ui'] = {
        experiment_id: '1',
        variants: {
          control: {
            exposure_count: 1250,
            conversion_count: 875,
            conversion_rate: 0.70,
            confidence_interval: [0.67, 0.73],
            statistical_significance: 0.0,
          },
          new_ui: {
            exposure_count: 1180,
            conversion_count: 944,
            conversion_rate: 0.80,
            confidence_interval: [0.77, 0.83],
            statistical_significance: 0.012,
          },
        },
        winner: 'new_ui',
        confidence_level: 0.95,
        is_statistically_significant: true,
      };

      results['premium_onboarding_flow'] = {
        experiment_id: '2',
        variants: {
          current_flow: {
            exposure_count: 2100,
            conversion_count: 168,
            conversion_rate: 0.08,
            confidence_interval: [0.07, 0.09],
            statistical_significance: 0.0,
          },
          simplified_flow: {
            exposure_count: 2050,
            conversion_count: 246,
            conversion_rate: 0.12,
            confidence_interval: [0.11, 0.13],
            statistical_significance: 0.032,
          },
        },
        winner: 'simplified_flow',
        confidence_level: 0.95,
        is_statistically_significant: true,
      };
      
      setExperimentResults(results);
    } catch (error) {
      console.error('Error loading experiment results:', error);
    }
  };

  const loadUserAssignments = async () => {
    try {
      // Mock user assignments for demonstration
      const assignments: { [key: string]: ExperimentAssignment } = {
        'new_identification_ui': {
          experiment_id: '1',
          variant_id: 'variant_1',
          variant_key: 'new_ui',
          assignment_method: 'deterministic',
          assignment_hash: 'abc123',
          assignment_context: {},
          user_segment: 'premium',
        },
      };
      
      setUserAssignments(assignments);
    } catch (error) {
      console.error('Error loading user assignments:', error);
    }
  };

  const onRefresh = () => {
    setRefreshing(true);
    loadDashboardData();
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'running': return '#10B981';
      case 'completed': return '#3B82F6';
      case 'paused': return '#F59E0B';
      case 'draft': return '#6B7280';
      case 'archived': return '#6B7280';
      default: return '#6B7280';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'running': return Play;
      case 'completed': return CheckCircle;
      case 'paused': return Pause;
      case 'draft': return Clock;
      case 'archived': return Square;
      default: return AlertCircle;
    }
  };

  const renderExperimentCard = (experiment: ABExperiment) => {
    const results = experimentResults[experiment.experiment_key];
    const StatusIcon = getStatusIcon(experiment.status);
    
    return (
      <View key={experiment.id} style={styles.experimentCard}>
        <View style={styles.experimentHeader}>
          <View style={styles.experimentInfo}>
            <Text style={styles.experimentName}>{experiment.experiment_name}</Text>
            <Text style={styles.experimentKey}>{experiment.experiment_key}</Text>
            {experiment.description && (
              <Text style={styles.experimentDescription}>{experiment.description}</Text>
            )}
          </View>
          
          <View style={styles.experimentStatus}>
            <View style={[styles.statusBadge, { backgroundColor: getStatusColor(experiment.status) }]}>
              <StatusIcon size={12} color="white" />
              <Text style={styles.statusText}>{experiment.status.toUpperCase()}</Text>
            </View>
          </View>
        </View>

        <View style={styles.experimentMetrics}>
          <View style={styles.metricItem}>
            <Users size={16} color="#6B7280" />
            <Text style={styles.metricLabel}>Traffic</Text>
            <Text style={styles.metricValue}>{experiment.traffic_allocation}%</Text>
          </View>
          
          <View style={styles.metricItem}>
            <Target size={16} color="#6B7280" />
            <Text style={styles.metricLabel}>Primary Metric</Text>
            <Text style={styles.metricValue}>{experiment.primary_metric}</Text>
          </View>
          
          {experiment.statistical_significance && (
            <View style={styles.metricItem}>
              <TrendingUp size={16} color="#10B981" />
              <Text style={styles.metricLabel}>Significance</Text>
              <Text style={[styles.metricValue, { color: '#10B981' }]}>
                {(experiment.statistical_significance * 100).toFixed(1)}%
              </Text>
            </View>
          )}
        </View>

        {results && (
          <View style={styles.resultsSection}>
            <Text style={styles.resultsTitle}>Results</Text>
            
            {Object.entries(results.variants).map(([variantKey, variantResults]) => (
              <View key={variantKey} style={styles.variantResult}>
                <View style={styles.variantHeader}>
                  <Text style={styles.variantName}>{variantKey}</Text>
                  {results.winner === variantKey && (
                    <View style={styles.winnerBadge}>
                      <Award size={12} color="#F59E0B" />
                      <Text style={styles.winnerText}>Winner</Text>
                    </View>
                  )}
                </View>
                
                <View style={styles.variantMetrics}>
                  <View style={styles.variantMetric}>
                    <Eye size={14} color="#6B7280" />
                    <Text style={styles.variantMetricText}>
                      {variantResults.exposure_count.toLocaleString()} exposures
                    </Text>
                  </View>
                  
                  <View style={styles.variantMetric}>
                    <MousePointer size={14} color="#6B7280" />
                    <Text style={styles.variantMetricText}>
                      {variantResults.conversion_count.toLocaleString()} conversions
                    </Text>
                  </View>
                  
                  <View style={styles.variantMetric}>
                    <TrendingUp size={14} color="#10B981" />
                    <Text style={styles.variantMetricText}>
                      {(variantResults.conversion_rate * 100).toFixed(2)}% rate
                    </Text>
                  </View>
                </View>
                
                {/* Conversion Rate Bar */}
                <View style={styles.conversionBar}>
                  <View 
                    style={[
                      styles.conversionFill,
                      { 
                        width: `${variantResults.conversion_rate * 100}%`,
                        backgroundColor: results.winner === variantKey ? '#10B981' : '#3B82F6'
                      }
                    ]} 
                  />
                </View>
              </View>
            ))}
            
            {results.is_statistically_significant && (
              <View style={styles.significanceIndicator}>
                <CheckCircle size={16} color="#10B981" />
                <Text style={styles.significanceText}>
                  Statistically significant at {(results.confidence_level * 100)}% confidence
                </Text>
              </View>
            )}
          </View>
        )}

        {experiment.hypothesis && (
          <View style={styles.hypothesisSection}>
            <Text style={styles.hypothesisLabel}>Hypothesis:</Text>
            <Text style={styles.hypothesisText}>{experiment.hypothesis}</Text>
          </View>
        )}
      </View>
    );
  };

  const renderRunningExperiments = () => {
    const runningExperiments = experiments.filter(exp => exp.status === 'running');
    
    return (
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Running Experiments ({runningExperiments.length})</Text>
        
        {runningExperiments.length === 0 ? (
          <View style={styles.emptyState}>
            <TestTube size={48} color="#6B7280" />
            <Text style={styles.emptyStateText}>No running experiments</Text>
            <Text style={styles.emptyStateSubtext}>
              Start an A/B test to optimize your app features
            </Text>
          </View>
        ) : (
          runningExperiments.map(renderExperimentCard)
        )}
      </View>
    );
  };

  const renderCompletedExperiments = () => {
    const completedExperiments = experiments.filter(exp => exp.status === 'completed');
    
    return (
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Completed Experiments ({completedExperiments.length})</Text>
        
        {completedExperiments.length === 0 ? (
          <View style={styles.emptyState}>
            <CheckCircle size={48} color="#6B7280" />
            <Text style={styles.emptyStateText}>No completed experiments</Text>
            <Text style={styles.emptyStateSubtext}>
              Completed experiments will appear here
            </Text>
          </View>
        ) : (
          completedExperiments.map(renderExperimentCard)
        )}
      </View>
    );
  };

  const renderUserAssignments = () => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>Your Experiment Assignments</Text>
      
      {Object.keys(userAssignments).length === 0 ? (
        <View style={styles.emptyState}>
          <Users size={48} color="#6B7280" />
          <Text style={styles.emptyStateText}>No active assignments</Text>
          <Text style={styles.emptyStateSubtext}>
            You're not currently part of any experiments
          </Text>
        </View>
      ) : (
        Object.entries(userAssignments).map(([experimentKey, assignment]) => {
          const experiment = experiments.find(exp => exp.experiment_key === experimentKey);
          if (!experiment) return null;
          
          return (
            <View key={experimentKey} style={styles.assignmentCard}>
              <View style={styles.assignmentHeader}>
                <Text style={styles.assignmentExperiment}>{experiment.experiment_name}</Text>
                <View style={styles.assignmentVariant}>
                  <Text style={styles.assignmentVariantText}>{assignment.variant_key}</Text>
                </View>
              </View>
              
              <Text style={styles.assignmentDescription}>{experiment.description}</Text>
              
              <View style={styles.assignmentDetails}>
                <Text style={styles.assignmentDetail}>
                  Assignment Method: {assignment.assignment_method}
                </Text>
                {assignment.user_segment && (
                  <Text style={styles.assignmentDetail}>
                    Segment: {assignment.user_segment}
                  </Text>
                )}
              </View>
            </View>
          );
        })
      )}
    </View>
  );

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <Activity size={32} color="#3B82F6" />
        <Text style={styles.loadingText}>Loading A/B testing data...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Header */}
      <LinearGradient
        colors={['#8B5CF6', '#7C3AED']}
        style={styles.header}
      >
        <TestTube size={32} color="white" />
        <Text style={styles.headerTitle}>A/B Testing</Text>
        <Text style={styles.headerSubtitle}>
          Experiment with features and optimize user experience
        </Text>
      </LinearGradient>

      {/* Tab Navigation */}
      <View style={styles.tabContainer}>
        {[
          { key: 'running', label: 'Running', icon: Play },
          { key: 'completed', label: 'Completed', icon: CheckCircle },
          { key: 'assignments', label: 'My Tests', icon: Users },
        ].map(({ key, label, icon: Icon }) => (
          <TouchableOpacity
            key={key}
            style={[styles.tab, activeTab === key && styles.activeTab]}
            onPress={() => setActiveTab(key as any)}
          >
            <Icon size={18} color={activeTab === key ? '#8B5CF6' : '#6B7280'} />
            <Text style={[styles.tabText, activeTab === key && styles.activeTabText]}>
              {label}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      {/* Content */}
      <ScrollView
        style={styles.content}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {activeTab === 'running' && renderRunningExperiments()}
        {activeTab === 'completed' && renderCompletedExperiments()}
        {activeTab === 'assignments' && renderUserAssignments()}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#F9FAFB',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#6B7280',
  },
  header: {
    padding: 24,
    paddingTop: 60,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: 'white',
    marginTop: 8,
  },
  headerSubtitle: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.8)',
    marginTop: 4,
    textAlign: 'center',
  },
  tabContainer: {
    flexDirection: 'row',
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  tab: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    gap: 6,
  },
  activeTab: {
    borderBottomWidth: 2,
    borderBottomColor: '#8B5CF6',
  },
  tabText: {
    fontSize: 12,
    fontWeight: '500',
    color: '#6B7280',
  },
  activeTabText: {
    color: '#8B5CF6',
  },
  content: {
    flex: 1,
    padding: 20,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 16,
  },
  experimentCard: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  experimentHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  experimentInfo: {
    flex: 1,
    marginRight: 12,
  },
  experimentName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 2,
  },
  experimentKey: {
    fontSize: 14,
    color: '#6B7280',
    fontFamily: 'monospace',
    marginBottom: 4,
  },
  experimentDescription: {
    fontSize: 14,
    color: '#6B7280',
    lineHeight: 18,
  },
  experimentStatus: {
    alignItems: 'flex-end',
  },
  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 4,
    gap: 4,
  },
  statusText: {
    fontSize: 10,
    fontWeight: '600',
    color: 'white',
  },
  experimentMetrics: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 16,
    marginBottom: 16,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
  },
  metricItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  metricLabel: {
    fontSize: 12,
    color: '#6B7280',
  },
  metricValue: {
    fontSize: 12,
    fontWeight: '600',
    color: '#111827',
  },
  resultsSection: {
    marginBottom: 16,
  },
  resultsTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 12,
  },
  variantResult: {
    backgroundColor: '#F9FAFB',
    borderRadius: 8,
    padding: 12,
    marginBottom: 8,
  },
  variantHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  variantName: {
    fontSize: 14,
    fontWeight: '600',
    color: '#111827',
  },
  winnerBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FEF3C7',
    borderRadius: 12,
    paddingHorizontal: 6,
    paddingVertical: 2,
    gap: 4,
  },
  winnerText: {
    fontSize: 10,
    fontWeight: '600',
    color: '#F59E0B',
  },
  variantMetrics: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
    marginBottom: 8,
  },
  variantMetric: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  variantMetricText: {
    fontSize: 12,
    color: '#6B7280',
  },
  conversionBar: {
    height: 4,
    backgroundColor: '#E5E7EB',
    borderRadius: 2,
    overflow: 'hidden',
  },
  conversionFill: {
    height: '100%',
    borderRadius: 2,
  },
  significanceIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#ECFDF5',
    borderRadius: 8,
    padding: 8,
    marginTop: 8,
    gap: 6,
  },
  significanceText: {
    fontSize: 12,
    fontWeight: '500',
    color: '#10B981',
  },
  hypothesisSection: {
    backgroundColor: '#F3F4F6',
    borderRadius: 8,
    padding: 12,
  },
  hypothesisLabel: {
    fontSize: 12,
    fontWeight: '600',
    color: '#374151',
    marginBottom: 4,
  },
  hypothesisText: {
    fontSize: 12,
    color: '#6B7280',
    lineHeight: 16,
    fontStyle: 'italic',
  },
  emptyState: {
    alignItems: 'center',
    padding: 32,
  },
  emptyStateText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#6B7280',
    marginTop: 12,
  },
  emptyStateSubtext: {
    fontSize: 14,
    color: '#9CA3AF',
    marginTop: 4,
    textAlign: 'center',
  },
  assignmentCard: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  assignmentHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  assignmentExperiment: {
    fontSize: 16,
    fontWeight: '600',
    color: '#111827',
    flex: 1,
  },
  assignmentVariant: {
    backgroundColor: '#8B5CF6',
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 4,
  },
  assignmentVariantText: {
    fontSize: 12,
    fontWeight: '600',
    color: 'white',
  },
  assignmentDescription: {
    fontSize: 14,
    color: '#6B7280',
    lineHeight: 18,
    marginBottom: 8,
  },
  assignmentDetails: {
    gap: 4,
  },
  assignmentDetail: {
    fontSize: 12,
    color: '#9CA3AF',
  },
});

export default ABTestingDashboard;
