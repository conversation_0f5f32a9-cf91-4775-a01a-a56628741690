import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  Dimensions,
  Alert,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useRouter } from 'expo-router';
import {
  User,
  Settings,
  Award,
  TrendingUp,
  Target,
  Zap,
  Star,
  MapPin,
  Calendar,
  Share,
  Crown,
  Trophy,
  Medal,
  Leaf,
  Bug,
  Bird,
  Camera,
  Heart,
  Globe,
  ChevronRight,
  Bell,
  Shield,
  Database,
} from 'lucide-react-native';

const { width } = Dimensions.get('window');

export default function ProfileScreen() {
  const router = useRouter();

  const achievements = [
    {
      id: 1,
      title: 'First Discovery',
      description: 'Scanned your first specimen',
      icon: Target,
      color: '#22C55E',
      earned: true,
      date: 'Dec 15, 2023',
    },
    {
      id: 2,
      title: 'Week Warrior',
      description: 'Scanned for 7 consecutive days',
      icon: Zap,
      color: '#F59E0B',
      earned: true,
      date: 'Jan 10, 2024',
    },
    {
      id: 3,
      title: 'Nature Photographer',
      description: 'Uploaded 50 quality photos',
      icon: Camera,
      color: '#3B82F6',
      earned: true,
      date: 'Jan 12, 2024',
    },
    {
      id: 4,
      title: 'Rare Hunter',
      description: 'Found 5 rare species',
      icon: Crown,
      color: '#8B5CF6',
      earned: false,
      progress: 3,
      total: 5,
    },
    {
      id: 5,
      title: 'Global Explorer',
      description: 'Scanned in 10 different countries',
      icon: Globe,
      color: '#06B6D4',
      earned: false,
      progress: 4,
      total: 10,
    },
    {
      id: 6,
      title: 'Community Helper',
      description: 'Helped identify 100 specimens',
      icon: Heart,
      color: '#EF4444',
      earned: false,
      progress: 27,
      total: 100,
    },
  ];

  const stats = [
    { label: 'Total Scans', value: '1,247', icon: Camera, color: '#22C55E' },
    { label: 'Species Found', value: '127', icon: Leaf, color: '#3B82F6' },
    { label: 'Streak Days', value: '7', icon: Zap, color: '#F59E0B' },
    { label: 'Rank', value: '#2,456', icon: Trophy, color: '#8B5CF6' },
  ];

  const categoryStats = [
    { category: 'Plants', count: 45, icon: Leaf, color: '#22C55E' },
    { category: 'Insects', count: 32, icon: Bug, color: '#F59E0B' },
    { category: 'Birds', count: 28, icon: Bird, color: '#3B82F6' },
    { category: 'Others', count: 22, icon: Star, color: '#6B7280' },
  ];

  // Navigation functions with error handling
  const navigateToSettings = () => {
    try {
      router.push('/(tabs)/settings');
    } catch (error) {
      console.error('Navigation error to settings:', error);
      Alert.alert('Navigation Error', 'Unable to open settings. Please try again.');
    }
  };

  const navigateToNotifications = () => {
    try {
      // Navigate to settings and show notification section
      router.push('/(tabs)/settings');
      // Note: In a real app, you might pass parameters to highlight the notifications section
    } catch (error) {
      console.error('Navigation error to notifications:', error);
      Alert.alert('Navigation Error', 'Unable to open notification settings. Please try again.');
    }
  };

  const navigateToPrivacy = () => {
    try {
      // Navigate to settings and show privacy section
      router.push('/(tabs)/settings');
      // Note: In a real app, you might pass parameters to highlight the privacy section
    } catch (error) {
      console.error('Navigation error to privacy:', error);
      Alert.alert('Navigation Error', 'Unable to open privacy settings. Please try again.');
    }
  };

  const menuItems = [
    { 
      label: 'Account Settings', 
      icon: Settings, 
      color: '#6B7280',
      onPress: navigateToSettings,
      description: 'Manage your account preferences'
    },
    { 
      label: 'Privacy & Data', 
      icon: Shield, 
      color: '#6B7280',
      onPress: navigateToPrivacy,
      description: 'Control your privacy settings'
    },
    { 
      label: 'Notifications', 
      icon: Bell, 
      color: '#6B7280',
      onPress: navigateToNotifications,
      description: 'Manage notification preferences'
    },
    { 
      label: 'Share App', 
      icon: Share, 
      color: '#6B7280',
      onPress: () => Alert.alert('Share', 'Share functionality would be implemented here'),
      description: 'Tell friends about BioScan'
    },
  ];

  const renderAchievement = (achievement: any) => {
    const IconComponent = achievement.icon;
    return (
      <TouchableOpacity key={achievement.id} style={styles.achievementCard}>
        <View style={[
          styles.achievementIcon,
          { backgroundColor: achievement.color + '20' },
          !achievement.earned && styles.achievementIconLocked
        ]}>
          <IconComponent
            size={20}
            color={achievement.earned ? achievement.color : '#9CA3AF'}
          />
        </View>
        
        <View style={styles.achievementContent}>
          <Text style={[
            styles.achievementTitle,
            !achievement.earned && styles.achievementTitleLocked
          ]}>
            {achievement.title}
          </Text>
          <Text style={styles.achievementDescription}>
            {achievement.description}
          </Text>
          
          {achievement.earned ? (
            <Text style={styles.achievementDate}>Earned {achievement.date}</Text>
          ) : (
            <View style={styles.progressContainer}>
              <View style={styles.progressBar}>
                <View
                  style={[
                    styles.progressFill,
                    {
                      width: `${(achievement.progress / achievement.total) * 100}%`,
                      backgroundColor: achievement.color,
                    }
                  ]}
                />
              </View>
              <Text style={styles.progressText}>
                {achievement.progress}/{achievement.total}
              </Text>
            </View>
          )}
        </View>
        
        {achievement.earned && (
          <Medal size={16} color={achievement.color} />
        )}
      </TouchableOpacity>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <LinearGradient
        colors={['#F3E8FF', '#E9D5FF', '#DDD6FE']}
        style={styles.gradient}>
        <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
          {/* Profile Header */}
          <View style={styles.profileHeader}>
            <LinearGradient
              colors={['#8B5CF6', '#7C3AED']}
              style={styles.avatarContainer}>
              <User size={40} color="#FFFFFF" />
            </LinearGradient>
            
            <View style={styles.profileInfo}>
              <Text style={styles.profileName}>Nature Explorer</Text>
              <Text style={styles.profileUsername}>@explorer2024</Text>
              <View style={styles.profileLocation}>
                <MapPin size={14} color="#6B7280" />
                <Text style={styles.locationText}>San Francisco, CA</Text>
              </View>
            </View>
            
            <TouchableOpacity style={styles.editButton} onPress={navigateToSettings}>
              <Settings size={20} color="#6B7280" />
            </TouchableOpacity>
          </View>

          {/* Stats Grid */}
          <View style={styles.statsGrid}>
            {stats.map((stat, index) => {
              const IconComponent = stat.icon;
              return (
                <View key={index} style={styles.statCard}>
                  <IconComponent size={20} color={stat.color} />
                  <Text style={styles.statValue}>{stat.value}</Text>
                  <Text style={styles.statLabel}>{stat.label}</Text>
                </View>
              );
            })}
          </View>

          {/* Category Breakdown */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Discovery Breakdown</Text>
            <View style={styles.categoryContainer}>
              {categoryStats.map((category, index) => {
                const IconComponent = category.icon;
                return (
                  <View key={index} style={styles.categoryCard}>
                    <View style={styles.categoryHeader}>
                      <IconComponent size={18} color={category.color} />
                      <Text style={styles.categoryName}>{category.category}</Text>
                    </View>
                    <Text style={styles.categoryCount}>{category.count}</Text>
                    <View style={styles.categoryBar}>
                      <View
                        style={[
                          styles.categoryFill,
                          {
                            backgroundColor: category.color,
                            width: `${(category.count / 50) * 100}%`,
                          }
                        ]}
                      />
                    </View>
                  </View>
                );
              })}
            </View>
          </View>

          {/* Activity Chart */}
          <View style={styles.section}>
            <View style={styles.sectionHeader}>
              <Text style={styles.sectionTitle}>Recent Activity</Text>
              <TouchableOpacity>
                <Text style={styles.seeAllText}>View All</Text>
              </TouchableOpacity>
            </View>
            
            <View style={styles.activityCard}>
              <LinearGradient
                colors={['#22C55E', '#16A34A']}
                style={styles.activityGradient}>
                <View style={styles.activityHeader}>
                  <TrendingUp size={24} color="#FFFFFF" />
                  <Text style={styles.activityTitle}>This Week</Text>
                </View>
                <Text style={styles.activityValue}>23</Text>
                <Text style={styles.activityLabel}>New Discoveries</Text>
                <Text style={styles.activityChange}>+35% from last week</Text>
              </LinearGradient>
            </View>
          </View>

          {/* Achievements */}
          <View style={styles.section}>
            <View style={styles.sectionHeader}>
              <View style={styles.sectionTitleContainer}>
                <Award size={20} color="#F59E0B" />
                <Text style={styles.sectionTitle}>Achievements</Text>
              </View>
              <TouchableOpacity>
                <Text style={styles.seeAllText}>See All</Text>
              </TouchableOpacity>
            </View>

            <View style={styles.achievementsContainer}>
              {achievements.slice(0, 4).map(renderAchievement)}
            </View>
          </View>

          {/* Settings Menu */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Settings</Text>
            <View style={styles.menuContainer}>
              {menuItems.map((item, index) => {
                const IconComponent = item.icon;
                return (
                  <TouchableOpacity 
                    key={index} 
                    style={styles.menuItem}
                    onPress={item.onPress}
                    activeOpacity={0.7}>
                    <View style={styles.menuItemLeft}>
                      <View style={styles.menuItemIcon}>
                        <IconComponent size={20} color={item.color} />
                      </View>
                      <View style={styles.menuItemText}>
                        <Text style={styles.menuItemTitle}>{item.label}</Text>
                        <Text style={styles.menuItemDescription}>{item.description}</Text>
                      </View>
                    </View>
                    <ChevronRight size={16} color="#9CA3AF" />
                  </TouchableOpacity>
                );
              })}
            </View>
          </View>

          {/* Pro Upgrade */}
          <View style={styles.section}>
            <LinearGradient
              colors={['#F59E0B', '#D97706']}
              style={styles.proCard}>
              <View style={styles.proHeader}>
                <Crown size={24} color="#FFFFFF" />
                <Text style={styles.proTitle}>Upgrade to Pro</Text>
              </View>
              <Text style={styles.proDescription}>
                Unlock unlimited scans, advanced AR features, and exclusive content
              </Text>
              <TouchableOpacity style={styles.proButton}>
                <Text style={styles.proButtonText}>Start Free Trial</Text>
              </TouchableOpacity>
            </LinearGradient>
          </View>

          {/* Bottom Spacing */}
          <View style={styles.bottomSpacing} />
        </ScrollView>
      </LinearGradient>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F3E8FF',
  },
  gradient: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  profileHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 24,
    gap: 16,
  },
  avatarContainer: {
    width: 80,
    height: 80,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#8B5CF6',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 12,
    elevation: 8,
  },
  profileInfo: {
    flex: 1,
  },
  profileName: {
    fontSize: 24,
    fontFamily: 'Inter-Bold',
    color: '#111827',
    marginBottom: 4,
  },
  profileUsername: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    marginBottom: 8,
  },
  profileLocation: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  locationText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
  },
  editButton: {
    padding: 12,
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    paddingHorizontal: 20,
    marginBottom: 24,
    gap: 12,
  },
  statCard: {
    width: (width - 52) / 2,
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 20,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  statValue: {
    fontSize: 28,
    fontFamily: 'Inter-Bold',
    color: '#111827',
    marginTop: 12,
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
    textAlign: 'center',
  },
  section: {
    paddingHorizontal: 20,
    marginBottom: 24,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  sectionTitle: {
    fontSize: 20,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
  },
  seeAllText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#8B5CF6',
  },
  categoryContainer: {
    gap: 12,
  },
  categoryCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  categoryHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 8,
  },
  categoryName: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    flex: 1,
  },
  categoryCount: {
    fontSize: 24,
    fontFamily: 'Inter-Bold',
    color: '#111827',
    marginBottom: 8,
  },
  categoryBar: {
    height: 4,
    backgroundColor: '#E5E7EB',
    borderRadius: 2,
  },
  categoryFill: {
    height: '100%',
    borderRadius: 2,
  },
  activityCard: {
    borderRadius: 16,
    overflow: 'hidden',
    shadowColor: '#22C55E',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 12,
    elevation: 8,
  },
  activityGradient: {
    padding: 24,
    alignItems: 'center',
  },
  activityHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 16,
  },
  activityTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: '#FFFFFF',
  },
  activityValue: {
    fontSize: 48,
    fontFamily: 'Inter-Bold',
    color: '#FFFFFF',
    marginBottom: 8,
  },
  activityLabel: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    color: '#FFFFFF',
    opacity: 0.9,
    marginBottom: 8,
  },
  activityChange: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#FFFFFF',
    opacity: 0.8,
  },
  achievementsContainer: {
    gap: 12,
  },
  achievementCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    flexDirection: 'row',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  achievementIcon: {
    width: 40,
    height: 40,
    borderRadius: 10,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
  },
  achievementIconLocked: {
    backgroundColor: '#F3F4F6',
  },
  achievementContent: {
    flex: 1,
  },
  achievementTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    marginBottom: 4,
  },
  achievementTitleLocked: {
    color: '#9CA3AF',
  },
  achievementDescription: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    marginBottom: 8,
  },
  achievementDate: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#22C55E',
  },
  progressContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  progressBar: {
    flex: 1,
    height: 4,
    backgroundColor: '#E5E7EB',
    borderRadius: 2,
  },
  progressFill: {
    height: '100%',
    borderRadius: 2,
  },
  progressText: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
    minWidth: 35,
  },
  menuContainer: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
  },
  menuItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
    flex: 1,
  },
  menuItemIcon: {
    width: 40,
    height: 40,
    borderRadius: 10,
    backgroundColor: '#F3F4F6',
    alignItems: 'center',
    justifyContent: 'center',
  },
  menuItemText: {
    flex: 1,
  },
  menuItemTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    marginBottom: 2,
  },
  menuItemDescription: {
    fontSize: 13,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    lineHeight: 18,
  },
  proCard: {
    borderRadius: 20,
    padding: 24,
    alignItems: 'center',
  },
  proHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
    marginBottom: 16,
  },
  proTitle: {
    fontSize: 20,
    fontFamily: 'Inter-Bold',
    color: '#FFFFFF',
  },
  proDescription: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#FFFFFF',
    opacity: 0.9,
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: 24,
  },
  proButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 12,
    paddingVertical: 12,
    paddingHorizontal: 24,
  },
  proButtonText: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#FFFFFF',
  },
  bottomSpacing: {
    height: 100,
  },
});