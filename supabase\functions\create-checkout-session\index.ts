import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import Stripe from 'https://esm.sh/stripe@12.0.0?target=deno'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

// Rate limiting configuration
const RATE_LIMITS = {
  MAX_REQUESTS_PER_HOUR: 10,
  MAX_REQUESTS_PER_DAY: 50
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Initialize Stripe
    const stripe = new Stripe(Deno.env.get('STRIPE_SECRET_KEY') || '', {
      apiVersion: '2023-10-16',
    })

    // Initialize Supabase client
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    // Get client information for fraud detection
    const clientIP = req.headers.get('CF-Connecting-IP') || req.headers.get('X-Forwarded-For') || 'unknown'
    const userAgent = req.headers.get('User-Agent') || 'unknown'

    // Get the authorization header from the request
    const authHeader = req.headers.get('Authorization')!
    const token = authHeader.replace('Bearer ', '')

    // Verify the JWT token
    const { data: { user }, error: authError } = await supabaseClient.auth.getUser(token)
    if (authError || !user) {
      throw new Error('Unauthorized')
    }

    const { planId, billingPeriod, userId, userEmail, promoCode } = await req.json()

    // Rate limiting check
    const rateLimitPassed = await checkRateLimit(supabaseClient, userId, clientIP)
    if (!rateLimitPassed) {
      throw new Error('Rate limit exceeded. Please try again later.')
    }

    // Get the subscription plan
    const { data: plan, error: planError } = await supabaseClient
      .from('subscription_plans')
      .select('*')
      .eq('id', planId)
      .single()

    if (planError || !plan) {
      throw new Error('Subscription plan not found')
    }

    // Get or create Stripe customer
    let customer
    const { data: existingCustomer } = await supabaseClient
      .from('stripe_customers')
      .select('stripe_customer_id')
      .eq('user_id', userId)
      .single()

    if (existingCustomer) {
      customer = await stripe.customers.retrieve(existingCustomer.stripe_customer_id)
    } else {
      // Create new Stripe customer
      customer = await stripe.customers.create({
        email: userEmail,
        metadata: {
          supabase_user_id: userId,
        },
      })

      // Store customer ID in database
      await supabaseClient
        .from('stripe_customers')
        .insert({
          user_id: userId,
          stripe_customer_id: customer.id,
        })
    }

    // Determine price based on billing period
    const price = billingPeriod === 'yearly' ? plan.price_yearly : plan.price_monthly
    const priceId = billingPeriod === 'yearly' 
      ? plan.stripe_price_id_yearly 
      : plan.stripe_price_id_monthly

    if (!priceId) {
      throw new Error('Price ID not configured for this plan')
    }

    // Handle promotional code if provided
    let discounts = undefined
    if (promoCode) {
      const { data: promoData } = await supabaseClient
        .from('promotional_codes')
        .select('*')
        .eq('code', promoCode)
        .eq('is_active', true)
        .single()

      if (promoData && promoData.stripe_coupon_id) {
        discounts = [{ coupon: promoData.stripe_coupon_id }]
      }
    }

    // Create checkout session with enhanced security
    const session = await stripe.checkout.sessions.create({
      customer: customer.id,
      payment_method_types: ['card'],
      line_items: [
        {
          price: priceId,
          quantity: 1,
        },
      ],
      mode: 'subscription',
      success_url: `${Deno.env.get('FRONTEND_URL')}/subscription/success?session_id={CHECKOUT_SESSION_ID}`,
      cancel_url: `${Deno.env.get('FRONTEND_URL')}/subscription/cancel`,
      metadata: {
        user_id: userId,
        plan_id: planId,
        billing_period: billingPeriod,
        client_ip: clientIP,
        user_agent: userAgent,
      },
      subscription_data: {
        metadata: {
          user_id: userId,
          plan_id: planId,
          billing_period: billingPeriod,
        },
        trial_period_days: plan.trial_period_days || 0,
      },
      discounts: discounts,
      allow_promotion_codes: !promoCode, // Disable if promo code already applied
      billing_address_collection: 'required',
      tax_id_collection: {
        enabled: true,
      },
      automatic_tax: {
        enabled: true,
      },
      customer_update: {
        address: 'auto',
        name: 'auto',
      },
      payment_intent_data: {
        metadata: {
          user_id: userId,
          client_ip: clientIP,
        },
      },
    })

    // Log checkout session creation for audit
    await supabaseClient
      .from('fraud_detection_logs')
      .insert({
        user_id: userId,
        ip_address: clientIP,
        user_agent: userAgent,
        risk_factors: {
          action: 'checkout_session_created',
          plan_id: planId,
          billing_period: billingPeriod,
          session_id: session.id
        },
        risk_score: 0,
        action_taken: 'allow',
        notes: 'Checkout session created successfully'
      })

    return new Response(
      JSON.stringify({ url: session.url }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      }
    )
  } catch (error: any) {
    console.error('Error creating checkout session:', error)
    return new Response(
      JSON.stringify({ error: error.message }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400,
      }
    )
  }
})

// Rate limiting function
async function checkRateLimit(supabaseClient: any, userId: string, clientIP: string): Promise<boolean> {
  try {
    const now = new Date()
    const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000)
    const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000)

    // Check hourly rate limit
    const { data: hourlyRequests } = await supabaseClient
      .from('fraud_detection_logs')
      .select('id')
      .eq('user_id', userId)
      .gte('created_at', oneHourAgo.toISOString())
      .contains('risk_factors', { action: 'checkout_session_created' })

    if (hourlyRequests && hourlyRequests.length >= RATE_LIMITS.MAX_REQUESTS_PER_HOUR) {
      return false
    }

    // Check daily rate limit
    const { data: dailyRequests } = await supabaseClient
      .from('fraud_detection_logs')
      .select('id')
      .eq('user_id', userId)
      .gte('created_at', oneDayAgo.toISOString())
      .contains('risk_factors', { action: 'checkout_session_created' })

    if (dailyRequests && dailyRequests.length >= RATE_LIMITS.MAX_REQUESTS_PER_DAY) {
      return false
    }

    // Check IP-based rate limiting
    const { data: ipRequests } = await supabaseClient
      .from('fraud_detection_logs')
      .select('id')
      .eq('ip_address', clientIP)
      .gte('created_at', oneHourAgo.toISOString())
      .contains('risk_factors', { action: 'checkout_session_created' })

    if (ipRequests && ipRequests.length >= RATE_LIMITS.MAX_REQUESTS_PER_HOUR * 2) {
      return false
    }

    return true
  } catch (error) {
    console.error('Error checking rate limit:', error)
    return true // Allow on error to avoid blocking legitimate users
  }
}
