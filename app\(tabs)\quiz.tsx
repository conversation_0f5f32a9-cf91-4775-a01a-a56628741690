import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Alert,
  Dimensions,
  ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import * as Haptics from 'expo-haptics';
import { router } from 'expo-router';

import { QuizService, Quiz, QuizQuestion, UserQuizStats } from '../../services/QuizService';
import { useAuth } from '../../components/AuthContext';

const { width } = Dimensions.get('window');

export default function QuizScreen() {
  const [currentQuiz, setCurrentQuiz] = useState<Quiz | null>(null);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [selectedAnswer, setSelectedAnswer] = useState<string | null>(null);
  const [showResult, setShowResult] = useState(false);
  const [quizResults, setQuizResults] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [quizStats, setQuizStats] = useState<UserQuizStats | null>(null);
  const [startTime, setStartTime] = useState<number>(0);

  const { user, isAuthenticated } = useAuth();
  const quizService = new QuizService();

  useEffect(() => {
    if (!isAuthenticated) {
      router.replace('/auth/login');
      return;
    }
    
    loadQuizStats();
  }, [isAuthenticated]);

  const loadQuizStats = async () => {
    try {
      const stats = await quizService.getUserQuizStats();
      setQuizStats(stats);
    } catch (error) {
      console.error('Error loading quiz stats:', error);
    }
  };

  const startPersonalizedQuiz = async () => {
    try {
      setLoading(true);
      const quiz = await quizService.generatePersonalizedQuiz(2, 10);
      setCurrentQuiz(quiz);
      setCurrentQuestionIndex(0);
      setSelectedAnswer(null);
      setShowResult(false);
      setQuizResults([]);
      setStartTime(Date.now());
    } catch (error) {
      console.error('Error starting quiz:', error);
      Alert.alert('Error', 'Failed to generate quiz. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const startGeneralQuiz = async () => {
    try {
      setLoading(true);
      const quiz = await quizService.generateGeneralQuiz(2, 10);
      setCurrentQuiz(quiz);
      setCurrentQuestionIndex(0);
      setSelectedAnswer(null);
      setShowResult(false);
      setQuizResults([]);
      setStartTime(Date.now());
    } catch (error) {
      console.error('Error starting quiz:', error);
      Alert.alert('Error', 'Failed to generate quiz. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleAnswerSelect = (answer: string) => {
    setSelectedAnswer(answer);
    Haptics.selectionAsync();
  };

  const submitAnswer = async () => {
    if (!currentQuiz || !selectedAnswer) return;

    const currentQuestion = currentQuiz.questions[currentQuestionIndex];
    const timeTaken = Math.floor((Date.now() - startTime) / 1000);
    const isCorrect = selectedAnswer === currentQuestion.correct_answer;

    // Store result
    const result = {
      question: currentQuestion,
      userAnswer: selectedAnswer,
      isCorrect,
      timeTaken,
    };

    setQuizResults(prev => [...prev, result]);

    // Haptic feedback
    if (isCorrect) {
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
    } else {
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
    }

    // Move to next question or show results
    if (currentQuestionIndex < currentQuiz.questions.length - 1) {
      setTimeout(() => {
        setCurrentQuestionIndex(prev => prev + 1);
        setSelectedAnswer(null);
        setStartTime(Date.now());
      }, 1500);
    } else {
      setTimeout(() => {
        setShowResult(true);
        loadQuizStats(); // Refresh stats
      }, 1500);
    }
  };

  const resetQuiz = () => {
    setCurrentQuiz(null);
    setCurrentQuestionIndex(0);
    setSelectedAnswer(null);
    setShowResult(false);
    setQuizResults([]);
  };

  const getScoreColor = (percentage: number) => {
    if (percentage >= 80) return '#22C55E';
    if (percentage >= 60) return '#F59E0B';
    return '#EF4444';
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#22C55E" />
        <Text style={styles.loadingText}>Generating quiz...</Text>
      </View>
    );
  }

  if (!currentQuiz) {
    return (
      <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
        {/* Header */}
        <LinearGradient
          colors={['#22C55E', '#16A34A']}
          style={styles.header}
        >
          <Text style={styles.headerTitle}>Nature Quiz</Text>
          <Text style={styles.headerSubtitle}>Test your knowledge!</Text>
        </LinearGradient>

        {/* Stats */}
        {quizStats && (
          <View style={styles.statsContainer}>
            <Text style={styles.statsTitle}>Your Progress</Text>
            <View style={styles.statsGrid}>
              <View style={styles.statItem}>
                <Text style={styles.statNumber}>{quizStats.total_quizzes_taken}</Text>
                <Text style={styles.statLabel}>Questions Answered</Text>
              </View>
              <View style={styles.statItem}>
                <Text style={styles.statNumber}>{quizStats.average_score}%</Text>
                <Text style={styles.statLabel}>Average Score</Text>
              </View>
              <View style={styles.statItem}>
                <Text style={styles.statNumber}>{Math.floor(quizStats.total_time_spent / 60)}</Text>
                <Text style={styles.statLabel}>Minutes Played</Text>
              </View>
            </View>
          </View>
        )}

        {/* Quiz Options */}
        <View style={styles.optionsContainer}>
          <TouchableOpacity
            style={styles.quizOption}
            onPress={startPersonalizedQuiz}
          >
            <LinearGradient
              colors={['#3B82F6', '#1D4ED8']}
              style={styles.optionGradient}
            >
              <Ionicons name="person" size={32} color="white" />
              <Text style={styles.optionTitle}>Personalized Quiz</Text>
              <Text style={styles.optionDescription}>
                Based on species you've discovered
              </Text>
            </LinearGradient>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.quizOption}
            onPress={startGeneralQuiz}
          >
            <LinearGradient
              colors={['#8B5CF6', '#7C3AED']}
              style={styles.optionGradient}
            >
              <Ionicons name="globe" size={32} color="white" />
              <Text style={styles.optionTitle}>General Quiz</Text>
              <Text style={styles.optionDescription}>
                Random species from our database
              </Text>
            </LinearGradient>
          </TouchableOpacity>
        </View>

        {/* Features */}
        <View style={styles.featuresContainer}>
          <Text style={styles.featuresTitle}>Quiz Features</Text>
          <View style={styles.featuresList}>
            <View style={styles.featureItem}>
              <Ionicons name="checkmark-circle" size={20} color="#22C55E" />
              <Text style={styles.featureText}>Adaptive difficulty</Text>
            </View>
            <View style={styles.featureItem}>
              <Ionicons name="checkmark-circle" size={20} color="#22C55E" />
              <Text style={styles.featureText}>Detailed explanations</Text>
            </View>
            <View style={styles.featureItem}>
              <Ionicons name="checkmark-circle" size={20} color="#22C55E" />
              <Text style={styles.featureText}>Progress tracking</Text>
            </View>
            <View style={styles.featureItem}>
              <Ionicons name="checkmark-circle" size={20} color="#22C55E" />
              <Text style={styles.featureText}>Multiple question types</Text>
            </View>
          </View>
        </View>
      </ScrollView>
    );
  }

  if (showResult) {
    const correctAnswers = quizResults.filter(r => r.isCorrect).length;
    const totalQuestions = quizResults.length;
    const scorePercentage = Math.round((correctAnswers / totalQuestions) * 100);

    return (
      <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
        <View style={styles.resultContainer}>
          <Text style={styles.resultTitle}>Quiz Complete!</Text>
          
          <View style={styles.scoreContainer}>
            <Text style={[styles.scoreText, { color: getScoreColor(scorePercentage) }]}>
              {scorePercentage}%
            </Text>
            <Text style={styles.scoreSubtext}>
              {correctAnswers} out of {totalQuestions} correct
            </Text>
          </View>

          <View style={styles.resultActions}>
            <TouchableOpacity
              style={styles.actionButton}
              onPress={resetQuiz}
            >
              <Ionicons name="refresh" size={20} color="white" />
              <Text style={styles.actionButtonText}>Try Again</Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[styles.actionButton, styles.secondaryButton]}
              onPress={() => router.push('/camera')}
            >
              <Ionicons name="camera" size={20} color="#22C55E" />
              <Text style={[styles.actionButtonText, styles.secondaryButtonText]}>
                Identify Species
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>
    );
  }

  const currentQuestion = currentQuiz.questions[currentQuestionIndex];
  const progress = ((currentQuestionIndex + 1) / currentQuiz.questions.length) * 100;

  return (
    <View style={styles.container}>
      {/* Progress Bar */}
      <View style={styles.progressContainer}>
        <View style={styles.progressBar}>
          <View style={[styles.progressFill, { width: `${progress}%` }]} />
        </View>
        <Text style={styles.progressText}>
          {currentQuestionIndex + 1} of {currentQuiz.questions.length}
        </Text>
      </View>

      {/* Question */}
      <ScrollView style={styles.questionContainer} showsVerticalScrollIndicator={false}>
        <Text style={styles.questionText}>{currentQuestion.question_text}</Text>

        {/* Answer Options */}
        <View style={styles.optionsContainer}>
          {currentQuestion.options?.map((option, index) => (
            <TouchableOpacity
              key={index}
              style={[
                styles.answerOption,
                selectedAnswer === option && styles.selectedOption,
              ]}
              onPress={() => handleAnswerSelect(option)}
            >
              <Text
                style={[
                  styles.answerText,
                  selectedAnswer === option && styles.selectedAnswerText,
                ]}
              >
                {option}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </ScrollView>

      {/* Submit Button */}
      <View style={styles.submitContainer}>
        <TouchableOpacity
          style={[
            styles.submitButton,
            !selectedAnswer && styles.submitButtonDisabled,
          ]}
          onPress={submitAnswer}
          disabled={!selectedAnswer}
        >
          <Text style={styles.submitButtonText}>
            {currentQuestionIndex === currentQuiz.questions.length - 1 ? 'Finish' : 'Next'}
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#F9FAFB',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#6B7280',
  },
  header: {
    padding: 40,
    paddingTop: 80,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 32,
    fontWeight: 'bold',
    color: 'white',
    marginBottom: 8,
  },
  headerSubtitle: {
    fontSize: 18,
    color: 'rgba(255, 255, 255, 0.9)',
  },
  statsContainer: {
    margin: 20,
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  statsTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1F2937',
    marginBottom: 16,
  },
  statsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  statItem: {
    alignItems: 'center',
  },
  statNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#22C55E',
  },
  statLabel: {
    fontSize: 12,
    color: '#6B7280',
    marginTop: 4,
    textAlign: 'center',
  },
  optionsContainer: {
    padding: 20,
    gap: 16,
  },
  quizOption: {
    borderRadius: 16,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 6,
  },
  optionGradient: {
    padding: 24,
    alignItems: 'center',
  },
  optionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: 'white',
    marginTop: 12,
    marginBottom: 8,
  },
  optionDescription: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.9)',
    textAlign: 'center',
  },
  featuresContainer: {
    margin: 20,
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 20,
  },
  featuresTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1F2937',
    marginBottom: 16,
  },
  featuresList: {
    gap: 12,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  featureText: {
    fontSize: 16,
    color: '#4B5563',
  },
  progressContainer: {
    padding: 20,
    paddingTop: 60,
  },
  progressBar: {
    height: 8,
    backgroundColor: '#E5E7EB',
    borderRadius: 4,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#22C55E',
  },
  progressText: {
    fontSize: 14,
    color: '#6B7280',
    textAlign: 'center',
    marginTop: 8,
  },
  questionContainer: {
    flex: 1,
    padding: 20,
  },
  questionText: {
    fontSize: 20,
    fontWeight: '600',
    color: '#1F2937',
    lineHeight: 28,
    marginBottom: 32,
  },
  answerOption: {
    backgroundColor: 'white',
    padding: 20,
    borderRadius: 12,
    marginBottom: 12,
    borderWidth: 2,
    borderColor: '#E5E7EB',
  },
  selectedOption: {
    borderColor: '#22C55E',
    backgroundColor: '#F0FDF4',
  },
  answerText: {
    fontSize: 16,
    color: '#4B5563',
  },
  selectedAnswerText: {
    color: '#22C55E',
    fontWeight: '600',
  },
  submitContainer: {
    padding: 20,
  },
  submitButton: {
    backgroundColor: '#22C55E',
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
  },
  submitButtonDisabled: {
    backgroundColor: '#9CA3AF',
  },
  submitButtonText: {
    color: 'white',
    fontSize: 18,
    fontWeight: '600',
  },
  resultContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  resultTitle: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#1F2937',
    marginBottom: 32,
  },
  scoreContainer: {
    alignItems: 'center',
    marginBottom: 40,
  },
  scoreText: {
    fontSize: 64,
    fontWeight: 'bold',
  },
  scoreSubtext: {
    fontSize: 18,
    color: '#6B7280',
    marginTop: 8,
  },
  resultActions: {
    flexDirection: 'row',
    gap: 16,
  },
  actionButton: {
    backgroundColor: '#22C55E',
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingVertical: 16,
    borderRadius: 12,
    gap: 8,
  },
  secondaryButton: {
    backgroundColor: 'white',
    borderWidth: 1,
    borderColor: '#22C55E',
  },
  actionButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  secondaryButtonText: {
    color: '#22C55E',
  },
});
