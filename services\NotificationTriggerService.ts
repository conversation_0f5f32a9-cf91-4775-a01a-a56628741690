import { PushNotificationService } from './PushNotificationService';
import { supabase } from '../lib/supabase';

/**
 * NotificationTriggerService - Handles triggering notifications based on user actions
 * This service integrates with the main app features to send contextual notifications
 */
export class NotificationTriggerService {
  private static instance: NotificationTriggerService;
  private pushService: PushNotificationService;

  private constructor() {
    this.pushService = PushNotificationService.getInstance();
  }

  static getInstance(): NotificationTriggerService {
    if (!NotificationTriggerService.instance) {
      NotificationTriggerService.instance = new NotificationTriggerService();
    }
    return NotificationTriggerService.instance;
  }

  /**
   * Trigger achievement notification when user identifies a new species
   */
  async onNewSpeciesIdentified(speciesData: {
    speciesName: string;
    commonName?: string;
    isFirstTime: boolean;
    totalIdentifications: number;
    userId: string;
  }): Promise<void> {
    try {
      const { speciesName, commonName, isFirstTime, totalIdentifications, userId } = speciesData;

      if (isFirstTime) {
        // Send new species discovery notification
        await this.pushService.sendPersonalizedNotification(
          'new_species_discovered',
          {
            species_name: commonName || speciesName,
            scientific_name: speciesName,
            total_count: totalIdentifications,
          }
        );

        // Log achievement in database
        await this.logAchievement(userId, 'new_species', {
          species_name: speciesName,
          common_name: commonName,
        });
      }

      // Check for milestone achievements
      await this.checkMilestoneAchievements(userId, totalIdentifications);

    } catch (error) {
      console.error('Error triggering new species notification:', error);
    }
  }

  /**
   * Trigger streak notification when user maintains identification streak
   */
  async onIdentificationStreak(streakData: {
    streakCount: number;
    userId: string;
    lastIdentificationDate: string;
  }): Promise<void> {
    try {
      const { streakCount, userId } = streakData;

      // Only send notifications for significant streaks
      if (streakCount >= 3 && (streakCount % 7 === 0 || streakCount % 30 === 0)) {
        await this.pushService.sendPersonalizedNotification(
          'identification_streak',
          {
            streak_count: streakCount,
            milestone: streakCount % 30 === 0 ? 'monthly' : 'weekly',
          }
        );

        // Log streak achievement
        await this.logAchievement(userId, 'streak', {
          streak_count: streakCount,
        });
      }

    } catch (error) {
      console.error('Error triggering streak notification:', error);
    }
  }

  /**
   * Trigger location-based reminder notifications
   */
  async onLocationUpdate(locationData: {
    latitude: number;
    longitude: number;
    userId: string;
  }): Promise<void> {
    try {
      const { latitude, longitude, userId } = locationData;

      // Check if user has location-based notifications enabled
      const preferences = await this.pushService.getNotificationPreferences();
      if (!preferences?.location_based_content || !preferences?.reminders_enabled) {
        return;
      }

      // Find nearby species that user hasn't identified yet
      const nearbySpecies = await this.findNearbyUnidentifiedSpecies(
        latitude,
        longitude,
        userId
      );

      if (nearbySpecies.length > 0) {
        // Don't spam - only send if last location reminder was > 24 hours ago
        const lastReminder = await this.getLastLocationReminder(userId);
        const now = new Date();
        const dayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);

        if (!lastReminder || new Date(lastReminder) < dayAgo) {
          await this.pushService.sendPersonalizedNotification(
            'location_reminder',
            {
              species_count: nearbySpecies.length,
              location_name: await this.getLocationName(latitude, longitude),
              top_species: nearbySpecies.slice(0, 3).map(s => s.common_name).join(', '),
            }
          );

          // Record that we sent a location reminder
          await this.recordLocationReminder(userId, latitude, longitude);
        }
      }

    } catch (error) {
      console.error('Error triggering location notification:', error);
    }
  }

  /**
   * Trigger community notifications for social features
   */
  async onCommunityActivity(activityData: {
    type: 'rare_sighting' | 'challenge_completed' | 'leaderboard_change';
    userId: string;
    data: any;
  }): Promise<void> {
    try {
      const { type, userId, data } = activityData;

      // Check if user has social notifications enabled
      const preferences = await this.pushService.getNotificationPreferences();
      if (!preferences?.social_enabled) {
        return;
      }

      switch (type) {
        case 'rare_sighting':
          await this.pushService.sendPersonalizedNotification(
            'community_highlight',
            {
              user_name: data.userName,
              species_name: data.speciesName,
              location: data.location,
              rarity_score: data.rarityScore,
            }
          );
          break;

        case 'challenge_completed':
          // Send to friends/followers
          await this.notifyFollowers(userId, 'challenge_completed', data);
          break;

        case 'leaderboard_change':
          if (data.newRank <= 10) {
            await this.pushService.sendPersonalizedNotification(
              'achievement',
              {
                achievement_type: 'leaderboard',
                rank: data.newRank,
                category: data.category,
              }
            );
          }
          break;
      }

    } catch (error) {
      console.error('Error triggering community notification:', error);
    }
  }

  /**
   * Schedule reminder notifications for inactive users
   */
  async scheduleInactivityReminders(userId: string): Promise<void> {
    try {
      const lastActivity = await this.getLastUserActivity(userId);
      const now = new Date();
      const daysSinceActivity = Math.floor(
        (now.getTime() - new Date(lastActivity).getTime()) / (1000 * 60 * 60 * 24)
      );

      // Send reminders at 3, 7, and 30 days of inactivity
      if ([3, 7, 30].includes(daysSinceActivity)) {
        let reminderType = 'gentle_reminder';
        let reminderData = {};

        if (daysSinceActivity === 3) {
          reminderType = 'short_break_reminder';
          reminderData = {
            days_away: 3,
            suggestion: 'Take a quick nature walk and identify something new!',
          };
        } else if (daysSinceActivity === 7) {
          reminderType = 'weekly_reminder';
          reminderData = {
            days_away: 7,
            suggestion: 'Discover what\'s blooming in your area this week',
          };
        } else if (daysSinceActivity === 30) {
          reminderType = 'comeback_reminder';
          reminderData = {
            days_away: 30,
            suggestion: 'See what new features we\'ve added while you were away',
          };
        }

        await this.pushService.sendPersonalizedNotification(
          reminderType,
          reminderData
        );
      }

    } catch (error) {
      console.error('Error scheduling inactivity reminders:', error);
    }
  }

  /**
   * Trigger educational notifications based on user interests
   */
  async sendEducationalContent(userId: string): Promise<void> {
    try {
      const preferences = await this.pushService.getNotificationPreferences();
      if (!preferences?.educational_enabled) {
        return;
      }

      // Get user's identification history to determine interests
      const userInterests = await this.getUserInterests(userId);
      
      // Find relevant educational content
      const educationalContent = await this.getEducationalContent(userInterests);

      if (educationalContent) {
        await this.pushService.sendPersonalizedNotification(
          'educational_content',
          {
            title: educationalContent.title,
            content: educationalContent.content,
            category: educationalContent.category,
            action_url: educationalContent.url,
          }
        );
      }

    } catch (error) {
      console.error('Error sending educational notification:', error);
    }
  }

  // Private helper methods

  private async checkMilestoneAchievements(userId: string, totalCount: number): Promise<void> {
    const milestones = [10, 25, 50, 100, 250, 500, 1000];
    
    if (milestones.includes(totalCount)) {
      await this.pushService.sendPersonalizedNotification(
        'milestone_achievement',
        {
          milestone_type: 'identification_count',
          count: totalCount,
          next_milestone: milestones.find(m => m > totalCount) || 'Max level reached!',
        }
      );

      await this.logAchievement(userId, 'milestone', {
        type: 'identification_count',
        count: totalCount,
      });
    }
  }

  private async findNearbyUnidentifiedSpecies(
    lat: number,
    lng: number,
    userId: string
  ): Promise<any[]> {
    try {
      // This would query your species database for species common in the area
      // that the user hasn't identified yet
      const { data, error } = await supabase.rpc('find_nearby_species', {
        user_lat: lat,
        user_lng: lng,
        user_uuid: userId,
        radius_km: 10,
      });

      return data || [];
    } catch (error) {
      console.error('Error finding nearby species:', error);
      return [];
    }
  }

  private async getLocationName(lat: number, lng: number): Promise<string> {
    try {
      // Use reverse geocoding to get location name
      // This is a simplified version - you'd use a real geocoding service
      return `Location (${lat.toFixed(2)}, ${lng.toFixed(2)})`;
    } catch (error) {
      return 'your current location';
    }
  }

  private async getLastLocationReminder(userId: string): Promise<string | null> {
    try {
      const { data } = await supabase
        .from('notification_delivery_log')
        .select('created_at')
        .eq('user_id', userId)
        .like('template_id', '%location_reminder%')
        .order('created_at', { ascending: false })
        .limit(1)
        .single();

      return data?.created_at || null;
    } catch (error) {
      return null;
    }
  }

  private async recordLocationReminder(
    userId: string,
    lat: number,
    lng: number
  ): Promise<void> {
    try {
      await supabase
        .from('user_location_reminders')
        .insert({
          user_id: userId,
          latitude: lat,
          longitude: lng,
          sent_at: new Date().toISOString(),
        });
    } catch (error) {
      console.error('Error recording location reminder:', error);
    }
  }

  private async logAchievement(
    userId: string,
    type: string,
    data: any
  ): Promise<void> {
    try {
      await supabase
        .from('user_achievements')
        .insert({
          user_id: userId,
          achievement_type: type,
          achievement_data: data,
          earned_at: new Date().toISOString(),
        });
    } catch (error) {
      console.error('Error logging achievement:', error);
    }
  }

  private async notifyFollowers(
    userId: string,
    activityType: string,
    data: any
  ): Promise<void> {
    try {
      // Get user's followers
      const { data: followers } = await supabase
        .from('user_followers')
        .select('follower_id')
        .eq('user_id', userId);

      if (followers && followers.length > 0) {
        // Send notifications to followers
        for (const follower of followers) {
          await this.pushService.sendPersonalizedNotification(
            'social_activity',
            {
              activity_type: activityType,
              user_name: data.userName,
              ...data,
            }
          );
        }
      }
    } catch (error) {
      console.error('Error notifying followers:', error);
    }
  }

  private async getLastUserActivity(userId: string): Promise<string> {
    try {
      const { data } = await supabase
        .from('user_activity_log')
        .select('created_at')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .limit(1)
        .single();

      return data?.created_at || new Date().toISOString();
    } catch (error) {
      return new Date().toISOString();
    }
  }

  private async getUserInterests(userId: string): Promise<string[]> {
    try {
      // Analyze user's identification history to determine interests
      const { data } = await supabase
        .from('species_identifications')
        .select('species_category')
        .eq('user_id', userId)
        .limit(100);

      const categories = data?.map(d => d.species_category) || [];
      const interests = [...new Set(categories)];
      
      return interests;
    } catch (error) {
      return [];
    }
  }

  private async getEducationalContent(interests: string[]): Promise<any | null> {
    try {
      const { data } = await supabase
        .from('educational_content')
        .select('*')
        .in('category', interests)
        .eq('is_active', true)
        .order('created_at', { ascending: false })
        .limit(1)
        .single();

      return data;
    } catch (error) {
      return null;
    }
  }
}
