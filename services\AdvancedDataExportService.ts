import { supabase } from '../lib/supabase';
import * as FileSystem from 'expo-file-system';
import * as Sharing from 'expo-sharing';
import { AdvancedPrivacyService } from './AdvancedPrivacyService';

export interface ExportOptions {
  format: 'json' | 'csv' | 'xml';
  includeMetadata: boolean;
  includeDeleted: boolean;
  dateRange?: {
    start: string;
    end: string;
  };
  categories?: string[];
  compression?: boolean;
  encryption?: boolean;
}

export interface ExportResult {
  success: boolean;
  filePath?: string;
  fileSize?: number;
  recordCount?: number;
  error?: string;
  exportId?: string;
}

export interface DataCategory {
  name: string;
  description: string;
  tables: string[];
  sensitivityLevel: 'low' | 'medium' | 'high' | 'critical';
  retentionPeriod?: string;
}

export class AdvancedDataExportService {
  private static instance: AdvancedDataExportService;
  private privacyService: AdvancedPrivacyService;

  private readonly DATA_CATEGORIES: DataCategory[] = [
    {
      name: 'profile_data',
      description: 'User profile and account information',
      tables: ['user_profiles', 'user_preferences'],
      sensitivityLevel: 'medium',
    },
    {
      name: 'identification_data',
      description: 'Species identification history and results',
      tables: ['species_identifications', 'identification_history'],
      sensitivityLevel: 'low',
    },
    {
      name: 'location_data',
      description: 'Location information and GPS coordinates',
      tables: ['user_locations', 'location_history'],
      sensitivityLevel: 'high',
    },
    {
      name: 'behavioral_data',
      description: 'App usage patterns and analytics',
      tables: ['user_activity_log', 'app_usage_analytics'],
      sensitivityLevel: 'medium',
    },
    {
      name: 'privacy_data',
      description: 'Privacy settings and consent records',
      tables: ['privacy_consents', 'advanced_privacy_preferences', 'data_access_log'],
      sensitivityLevel: 'high',
    },
    {
      name: 'communication_data',
      description: 'Messages, notifications, and communications',
      tables: ['user_messages', 'notification_delivery_log'],
      sensitivityLevel: 'medium',
    },
    {
      name: 'payment_data',
      description: 'Subscription and payment information',
      tables: ['user_subscriptions', 'payment_history'],
      sensitivityLevel: 'critical',
    },
    {
      name: 'biometric_data',
      description: 'Biometric identifiers and related data',
      tables: ['biometric_data', 'device_fingerprints'],
      sensitivityLevel: 'critical',
    },
  ];

  static getInstance(): AdvancedDataExportService {
    if (!AdvancedDataExportService.instance) {
      AdvancedDataExportService.instance = new AdvancedDataExportService();
    }
    return AdvancedDataExportService.instance;
  }

  constructor() {
    this.privacyService = AdvancedPrivacyService.getInstance();
  }

  /**
   * Get available data categories for export
   */
  getDataCategories(): DataCategory[] {
    return this.DATA_CATEGORIES;
  }

  /**
   * Estimate export size and record count
   */
  async estimateExportSize(options: ExportOptions): Promise<{
    estimatedSize: number;
    recordCount: number;
    categories: { [key: string]: number };
  }> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      const categories = options.categories || this.DATA_CATEGORIES.map(c => c.name);
      const categoryRecords: { [key: string]: number } = {};
      let totalRecords = 0;

      for (const categoryName of categories) {
        const category = this.DATA_CATEGORIES.find(c => c.name === categoryName);
        if (!category) continue;

        let categoryCount = 0;
        for (const table of category.tables) {
          try {
            let query = supabase
              .from(table)
              .select('*', { count: 'exact', head: true })
              .eq('user_id', user.id);

            // Apply date range filter if specified
            if (options.dateRange) {
              query = query
                .gte('created_at', options.dateRange.start)
                .lte('created_at', options.dateRange.end);
            }

            const { count } = await query;
            categoryCount += count || 0;
          } catch (error) {
            console.warn(`Error counting records for table ${table}:`, error);
          }
        }

        categoryRecords[categoryName] = categoryCount;
        totalRecords += categoryCount;
      }

      // Estimate file size (rough calculation)
      const avgRecordSize = 1024; // 1KB per record average
      const estimatedSize = totalRecords * avgRecordSize;

      return {
        estimatedSize,
        recordCount: totalRecords,
        categories: categoryRecords,
      };
    } catch (error) {
      console.error('Error estimating export size:', error);
      throw error;
    }
  }

  /**
   * Export user data with advanced options
   */
  async exportUserData(options: ExportOptions): Promise<ExportResult> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      // Check user consent for data export
      const hasConsent = await this.privacyService.hasValidConsent('data_export');
      if (!hasConsent) {
        throw new Error('User consent required for data export');
      }

      // Log the export request
      await this.privacyService.logDataAccess({
        access_type: 'export',
        data_category: 'all_user_data',
        accessor_type: 'user',
        access_reason: 'User-initiated data export',
      });

      // Generate unique export ID
      const exportId = `export_${user.id}_${Date.now()}`;

      // Collect data from all requested categories
      const exportData = await this.collectUserData(user.id, options);

      // Format data according to requested format
      const formattedData = await this.formatExportData(exportData, options);

      // Create export file
      const filePath = await this.createExportFile(formattedData, options, exportId);

      // Get file stats
      const fileInfo = await FileSystem.getInfoAsync(filePath);
      const fileSize = fileInfo.exists ? fileInfo.size : 0;

      // Record export in database
      await this.recordExportActivity(user.id, exportId, options, fileSize);

      return {
        success: true,
        filePath,
        fileSize,
        recordCount: this.countRecords(exportData),
        exportId,
      };
    } catch (error) {
      console.error('Error exporting user data:', error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Share exported data file
   */
  async shareExportFile(filePath: string): Promise<boolean> {
    try {
      const isAvailable = await Sharing.isAvailableAsync();
      if (!isAvailable) {
        throw new Error('Sharing is not available on this device');
      }

      await Sharing.shareAsync(filePath, {
        mimeType: 'application/octet-stream',
        dialogTitle: 'Share Your Data Export',
      });

      return true;
    } catch (error) {
      console.error('Error sharing export file:', error);
      return false;
    }
  }

  /**
   * Get export history for user
   */
  async getExportHistory(): Promise<any[]> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      const { data, error } = await supabase
        .from('data_export_log')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false })
        .limit(20);

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error getting export history:', error);
      return [];
    }
  }

  /**
   * Schedule automatic data export
   */
  async scheduleAutoExport(
    frequency: 'weekly' | 'monthly' | 'quarterly',
    options: ExportOptions
  ): Promise<boolean> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      // Update user preferences to enable auto export
      await this.privacyService.updateAdvancedPrivacyPreferences({
        auto_export_enabled: true,
        export_frequency: frequency,
        export_format: options.format,
      });

      // Schedule the export (this would typically be handled by a background job)
      console.log(`Scheduled auto export for user ${user.id} with frequency: ${frequency}`);

      return true;
    } catch (error) {
      console.error('Error scheduling auto export:', error);
      return false;
    }
  }

  /**
   * Validate export request against privacy preferences
   */
  async validateExportRequest(options: ExportOptions): Promise<{
    valid: boolean;
    warnings: string[];
    restrictions: string[];
  }> {
    try {
      const warnings: string[] = [];
      const restrictions: string[] = [];

      // Check privacy preferences
      const preferences = await this.privacyService.getAdvancedPrivacyPreferences();

      // Check if sensitive data categories are included
      const sensitiveCategories = options.categories?.filter(cat => {
        const category = this.DATA_CATEGORIES.find(c => c.name === cat);
        return category && ['high', 'critical'].includes(category.sensitivityLevel);
      }) || [];

      if (sensitiveCategories.length > 0) {
        warnings.push(`Export includes sensitive data categories: ${sensitiveCategories.join(', ')}`);
      }

      // Check if biometric data is included
      if (options.categories?.includes('biometric_data') && !preferences?.allow_biometric_processing) {
        restrictions.push('Biometric data export requires explicit consent');
      }

      // Check if location data is included with high precision
      if (options.categories?.includes('location_data') && preferences?.location_precision_level === 'none') {
        restrictions.push('Location data export restricted by user preferences');
      }

      // Check export frequency limits
      const recentExports = await this.getExportHistory();
      const recentExportsCount = recentExports.filter(exp => 
        new Date(exp.created_at) > new Date(Date.now() - 24 * 60 * 60 * 1000)
      ).length;

      if (recentExportsCount >= 3) {
        restrictions.push('Daily export limit reached (maximum 3 exports per day)');
      }

      return {
        valid: restrictions.length === 0,
        warnings,
        restrictions,
      };
    } catch (error) {
      console.error('Error validating export request:', error);
      return {
        valid: false,
        warnings: [],
        restrictions: ['Error validating export request'],
      };
    }
  }

  // Private helper methods

  private async collectUserData(userId: string, options: ExportOptions): Promise<any> {
    const exportData: any = {
      metadata: {
        exportId: `export_${userId}_${Date.now()}`,
        exportDate: new Date().toISOString(),
        userId,
        format: options.format,
        includeMetadata: options.includeMetadata,
        includeDeleted: options.includeDeleted,
        dateRange: options.dateRange,
        categories: options.categories,
      },
      data: {},
    };

    const categories = options.categories || this.DATA_CATEGORIES.map(c => c.name);

    for (const categoryName of categories) {
      const category = this.DATA_CATEGORIES.find(c => c.name === categoryName);
      if (!category) continue;

      exportData.data[categoryName] = {};

      for (const table of category.tables) {
        try {
          let query = supabase
            .from(table)
            .select('*')
            .eq('user_id', userId);

          // Apply date range filter
          if (options.dateRange) {
            query = query
              .gte('created_at', options.dateRange.start)
              .lte('created_at', options.dateRange.end);
          }

          // Include deleted records if requested
          if (!options.includeDeleted) {
            query = query.is('deleted_at', null);
          }

          const { data, error } = await query;
          if (error) throw error;

          exportData.data[categoryName][table] = data || [];
        } catch (error) {
          console.warn(`Error collecting data from table ${table}:`, error);
          exportData.data[categoryName][table] = [];
        }
      }
    }

    return exportData;
  }

  private async formatExportData(data: any, options: ExportOptions): Promise<string> {
    switch (options.format) {
      case 'json':
        return JSON.stringify(data, null, 2);
      case 'csv':
        return this.convertToCSV(data);
      case 'xml':
        return this.convertToXML(data);
      default:
        throw new Error(`Unsupported export format: ${options.format}`);
    }
  }

  private convertToCSV(data: any): string {
    // Simplified CSV conversion - in production, you'd want a more robust implementation
    let csv = 'Category,Table,Field,Value\n';
    
    for (const [categoryName, categoryData] of Object.entries(data.data)) {
      for (const [tableName, tableData] of Object.entries(categoryData as any)) {
        if (Array.isArray(tableData)) {
          for (const record of tableData) {
            for (const [field, value] of Object.entries(record)) {
              csv += `"${categoryName}","${tableName}","${field}","${value}"\n`;
            }
          }
        }
      }
    }
    
    return csv;
  }

  private convertToXML(data: any): string {
    // Simplified XML conversion
    let xml = '<?xml version="1.0" encoding="UTF-8"?>\n<export>\n';
    xml += `  <metadata>\n`;
    xml += `    <exportId>${data.metadata.exportId}</exportId>\n`;
    xml += `    <exportDate>${data.metadata.exportDate}</exportDate>\n`;
    xml += `    <userId>${data.metadata.userId}</userId>\n`;
    xml += `  </metadata>\n`;
    xml += `  <data>\n`;
    
    for (const [categoryName, categoryData] of Object.entries(data.data)) {
      xml += `    <category name="${categoryName}">\n`;
      for (const [tableName, tableData] of Object.entries(categoryData as any)) {
        xml += `      <table name="${tableName}">\n`;
        if (Array.isArray(tableData)) {
          for (const record of tableData) {
            xml += `        <record>\n`;
            for (const [field, value] of Object.entries(record)) {
              xml += `          <${field}>${value}</${field}>\n`;
            }
            xml += `        </record>\n`;
          }
        }
        xml += `      </table>\n`;
      }
      xml += `    </category>\n`;
    }
    
    xml += `  </data>\n</export>`;
    return xml;
  }

  private async createExportFile(
    formattedData: string,
    options: ExportOptions,
    exportId: string
  ): Promise<string> {
    const fileExtension = options.format;
    const fileName = `bioscan_data_export_${exportId}.${fileExtension}`;
    const filePath = `${FileSystem.documentDirectory}${fileName}`;

    await FileSystem.writeAsStringAsync(filePath, formattedData, {
      encoding: FileSystem.EncodingType.UTF8,
    });

    return filePath;
  }

  private countRecords(exportData: any): number {
    let count = 0;
    for (const categoryData of Object.values(exportData.data)) {
      for (const tableData of Object.values(categoryData as any)) {
        if (Array.isArray(tableData)) {
          count += tableData.length;
        }
      }
    }
    return count;
  }

  private async recordExportActivity(
    userId: string,
    exportId: string,
    options: ExportOptions,
    fileSize: number
  ): Promise<void> {
    try {
      await supabase
        .from('data_export_log')
        .insert({
          user_id: userId,
          export_id: exportId,
          export_format: options.format,
          export_options: options,
          file_size: fileSize,
          record_count: 0, // Would be calculated
          status: 'completed',
        });
    } catch (error) {
      console.error('Error recording export activity:', error);
    }
  }
}
