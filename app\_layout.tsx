import { useEffect, useState, useRef } from 'react';
import { Stack } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { useFrameworkReady } from '@/hooks/useFrameworkReady';
import { useFonts } from 'expo-font';
import {
  Inter_400Regular,
  Inter_500Medium,
  Inter_600SemiBold,
  Inter_700Bold
} from '@expo-google-fonts/inter';
import * as SplashScreen from 'expo-splash-screen';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { SettingsProvider } from '@/components/SettingsContext';
import { SubscriptionProvider } from '@/components/SubscriptionContext';
import OnboardingWalkthrough from '@/components/OnboardingWalkthrough';

SplashScreen.preventAutoHideAsync();

export default function RootLayout() {
  useFrameworkReady();
  
  const [showOnboarding, setShowOnboarding] = useState(false);
  const [isCheckingOnboarding, setIsCheckingOnboarding] = useState(true);
  const isMounted = useRef(true);

  const [fontsLoaded, fontError] = useFonts({
    'Inter-Regular': Inter_400Regular,
    'Inter-Medium': Inter_500Medium,
    'Inter-SemiBold': Inter_600SemiBold,
    'Inter-Bold': Inter_700Bold,
  });

  useEffect(() => {
    isMounted.current = true;
    return () => {
      isMounted.current = false;
    };
  }, []);

  useEffect(() => {
    checkOnboardingStatus();
  }, []);

  useEffect(() => {
    if ((fontsLoaded || fontError) && !isCheckingOnboarding) {
      SplashScreen.hideAsync();
    }
  }, [fontsLoaded, fontError, isCheckingOnboarding]);

  const checkOnboardingStatus = async () => {
    try {
      const hasCompletedOnboarding = await AsyncStorage.getItem('hasCompletedOnboarding');
      if (isMounted.current) {
        setShowOnboarding(!hasCompletedOnboarding);
      }
    } catch (error) {
      console.error('Failed to check onboarding status:', error);
      if (isMounted.current) {
        setShowOnboarding(true); // Show onboarding by default if check fails
      }
    } finally {
      if (isMounted.current) {
        setIsCheckingOnboarding(false);
      }
    }
  };

  const handleOnboardingComplete = async () => {
    try {
      await AsyncStorage.setItem('hasCompletedOnboarding', 'true');
      if (isMounted.current) {
        setShowOnboarding(false);
      }
    } catch (error) {
      console.error('Failed to save onboarding completion:', error);
    }
  };

  if (!fontsLoaded && !fontError) {
    return null;
  }

  if (isCheckingOnboarding) {
    return null;
  }

  if (showOnboarding) {
    return (
      <OnboardingWalkthrough onComplete={handleOnboardingComplete} />
    );
  }

  return (
    <SubscriptionProvider>
      <SettingsProvider>
        <Stack screenOptions={{ headerShown: false }}>
          <Stack.Screen name="(tabs)" />
          <Stack.Screen name="scan-result" />
          <Stack.Screen name="+not-found" />
        </Stack>
        <StatusBar style="auto" />
      </SettingsProvider>
    </SubscriptionProvider>
  );
}