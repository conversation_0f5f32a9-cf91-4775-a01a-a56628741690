global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "alert_rules.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

scrape_configs:
  # Prometheus itself
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # API Gateway
  - job_name: 'api-gateway'
    static_configs:
      - targets: ['api-gateway:3000']
    metrics_path: '/metrics'
    scrape_interval: 10s

  # Auth Service
  - job_name: 'auth-service'
    static_configs:
      - targets: ['auth-service:3001']
    metrics_path: '/metrics'
    scrape_interval: 15s

  # Media Service
  - job_name: 'media-service'
    static_configs:
      - targets: ['media-service:3002']
    metrics_path: '/metrics'
    scrape_interval: 15s

  # Notification Service
  - job_name: 'notification-service'
    static_configs:
      - targets: ['notification-service:3003']
    metrics_path: '/metrics'
    scrape_interval: 15s

  # Payment Service
  - job_name: 'payment-service'
    static_configs:
      - targets: ['payment-service:3004']
    metrics_path: '/metrics'
    scrape_interval: 15s

  # Analytics Service
  - job_name: 'analytics-service'
    static_configs:
      - targets: ['analytics-service:3005']
    metrics_path: '/metrics'
    scrape_interval: 15s

  # PostgreSQL Exporter
  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres-exporter:9187']
    scrape_interval: 30s

  # Redis Exporter
  - job_name: 'redis'
    static_configs:
      - targets: ['redis-exporter:9121']
    scrape_interval: 30s

  # Node Exporter (system metrics)
  - job_name: 'node'
    static_configs:
      - targets: ['node-exporter:9100']
    scrape_interval: 30s

  # Nginx Exporter
  - job_name: 'nginx'
    static_configs:
      - targets: ['nginx-exporter:9113']
    scrape_interval: 30s
