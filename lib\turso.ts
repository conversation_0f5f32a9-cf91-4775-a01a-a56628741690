import { createClient, Client } from '@libsql/client';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Turso configuration
const TURSO_URL = process.env.EXPO_PUBLIC_TURSO_URL || 'file:local.db';
const TURSO_AUTH_TOKEN = process.env.EXPO_PUBLIC_TURSO_AUTH_TOKEN;

export interface TursoConfig {
  url: string;
  authToken?: string;
  syncUrl?: string;
  syncInterval?: number;
}

export class TursoService {
  private static instance: TursoService;
  private client: Client | null = null;
  private isInitialized = false;
  private syncQueue: Array<{ query: string; params?: any[] }> = [];
  private isOnline = true;

  private constructor() {}

  public static getInstance(): TursoService {
    if (!TursoService.instance) {
      TursoService.instance = new TursoService();
    }
    return TursoService.instance;
  }

  public async initialize(config?: TursoConfig): Promise<void> {
    if (this.isInitialized) return;

    try {
      const clientConfig = {
        url: config?.url || TURSO_URL,
        authToken: config?.authToken || TURSO_AUTH_TOKEN,
      };

      this.client = createClient(clientConfig);
      
      // Initialize database schema
      await this.initializeSchema();
      
      // Set up sync mechanism if remote URL is provided
      if (config?.syncUrl) {
        this.setupSync(config.syncUrl, config.syncInterval || 30000);
      }

      this.isInitialized = true;
      console.log('Turso service initialized successfully');
    } catch (error) {
      console.error('Failed to initialize Turso service:', error);
      throw error;
    }
  }

  private async initializeSchema(): Promise<void> {
    if (!this.client) throw new Error('Turso client not initialized');

    const schemaSql = `
      -- User preferences and settings
      CREATE TABLE IF NOT EXISTS user_preferences (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        key TEXT NOT NULL,
        value TEXT NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        UNIQUE(user_id, key)
      );

      -- App settings and configuration
      CREATE TABLE IF NOT EXISTS app_settings (
        id TEXT PRIMARY KEY,
        key TEXT UNIQUE NOT NULL,
        value TEXT NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      );

      -- Offline cached identification data
      CREATE TABLE IF NOT EXISTS cached_identifications (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        image_data TEXT NOT NULL,
        result_data TEXT,
        status TEXT DEFAULT 'pending',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        synced_at DATETIME
      );

      -- Search history and saved searches
      CREATE TABLE IF NOT EXISTS search_history (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        query TEXT NOT NULL,
        filters TEXT,
        results_count INTEGER DEFAULT 0,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
      );

      -- Saved searches
      CREATE TABLE IF NOT EXISTS saved_searches (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        name TEXT NOT NULL,
        query TEXT NOT NULL,
        filters TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      );

      -- Offline sync queue
      CREATE TABLE IF NOT EXISTS sync_queue (
        id TEXT PRIMARY KEY,
        operation_type TEXT NOT NULL,
        table_name TEXT NOT NULL,
        data TEXT NOT NULL,
        status TEXT DEFAULT 'pending',
        retry_count INTEGER DEFAULT 0,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      );

      -- Create indexes for better performance
      CREATE INDEX IF NOT EXISTS idx_user_preferences_user_id ON user_preferences(user_id);
      CREATE INDEX IF NOT EXISTS idx_cached_identifications_user_id ON cached_identifications(user_id);
      CREATE INDEX IF NOT EXISTS idx_cached_identifications_status ON cached_identifications(status);
      CREATE INDEX IF NOT EXISTS idx_search_history_user_id ON search_history(user_id);
      CREATE INDEX IF NOT EXISTS idx_saved_searches_user_id ON saved_searches(user_id);
      CREATE INDEX IF NOT EXISTS idx_sync_queue_status ON sync_queue(status);
    `;

    await this.client.executeMultiple(schemaSql);
  }

  // User Preferences Management
  public async setUserPreference(userId: string, key: string, value: any): Promise<void> {
    if (!this.client) throw new Error('Turso client not initialized');

    const sql = `
      INSERT OR REPLACE INTO user_preferences (id, user_id, key, value, updated_at)
      VALUES (?, ?, ?, ?, CURRENT_TIMESTAMP)
    `;
    
    const id = `${userId}_${key}`;
    await this.client.execute(sql, [id, userId, key, JSON.stringify(value)]);
    
    // Add to sync queue if online
    if (this.isOnline) {
      await this.addToSyncQueue('user_preferences', 'upsert', { userId, key, value });
    }
  }

  public async getUserPreference(userId: string, key: string): Promise<any> {
    if (!this.client) throw new Error('Turso client not initialized');

    const sql = 'SELECT value FROM user_preferences WHERE user_id = ? AND key = ?';
    const result = await this.client.execute(sql, [userId, key]);
    
    if (result.rows.length > 0) {
      return JSON.parse(result.rows[0].value as string);
    }
    return null;
  }

  public async getAllUserPreferences(userId: string): Promise<Record<string, any>> {
    if (!this.client) throw new Error('Turso client not initialized');

    const sql = 'SELECT key, value FROM user_preferences WHERE user_id = ?';
    const result = await this.client.execute(sql, [userId]);
    
    const preferences: Record<string, any> = {};
    result.rows.forEach(row => {
      preferences[row.key as string] = JSON.parse(row.value as string);
    });
    
    return preferences;
  }

  // App Settings Management
  public async setAppSetting(key: string, value: any): Promise<void> {
    if (!this.client) throw new Error('Turso client not initialized');

    const sql = `
      INSERT OR REPLACE INTO app_settings (id, key, value, updated_at)
      VALUES (?, ?, ?, CURRENT_TIMESTAMP)
    `;
    
    await this.client.execute(sql, [key, key, JSON.stringify(value)]);
  }

  public async getAppSetting(key: string): Promise<any> {
    if (!this.client) throw new Error('Turso client not initialized');

    const sql = 'SELECT value FROM app_settings WHERE key = ?';
    const result = await this.client.execute(sql, [key]);
    
    if (result.rows.length > 0) {
      return JSON.parse(result.rows[0].value as string);
    }
    return null;
  }

  // Cached Identifications Management
  public async cacheIdentification(userId: string, imageData: string, resultData?: any): Promise<string> {
    if (!this.client) throw new Error('Turso client not initialized');

    const id = `cache_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const sql = `
      INSERT INTO cached_identifications (id, user_id, image_data, result_data, status)
      VALUES (?, ?, ?, ?, ?)
    `;
    
    const status = resultData ? 'completed' : 'pending';
    await this.client.execute(sql, [id, userId, imageData, JSON.stringify(resultData), status]);
    
    return id;
  }

  public async getCachedIdentifications(userId: string, status?: string): Promise<any[]> {
    if (!this.client) throw new Error('Turso client not initialized');

    let sql = 'SELECT * FROM cached_identifications WHERE user_id = ?';
    const params = [userId];
    
    if (status) {
      sql += ' AND status = ?';
      params.push(status);
    }
    
    sql += ' ORDER BY created_at DESC';
    
    const result = await this.client.execute(sql, params);
    return result.rows.map(row => ({
      ...row,
      result_data: row.result_data ? JSON.parse(row.result_data as string) : null
    }));
  }

  // Search History Management
  public async addSearchHistory(userId: string, query: string, filters?: any, resultsCount?: number): Promise<void> {
    if (!this.client) throw new Error('Turso client not initialized');

    const id = `search_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const sql = `
      INSERT INTO search_history (id, user_id, query, filters, results_count)
      VALUES (?, ?, ?, ?, ?)
    `;
    
    await this.client.execute(sql, [id, userId, query, JSON.stringify(filters), resultsCount || 0]);
  }

  public async getSearchHistory(userId: string, limit: number = 50): Promise<any[]> {
    if (!this.client) throw new Error('Turso client not initialized');

    const sql = `
      SELECT * FROM search_history 
      WHERE user_id = ? 
      ORDER BY created_at DESC 
      LIMIT ?
    `;
    
    const result = await this.client.execute(sql, [userId, limit]);
    return result.rows.map(row => ({
      ...row,
      filters: row.filters ? JSON.parse(row.filters as string) : null
    }));
  }

  // Sync Queue Management
  private async addToSyncQueue(tableName: string, operationType: string, data: any): Promise<void> {
    if (!this.client) return;

    const id = `sync_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const sql = `
      INSERT INTO sync_queue (id, operation_type, table_name, data)
      VALUES (?, ?, ?, ?)
    `;
    
    await this.client.execute(sql, [id, operationType, tableName, JSON.stringify(data)]);
  }

  private setupSync(syncUrl: string, interval: number): void {
    setInterval(async () => {
      if (this.isOnline) {
        await this.processSyncQueue();
      }
    }, interval);
  }

  private async processSyncQueue(): Promise<void> {
    if (!this.client) return;

    const sql = 'SELECT * FROM sync_queue WHERE status = ? ORDER BY created_at ASC LIMIT 10';
    const result = await this.client.execute(sql, ['pending']);
    
    for (const row of result.rows) {
      try {
        // Process sync operation (implement based on your sync logic)
        await this.processSyncOperation(row);
        
        // Mark as completed
        await this.client.execute(
          'UPDATE sync_queue SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
          ['completed', row.id]
        );
      } catch (error) {
        console.error('Sync operation failed:', error);
        
        // Increment retry count
        await this.client.execute(
          'UPDATE sync_queue SET retry_count = retry_count + 1, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
          [row.id]
        );
      }
    }
  }

  private async processSyncOperation(operation: any): Promise<void> {
    // Implement sync logic based on operation type and table
    // This will be expanded based on specific sync requirements
    console.log('Processing sync operation:', operation);
  }

  // Network state management
  public setOnlineStatus(isOnline: boolean): void {
    this.isOnline = isOnline;
    if (isOnline) {
      this.processSyncQueue();
    }
  }

  // Cleanup and maintenance
  public async cleanup(): Promise<void> {
    if (!this.client) return;

    // Clean up old search history (keep last 1000 entries per user)
    await this.client.execute(`
      DELETE FROM search_history 
      WHERE id NOT IN (
        SELECT id FROM search_history 
        ORDER BY created_at DESC 
        LIMIT 1000
      )
    `);

    // Clean up completed sync queue items older than 7 days
    await this.client.execute(`
      DELETE FROM sync_queue 
      WHERE status = 'completed' 
      AND created_at < datetime('now', '-7 days')
    `);
  }
}

export const tursoService = TursoService.getInstance();
