import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  Dimensions,
  Alert,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { BookOpen, Grid3x3 as Grid3X3, List, Filter, Search, Star, Calendar, MapPin, Award, TrendingUp, Leaf, Bug, Bird, Fish, Mountain, Flower, Plus, Eye } from 'lucide-react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import HapticService from '@/components/HapticService';

const { width } = Dimensions.get('window');

interface Specimen {
  id: number;
  name: string;
  scientificName: string;
  category: string;
  rarity: string;
  confidence: number;
  dateFound: string;
  location: string;
  isFavorite: boolean;
  icon: any;
  color: string;
}

export default function CollectionScreen() {
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [selectedFilter, setSelectedFilter] = useState('All');
  const [specimens, setSpecimens] = useState<Specimen[]>([]);
  const [loading, setLoading] = useState(true);

  const hapticService = HapticService.getInstance();

  const filterOptions = ['All', 'Plants', 'Animals', 'Insects', 'Birds', 'Favorites'];

  const collectionStats = [
    { label: 'Total Species', value: specimens.length, icon: Star, color: '#F59E0B' },
    { label: 'This Month', value: specimens.filter(s => new Date(s.dateFound).getMonth() === new Date().getMonth()).length, icon: Calendar, color: '#22C55E' },
    { label: 'Rare Finds', value: specimens.filter(s => s.rarity === 'Rare').length, icon: Award, color: '#8B5CF6' },
    { label: 'Locations', value: new Set(specimens.map(s => s.location)).size, icon: MapPin, color: '#3B82F6' },
  ];

  useEffect(() => {
    loadCollection();
  }, []);

  const loadCollection = async () => {
    try {
      const savedCollection = await AsyncStorage.getItem('userCollections');
      if (savedCollection) {
        const parsed = JSON.parse(savedCollection);
        setSpecimens(parsed);
      } else {
        // Initialize with empty collection
        setSpecimens([]);
      }
    } catch (error) {
      console.error('Failed to load collection:', error);
      setSpecimens([]);
    } finally {
      setLoading(false);
    }
  };

  const saveCollection = async (newSpecimens: Specimen[]) => {
    try {
      await AsyncStorage.setItem('userCollections', JSON.stringify(newSpecimens));
      setSpecimens(newSpecimens);
    } catch (error) {
      console.error('Failed to save collection:', error);
      Alert.alert('Error', 'Failed to save collection changes.');
    }
  };

  const toggleFavorite = async (specimenId: number) => {
    await hapticService.triggerSelectionFeedback();
    const updatedSpecimens = specimens.map(specimen =>
      specimen.id === specimenId
        ? { ...specimen, isFavorite: !specimen.isFavorite }
        : specimen
    );
    await saveCollection(updatedSpecimens);
  };

  const addSampleSpecimen = async () => {
    await hapticService.triggerImpactFeedback('medium');
    
    const sampleSpecimens: Specimen[] = [
      {
        id: Date.now(),
        name: 'Monarch Butterfly',
        scientificName: 'Danaus plexippus',
        category: 'Insects',
        rarity: 'Common',
        confidence: 98,
        dateFound: new Date().toISOString().split('T')[0],
        location: 'Central Park, NY',
        isFavorite: false,
        icon: Bug,
        color: '#F59E0B',
      },
      {
        id: Date.now() + 1,
        name: 'Red Oak',
        scientificName: 'Quercus rubra',
        category: 'Plants',
        rarity: 'Common',
        confidence: 94,
        dateFound: new Date(Date.now() - 86400000).toISOString().split('T')[0],
        location: 'Local Park',
        isFavorite: true,
        icon: Leaf,
        color: '#22C55E',
      },
    ];

    const updatedSpecimens = [...specimens, ...sampleSpecimens];
    await saveCollection(updatedSpecimens);
    Alert.alert('Success', 'Sample specimens added to your collection!');
  };

  const getRarityColor = (rarity: string) => {
    switch (rarity) {
      case 'Rare': return '#8B5CF6';
      case 'Uncommon': return '#3B82F6';
      case 'Common': return '#22C55E';
      default: return '#6B7280';
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
  };

  const filteredSpecimens = specimens.filter(specimen => {
    if (selectedFilter === 'All') return true;
    if (selectedFilter === 'Favorites') return specimen.isFavorite;
    return specimen.category === selectedFilter;
  });

  const renderGridItem = (specimen: Specimen) => {
    const IconComponent = specimen.icon;
    return (
      <TouchableOpacity 
        key={specimen.id} 
        style={styles.gridItem}
        onPress={async () => {
          await hapticService.triggerSelectionFeedback();
          Alert.alert(specimen.name, `${specimen.scientificName}\n\nConfidence: ${specimen.confidence}%\nLocation: ${specimen.location}\nDate: ${formatDate(specimen.dateFound)}`);
        }}>
        <View style={styles.gridImageContainer}>
          <LinearGradient
            colors={[specimen.color + '20', specimen.color + '10']}
            style={styles.gridImage}>
            <IconComponent size={24} color={specimen.color} />
          </LinearGradient>
          {specimen.isFavorite && (
            <TouchableOpacity 
              style={styles.favoriteIcon}
              onPress={() => toggleFavorite(specimen.id)}>
              <Star size={12} color="#F59E0B" fill="#F59E0B" />
            </TouchableOpacity>
          )}
        </View>
        <Text style={styles.gridName} numberOfLines={1}>{specimen.name}</Text>
        <Text style={styles.gridScientific} numberOfLines={1}>{specimen.scientificName}</Text>
        <View style={styles.gridMeta}>
          <View style={[styles.rarityDot, { backgroundColor: getRarityColor(specimen.rarity) }]} />
          <Text style={styles.gridRarity}>{specimen.rarity}</Text>
        </View>
      </TouchableOpacity>
    );
  };

  const renderListItem = (specimen: Specimen) => {
    const IconComponent = specimen.icon;
    return (
      <TouchableOpacity 
        key={specimen.id} 
        style={styles.listItem}
        onPress={async () => {
          await hapticService.triggerSelectionFeedback();
          Alert.alert(specimen.name, `${specimen.scientificName}\n\nConfidence: ${specimen.confidence}%\nLocation: ${specimen.location}\nDate: ${formatDate(specimen.dateFound)}`);
        }}>
        <View style={styles.listImageContainer}>
          <LinearGradient
            colors={[specimen.color + '20', specimen.color + '10']}
            style={styles.listImage}>
            <IconComponent size={20} color={specimen.color} />
          </LinearGradient>
        </View>
        
        <View style={styles.listContent}>
          <View style={styles.listHeader}>
            <Text style={styles.listName}>{specimen.name}</Text>
            {specimen.isFavorite && (
              <TouchableOpacity onPress={() => toggleFavorite(specimen.id)}>
                <Star size={14} color="#F59E0B" fill="#F59E0B" />
              </TouchableOpacity>
            )}
          </View>
          <Text style={styles.listScientific}>{specimen.scientificName}</Text>
          <View style={styles.listMeta}>
            <View style={styles.metaItem}>
              <Calendar size={12} color="#6B7280" />
              <Text style={styles.metaText}>{formatDate(specimen.dateFound)}</Text>
            </View>
            <View style={styles.metaItem}>
              <MapPin size={12} color="#6B7280" />
              <Text style={styles.metaText}>{specimen.location}</Text>
            </View>
          </View>
        </View>
        
        <View style={styles.listActions}>
          <View style={[styles.rarityBadge, { backgroundColor: getRarityColor(specimen.rarity) }]}>
            <Text style={styles.rarityBadgeText}>{specimen.rarity}</Text>
          </View>
          <Text style={styles.confidenceText}>{specimen.confidence}%</Text>
        </View>
      </TouchableOpacity>
    );
  };

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <View style={styles.emptyIcon}>
        <BookOpen size={48} color="#9CA3AF" />
      </View>
      <Text style={styles.emptyTitle}>Start Your Collection</Text>
      <Text style={styles.emptyDescription}>
        Your discovered specimens will appear here. Start scanning to build your personal nature collection!
      </Text>
      <TouchableOpacity style={styles.addSampleButton} onPress={addSampleSpecimen}>
        <LinearGradient
          colors={['#22C55E', '#16A34A']}
          style={styles.addSampleGradient}>
          <Plus size={20} color="#FFFFFF" />
          <Text style={styles.addSampleText}>Add Sample Specimens</Text>
        </LinearGradient>
      </TouchableOpacity>
    </View>
  );

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <BookOpen size={32} color="#6B7280" />
          <Text style={styles.loadingText}>Loading collection...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <LinearGradient
        colors={['#FEF3C7', '#FDE68A', '#FCD34D']}
        style={styles.gradient}>
        <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
          {/* Header */}
          <View style={styles.header}>
            <Text style={styles.title}>My Collection</Text>
            <Text style={styles.subtitle}>Your discovered specimens</Text>
          </View>

          {/* Stats Cards */}
          <View style={styles.statsContainer}>
            {collectionStats.map((stat, index) => {
              const IconComponent = stat.icon;
              return (
                <View key={index} style={styles.statCard}>
                  <IconComponent size={20} color={stat.color} />
                  <Text style={styles.statValue}>{stat.value}</Text>
                  <Text style={styles.statLabel}>{stat.label}</Text>
                </View>
              );
            })}
          </View>

          {specimens.length === 0 ? (
            renderEmptyState()
          ) : (
            <>
              {/* Search and Filters */}
              <View style={styles.controlsContainer}>
                <View style={styles.searchContainer}>
                  <Search size={18} color="#6B7280" />
                  <Text style={styles.searchPlaceholder}>Search collection...</Text>
                </View>
                
                <View style={styles.viewControls}>
                  <TouchableOpacity
                    style={[styles.viewButton, viewMode === 'grid' && styles.viewButtonActive]}
                    onPress={async () => {
                      await hapticService.triggerSelectionFeedback();
                      setViewMode('grid');
                    }}>
                    <Grid3X3 size={18} color={viewMode === 'grid' ? '#FFFFFF' : '#6B7280'} />
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={[styles.viewButton, viewMode === 'list' && styles.viewButtonActive]}
                    onPress={async () => {
                      await hapticService.triggerSelectionFeedback();
                      setViewMode('list');
                    }}>
                    <List size={18} color={viewMode === 'list' ? '#FFFFFF' : '#6B7280'} />
                  </TouchableOpacity>
                </View>
              </View>

              {/* Filter Tabs */}
              <View style={styles.filtersContainer}>
                <ScrollView horizontal showsHorizontalScrollIndicator={false}>
                  <View style={styles.filtersScrollContent}>
                    {filterOptions.map((filter) => (
                      <TouchableOpacity
                        key={filter}
                        style={[
                          styles.filterButton,
                          selectedFilter === filter && styles.filterButtonActive
                        ]}
                        onPress={async () => {
                          await hapticService.triggerSelectionFeedback();
                          setSelectedFilter(filter);
                        }}>
                        <Text
                          style={[
                            styles.filterText,
                            selectedFilter === filter && styles.filterTextActive
                          ]}>
                          {filter}
                        </Text>
                      </TouchableOpacity>
                    ))}
                  </View>
                </ScrollView>
              </View>

              {/* Collection Content */}
              <View style={styles.collectionContainer}>
                <View style={styles.collectionHeader}>
                  <Text style={styles.collectionTitle}>
                    {filteredSpecimens.length} specimens
                  </Text>
                  <TouchableOpacity 
                    style={styles.sortButton}
                    onPress={async () => {
                      await hapticService.triggerSelectionFeedback();
                      Alert.alert('Sort', 'Sort options would be implemented here');
                    }}>
                    <Filter size={16} color="#6B7280" />
                    <Text style={styles.sortText}>Sort</Text>
                  </TouchableOpacity>
                </View>

                {viewMode === 'grid' ? (
                  <View style={styles.gridContainer}>
                    {filteredSpecimens.map(renderGridItem)}
                  </View>
                ) : (
                  <View style={styles.listContainer}>
                    {filteredSpecimens.map(renderListItem)}
                  </View>
                )}
              </View>
            </>
          )}

          {/* Bottom Spacing */}
          <View style={styles.bottomSpacing} />
        </ScrollView>
      </LinearGradient>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FEF3C7',
  },
  gradient: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    gap: 16,
  },
  loadingText: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
  },
  header: {
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 10,
  },
  title: {
    fontSize: 32,
    fontFamily: 'Inter-Bold',
    color: '#92400E',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
  },
  statsContainer: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    marginBottom: 24,
    gap: 12,
  },
  statCard: {
    flex: 1,
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 16,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  statValue: {
    fontSize: 24,
    fontFamily: 'Inter-Bold',
    color: '#111827',
    marginTop: 8,
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 11,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
    textAlign: 'center',
  },
  emptyState: {
    alignItems: 'center',
    paddingHorizontal: 40,
    paddingVertical: 60,
  },
  emptyIcon: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#F3F4F6',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 24,
  },
  emptyTitle: {
    fontSize: 24,
    fontFamily: 'Inter-Bold',
    color: '#111827',
    marginBottom: 12,
    textAlign: 'center',
  },
  emptyDescription: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 32,
  },
  addSampleButton: {
    borderRadius: 16,
    overflow: 'hidden',
    shadowColor: '#22C55E',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 12,
    elevation: 8,
  },
  addSampleGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 24,
    gap: 8,
  },
  addSampleText: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#FFFFFF',
  },
  controlsContainer: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    marginBottom: 16,
    gap: 12,
  },
  searchContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    gap: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  searchPlaceholder: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#9CA3AF',
  },
  viewControls: {
    flexDirection: 'row',
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  viewButton: {
    padding: 8,
    borderRadius: 8,
  },
  viewButtonActive: {
    backgroundColor: '#F59E0B',
  },
  filtersContainer: {
    marginBottom: 20,
  },
  filtersScrollContent: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    gap: 12,
  },
  filterButton: {
    backgroundColor: '#FFFFFF',
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  filterButtonActive: {
    backgroundColor: '#F59E0B',
  },
  filterText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
  },
  filterTextActive: {
    color: '#FFFFFF',
  },
  collectionContainer: {
    paddingHorizontal: 20,
    marginBottom: 24,
  },
  collectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  collectionTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
  },
  sortButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
    paddingHorizontal: 12,
    paddingVertical: 6,
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 1,
  },
  sortText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
  },
  gridContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  gridItem: {
    width: (width - 52) / 2,
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 16,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  gridImageContainer: {
    position: 'relative',
    marginBottom: 12,
  },
  gridImage: {
    width: 60,
    height: 60,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  favoriteIcon: {
    position: 'absolute',
    top: -4,
    right: -4,
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    padding: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  gridName: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    textAlign: 'center',
    marginBottom: 4,
  },
  gridScientific: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    textAlign: 'center',
    fontStyle: 'italic',
    marginBottom: 8,
  },
  gridMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  rarityDot: {
    width: 6,
    height: 6,
    borderRadius: 3,
  },
  gridRarity: {
    fontSize: 11,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
  },
  listContainer: {
    gap: 12,
  },
  listItem: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 16,
    flexDirection: 'row',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  listImageContainer: {
    marginRight: 16,
  },
  listImage: {
    width: 50,
    height: 50,
    borderRadius: 10,
    alignItems: 'center',
    justifyContent: 'center',
  },
  listContent: {
    flex: 1,
  },
  listHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 4,
  },
  listName: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    flex: 1,
  },
  listScientific: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    fontStyle: 'italic',
    marginBottom: 8,
  },
  listMeta: {
    flexDirection: 'row',
    gap: 16,
  },
  metaItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  metaText: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
  },
  listActions: {
    alignItems: 'flex-end',
    gap: 8,
  },
  rarityBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
  },
  rarityBadgeText: {
    fontSize: 10,
    fontFamily: 'Inter-Bold',
    color: '#FFFFFF',
  },
  confidenceText: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
  },
  bottomSpacing: {
    height: 100,
  },
});