import * as AuthSession from 'expo-auth-session';
import * as <PERSON><PERSON>rowser from 'expo-web-browser';
import * as SecureStore from 'expo-secure-store';
import * as Crypto from 'expo-crypto';
import { supabase } from '../lib/supabase';
import { AuthError, AuthResponse, User, Session } from '@supabase/supabase-js';
import zxcvbn from 'zxcvbn';

// Complete the auth session for OAuth flows
WebBrowser.maybeCompleteAuthSession();

export interface UserProfile {
  id: string;
  first_name: string | null;
  last_name: string | null;
  avatar_url: string | null;
  bio: string | null;
  location: string | null;
  date_of_birth: string | null;
  role: 'user' | 'premium' | 'admin';
  preferences: any;
  privacy_settings: any;
  total_identifications: number;
  total_discoveries: number;
  streak_days: number;
  last_activity_date: string | null;
  created_at: string;
  updated_at: string;
}

export interface AuthUser extends User {
  profile?: UserProfile;
}

export interface LoginCredentials {
  email: string;
  password: string;
  rememberMe?: boolean;
}

export interface RegisterCredentials {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
}

export interface SupabaseAuthResponse {
  user: AuthUser;
  session: Session | null;
}

export interface LoginResponse {
  user: AuthUser;
  session: Session | null;
}

export interface PasswordStrength {
  score: number;
  label: string;
  color: string;
  percentage: number;
  feedback: string[];
}

export class AuthService {
  constructor() {
    // Initialize Supabase auth state listener
    this.initializeAuthListener();
  }

  private initializeAuthListener() {
    supabase.auth.onAuthStateChange(async (event, session) => {
      if (event === 'SIGNED_IN' && session) {
        // Store session data securely
        await SecureStore.setItemAsync('supabase_session', JSON.stringify(session));
      } else if (event === 'SIGNED_OUT') {
        // Clear stored session
        await SecureStore.deleteItemAsync('supabase_session');
      }
    });
  }

  /**
   * Login with email and password
   */
  async login(credentials: LoginCredentials): Promise<LoginResponse> {
    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email: credentials.email,
        password: credentials.password,
      });

      if (error) {
        throw error;
      }

      if (!data.user) {
        throw new Error('Login failed - no user returned');
      }

      // Get user profile
      const profile = await this.getUserProfile(data.user.id);

      const authUser: AuthUser = {
        ...data.user,
        profile,
      };

      return {
        user: authUser,
        session: data.session,
      };
    } catch (error: any) {
      throw this.handleError(error);
    }
  }

  /**
   * Register new user account
   */
  async register(credentials: RegisterCredentials): Promise<{ message: string; user: User | null }> {
    try {
      // Validate password strength first
      const passwordStrength = this.checkPasswordStrengthLocal(credentials.password);
      if (!passwordStrength.isValid) {
        throw new Error('Password does not meet security requirements');
      }

      const { data, error } = await supabase.auth.signUp({
        email: credentials.email,
        password: credentials.password,
        options: {
          data: {
            first_name: credentials.firstName,
            last_name: credentials.lastName,
          },
        },
      });

      if (error) {
        throw error;
      }

      return {
        message: 'Registration successful. Please check your email to verify your account.',
        user: data.user,
      };
    } catch (error: any) {
      throw this.handleError(error);
    }
  }

  /**
   * Logout user
   */
  async logout(): Promise<void> {
    try {
      const { error } = await supabase.auth.signOut();
      if (error) {
        throw error;
      }
    } catch (error: any) {
      // Continue with logout even if server request fails
      console.error('Logout error:', error);
    }
  }

  /**
   * Refresh access token
   */
  async refreshToken(): Promise<Session | null> {
    try {
      const { data, error } = await supabase.auth.refreshSession();
      if (error) {
        throw error;
      }
      return data.session;
    } catch (error: any) {
      throw this.handleError(error);
    }
  }

  /**
   * Get current user information
   */
  async getCurrentUser(): Promise<AuthUser | null> {
    try {
      const { data: { user }, error } = await supabase.auth.getUser();
      if (error) {
        throw error;
      }

      if (!user) {
        return null;
      }

      // Get user profile
      const profile = await this.getUserProfile(user.id);

      return {
        ...user,
        profile,
      };
    } catch (error: any) {
      throw this.handleError(error);
    }
  }

  /**
   * Get user profile from database
   */
  async getUserProfile(userId: string): Promise<UserProfile | null> {
    try {
      const { data, error } = await supabase
        .from('user_profiles')
        .select('*')
        .eq('id', userId)
        .single();

      if (error && error.code !== 'PGRST116') { // PGRST116 is "not found"
        throw error;
      }

      return data;
    } catch (error: any) {
      console.error('Error fetching user profile:', error);
      return null;
    }
  }

  /**
   * Login with Google OAuth
   */
  async loginWithGoogle(): Promise<LoginResponse> {
    try {
      const { data, error } = await supabase.auth.signInWithOAuth({
        provider: 'google',
        options: {
          redirectTo: 'exp://localhost:8081/auth/callback',
          queryParams: {
            access_type: 'offline',
            prompt: 'consent',
          },
        },
      });

      if (error) {
        throw error;
      }

      // Wait for the auth state change
      return new Promise((resolve, reject) => {
        const { data: authListener } = supabase.auth.onAuthStateChange(
          async (event, session) => {
            if (event === 'SIGNED_IN' && session) {
              authListener.subscription.unsubscribe();

              const profile = await this.getUserProfile(session.user.id);
              const authUser: AuthUser = {
                ...session.user,
                profile,
              };

              resolve({
                user: authUser,
                session,
              });
            } else if (event === 'SIGNED_OUT') {
              authListener.subscription.unsubscribe();
              reject(new Error('Google authentication failed'));
            }
          }
        );

        // Set a timeout to prevent hanging
        setTimeout(() => {
          authListener.subscription.unsubscribe();
          reject(new Error('Google authentication timeout'));
        }, 30000);
      });
    } catch (error: any) {
      throw this.handleError(error);
    }
  }

  /**
   * Login with Apple OAuth
   */
  async loginWithApple(): Promise<LoginResponse> {
    try {
      const { data, error } = await supabase.auth.signInWithOAuth({
        provider: 'apple',
        options: {
          redirectTo: 'exp://localhost:8081/auth/callback',
        },
      });

      if (error) {
        throw error;
      }

      // Wait for the auth state change
      return new Promise((resolve, reject) => {
        const { data: authListener } = supabase.auth.onAuthStateChange(
          async (event, session) => {
            if (event === 'SIGNED_IN' && session) {
              authListener.subscription.unsubscribe();

              const profile = await this.getUserProfile(session.user.id);
              const authUser: AuthUser = {
                ...session.user,
                profile,
              };

              resolve({
                user: authUser,
                session,
              });
            } else if (event === 'SIGNED_OUT') {
              authListener.subscription.unsubscribe();
              reject(new Error('Apple authentication failed'));
            }
          }
        );

        // Set a timeout to prevent hanging
        setTimeout(() => {
          authListener.subscription.unsubscribe();
          reject(new Error('Apple authentication timeout'));
        }, 30000);
      });
    } catch (error: any) {
      throw this.handleError(error);
    }
  }

  /**
   * Request password reset
   */
  async requestPasswordReset(email: string): Promise<{ message: string }> {
    try {
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: 'exp://localhost:8081/auth/reset-password',
      });

      if (error) {
        throw error;
      }

      return {
        message: 'Password reset email sent. Please check your inbox.',
      };
    } catch (error: any) {
      throw this.handleError(error);
    }
  }

  /**
   * Reset password with new password (during reset flow)
   */
  async resetPassword(newPassword: string): Promise<{ message: string }> {
    try {
      // Validate password strength first
      const passwordStrength = this.checkPasswordStrengthLocal(newPassword);
      if (!passwordStrength.isValid) {
        throw new Error('Password does not meet security requirements');
      }

      const { error } = await supabase.auth.updateUser({
        password: newPassword,
      });

      if (error) {
        throw error;
      }

      return {
        message: 'Password updated successfully.',
      };
    } catch (error: any) {
      throw this.handleError(error);
    }
  }

  /**
   * Verify email with token
   */
  async verifyEmail(token: string): Promise<{ message: string }> {
    try {
      const response = await this.api.post('/verify-email', { token });
      return response.data;
    } catch (error: any) {
      throw this.handleError(error);
    }
  }

  /**
   * Resend email verification
   */
  async resendEmailVerification(): Promise<{ message: string }> {
    try {
      const response = await this.api.post('/resend-verification');
      return response.data;
    } catch (error: any) {
      throw this.handleError(error);
    }
  }

  /**
   * Check password strength (local implementation)
   */
  checkPasswordStrengthLocal(password: string): PasswordStrength {
    const requirements = {
      minLength: password.length >= 12,
      hasUppercase: /[A-Z]/.test(password),
      hasLowercase: /[a-z]/.test(password),
      hasNumbers: /\d/.test(password),
      hasSpecialChars: /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password),
    };

    // Use zxcvbn for advanced analysis
    const zxcvbnResult = zxcvbn(password);

    // Generate feedback
    let feedback: string[] = [];

    if (!requirements.minLength) {
      feedback.push('At least 12 characters');
    }
    if (!requirements.hasUppercase) {
      feedback.push('Add uppercase letters (A-Z)');
    }
    if (!requirements.hasLowercase) {
      feedback.push('Add lowercase letters (a-z)');
    }
    if (!requirements.hasNumbers) {
      feedback.push('Add numbers (0-9)');
    }
    if (!requirements.hasSpecialChars) {
      feedback.push('Add special characters (!@#$...)');
    }

    // Add zxcvbn suggestions
    if (zxcvbnResult.feedback.suggestions.length > 0) {
      feedback.push(...zxcvbnResult.feedback.suggestions.slice(0, 2));
    }

    // Determine overall strength
    const basicRequirementsMet = Object.values(requirements).every(req => req);
    const isValid = basicRequirementsMet && zxcvbnResult.score >= 2;

    // Adjust score based on requirements
    let adjustedScore = zxcvbnResult.score;
    if (!basicRequirementsMet) {
      adjustedScore = Math.min(adjustedScore, 1);
    }

    const labels = ['Very Weak', 'Weak', 'Fair', 'Good', 'Strong'];
    const colors = ['#ff4757', '#ff6b7a', '#ffa502', '#2ed573', '#20bf6b'];

    return {
      score: adjustedScore,
      label: labels[adjustedScore] || 'Very Weak',
      color: colors[adjustedScore] || '#ff4757',
      percentage: Math.min(100, (adjustedScore / 4) * 100),
      feedback: feedback.slice(0, 3), // Limit to 3 suggestions
      isValid
    };
  }

  /**
   * Check password strength (async wrapper for compatibility)
   */
  async checkPasswordStrength(password: string): Promise<PasswordStrength> {
    return this.checkPasswordStrengthLocal(password);
  }

  /**
   * Change password
   */
  async changePassword(currentPassword: string, newPassword: string): Promise<{ message: string }> {
    try {
      const response = await this.api.post('/change-password', {
        currentPassword,
        newPassword,
      });
      return response.data;
    } catch (error: any) {
      throw this.handleError(error);
    }
  }

  /**
   * Update user profile
   */
  async updateProfile(updates: Partial<User>): Promise<User> {
    try {
      const response = await this.api.put('/profile', updates);
      return response.data.user;
    } catch (error: any) {
      throw this.handleError(error);
    }
  }

  /**
   * Delete user account
   */
  async deleteAccount(password: string): Promise<{ message: string }> {
    try {
      const response = await this.api.delete('/account', {
        data: { password }
      });
      return response.data;
    } catch (error: any) {
      throw this.handleError(error);
    }
  }

  /**
   * Update user profile
   */
  async updateProfile(updates: Partial<UserProfile>): Promise<UserProfile> {
    try {
      const { data, error } = await supabase
        .from('user_profiles')
        .update(updates)
        .eq('id', (await supabase.auth.getUser()).data.user?.id)
        .select()
        .single();

      if (error) {
        throw error;
      }

      return data;
    } catch (error: any) {
      throw this.handleError(error);
    }
  }

  /**
   * Change password
   */
  async changePassword(currentPassword: string, newPassword: string): Promise<{ message: string }> {
    try {
      // Validate new password strength first
      const passwordStrength = this.checkPasswordStrengthLocal(newPassword);
      if (!passwordStrength.isValid) {
        throw new Error('New password does not meet security requirements');
      }

      // Verify current password by attempting to sign in
      const user = await this.getCurrentUser();
      if (!user?.email) {
        throw new Error('User email not found');
      }

      const { error: signInError } = await supabase.auth.signInWithPassword({
        email: user.email,
        password: currentPassword,
      });

      if (signInError) {
        throw new Error('Current password is incorrect');
      }

      // Update password
      const { error } = await supabase.auth.updateUser({
        password: newPassword,
      });

      if (error) {
        throw error;
      }

      return {
        message: 'Password changed successfully.',
      };
    } catch (error: any) {
      throw this.handleError(error);
    }
  }

  /**
   * Verify email with token
   */
  async verifyEmail(token: string, type: 'signup' | 'email_change' = 'signup'): Promise<{ message: string }> {
    try {
      const { error } = await supabase.auth.verifyOtp({
        token_hash: token,
        type: type,
      });

      if (error) {
        throw error;
      }

      return {
        message: 'Email verified successfully.',
      };
    } catch (error: any) {
      throw this.handleError(error);
    }
  }

  /**
   * Resend email verification
   */
  async resendEmailVerification(): Promise<{ message: string }> {
    try {
      const user = await this.getCurrentUser();
      if (!user?.email) {
        throw new Error('User email not found');
      }

      const { error } = await supabase.auth.resend({
        type: 'signup',
        email: user.email,
      });

      if (error) {
        throw error;
      }

      return {
        message: 'Verification email sent.',
      };
    } catch (error: any) {
      throw this.handleError(error);
    }
  }

  /**
   * Handle Supabase errors
   */
  private handleError(error: any): Error {
    if (error instanceof AuthError) {
      // Supabase auth error
      const message = error.message || 'Authentication error occurred';
      const customError = new Error(message);
      (customError as any).code = error.status;
      return customError;
    } else if (error.message) {
      // Other error with message
      return new Error(error.message);
    } else {
      // Unknown error
      return new Error('An unexpected error occurred');
    }
  }
}
