import 'react-native-url-polyfill/auto';
import { createClient } from '@supabase/supabase-js';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Supabase configuration
const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL || 'https://rgalmnkgvvxygftgflmj.supabase.co';
const supabaseAnonKey = process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJnYWxtbmtndnZ4eWdmdGdmbG1qIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTExOTA2NDEsImV4cCI6MjA2Njc2NjY0MX0.SONGsWSKgzNHvnv9ZGJl-kWXE2B3i2RLQoEjRLJvPy8';

// Create Supabase client with proper configuration
export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    storage: AsyncStorage,
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: false,
  },
  realtime: {
    params: {
      eventsPerSecond: 10,
    },
  },
});

// Database types (generated from your schema)
export interface Database {
  public: {
    Tables: {
      user_profiles: {
        Row: {
          id: string;
          first_name: string | null;
          last_name: string | null;
          avatar_url: string | null;
          bio: string | null;
          location: string | null;
          date_of_birth: string | null;
          role: 'user' | 'premium' | 'admin';
          preferences: any;
          privacy_settings: any;
          total_identifications: number;
          total_discoveries: number;
          streak_days: number;
          last_activity_date: string | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id: string;
          first_name?: string | null;
          last_name?: string | null;
          avatar_url?: string | null;
          bio?: string | null;
          location?: string | null;
          date_of_birth?: string | null;
          role?: 'user' | 'premium' | 'admin';
          preferences?: any;
          privacy_settings?: any;
          total_identifications?: number;
          total_discoveries?: number;
          streak_days?: number;
          last_activity_date?: string | null;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          first_name?: string | null;
          last_name?: string | null;
          avatar_url?: string | null;
          bio?: string | null;
          location?: string | null;
          date_of_birth?: string | null;
          role?: 'user' | 'premium' | 'admin';
          preferences?: any;
          privacy_settings?: any;
          total_identifications?: number;
          total_discoveries?: number;
          streak_days?: number;
          last_activity_date?: string | null;
          created_at?: string;
          updated_at?: string;
        };
      };
      species_categories: {
        Row: {
          id: string;
          name: string;
          description: string | null;
          icon_url: string | null;
          color_hex: string | null;
          created_at: string;
        };
        Insert: {
          id?: string;
          name: string;
          description?: string | null;
          icon_url?: string | null;
          color_hex?: string | null;
          created_at?: string;
        };
        Update: {
          id?: string;
          name?: string;
          description?: string | null;
          icon_url?: string | null;
          color_hex?: string | null;
          created_at?: string;
        };
      };
      species: {
        Row: {
          id: string;
          common_name: string;
          scientific_name: string;
          category_id: string | null;
          description: string | null;
          habitat: string | null;
          conservation_status: string | null;
          physical_traits: any;
          interesting_facts: string[] | null;
          image_urls: string[] | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          common_name: string;
          scientific_name: string;
          category_id?: string | null;
          description?: string | null;
          habitat?: string | null;
          conservation_status?: string | null;
          physical_traits?: any;
          interesting_facts?: string[] | null;
          image_urls?: string[] | null;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          common_name?: string;
          scientific_name?: string;
          category_id?: string | null;
          description?: string | null;
          habitat?: string | null;
          conservation_status?: string | null;
          physical_traits?: any;
          interesting_facts?: string[] | null;
          image_urls?: string[] | null;
          created_at?: string;
          updated_at?: string;
        };
      };
      media_uploads: {
        Row: {
          id: string;
          user_id: string;
          file_name: string;
          file_size: number | null;
          mime_type: string | null;
          media_type: 'image' | 'video' | 'audio';
          storage_path: string;
          public_url: string | null;
          thumbnail_url: string | null;
          metadata: any;
          upload_completed: boolean;
          created_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          file_name: string;
          file_size?: number | null;
          mime_type?: string | null;
          media_type: 'image' | 'video' | 'audio';
          storage_path: string;
          public_url?: string | null;
          thumbnail_url?: string | null;
          metadata?: any;
          upload_completed?: boolean;
          created_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          file_name?: string;
          file_size?: number | null;
          mime_type?: string | null;
          media_type?: 'image' | 'video' | 'audio';
          storage_path?: string;
          public_url?: string | null;
          thumbnail_url?: string | null;
          metadata?: any;
          upload_completed?: boolean;
          created_at?: string;
        };
      };
      species_identifications: {
        Row: {
          id: string;
          user_id: string;
          media_id: string;
          species_id: string | null;
          confidence_score: number | null;
          status: 'pending' | 'completed' | 'failed';
          ai_response: any | null;
          alternative_suggestions: any;
          location_data: any | null;
          weather_data: any | null;
          user_notes: string | null;
          is_verified: boolean;
          verified_by: string | null;
          verified_at: string | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          media_id: string;
          species_id?: string | null;
          confidence_score?: number | null;
          status?: 'pending' | 'completed' | 'failed';
          ai_response?: any | null;
          alternative_suggestions?: any;
          location_data?: any | null;
          weather_data?: any | null;
          user_notes?: string | null;
          is_verified?: boolean;
          verified_by?: string | null;
          verified_at?: string | null;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          media_id?: string;
          species_id?: string | null;
          confidence_score?: number | null;
          status?: 'pending' | 'completed' | 'failed';
          ai_response?: any | null;
          alternative_suggestions?: any;
          location_data?: any | null;
          weather_data?: any | null;
          user_notes?: string | null;
          is_verified?: boolean;
          verified_by?: string | null;
          verified_at?: string | null;
          created_at?: string;
          updated_at?: string;
        };
      };
      user_discoveries: {
        Row: {
          id: string;
          user_id: string;
          identification_id: string;
          is_favorite: boolean;
          personal_notes: string | null;
          tags: string[] | null;
          created_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          identification_id: string;
          is_favorite?: boolean;
          personal_notes?: string | null;
          tags?: string[] | null;
          created_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          identification_id?: string;
          is_favorite?: boolean;
          personal_notes?: string | null;
          tags?: string[] | null;
          created_at?: string;
        };
      };
      notifications: {
        Row: {
          id: string;
          user_id: string;
          type: 'identification_complete' | 'daily_reminder' | 'achievement' | 'system';
          title: string;
          message: string;
          data: any;
          is_read: boolean;
          is_sent: boolean;
          scheduled_for: string | null;
          sent_at: string | null;
          created_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          type: 'identification_complete' | 'daily_reminder' | 'achievement' | 'system';
          title: string;
          message: string;
          data?: any;
          is_read?: boolean;
          is_sent?: boolean;
          scheduled_for?: string | null;
          sent_at?: string | null;
          created_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          type?: 'identification_complete' | 'daily_reminder' | 'achievement' | 'system';
          title?: string;
          message?: string;
          data?: any;
          is_read?: boolean;
          is_sent?: boolean;
          scheduled_for?: string | null;
          sent_at?: string | null;
          created_at?: string;
        };
      };
    };
    Views: {
      [_ in never]: never;
    };
    Functions: {
      [_ in never]: never;
    };
    Enums: {
      user_role: 'user' | 'premium' | 'admin';
      identification_status: 'pending' | 'completed' | 'failed';
      media_type: 'image' | 'video' | 'audio';
      notification_type: 'identification_complete' | 'daily_reminder' | 'achievement' | 'system';
      subscription_status: 'active' | 'canceled' | 'past_due' | 'unpaid';
    };
  };
}
