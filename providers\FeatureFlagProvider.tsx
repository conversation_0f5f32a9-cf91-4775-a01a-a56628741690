import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { FeatureFlagService, UserContext } from '../services/FeatureFlagService';
import { ABTestingService } from '../services/ABTestingService';

interface FeatureFlagContextType {
  // Feature Flag methods
  getFlag: (flagKey: string, defaultValue?: any) => Promise<any>;
  isEnabled: (flagKey: string) => Promise<boolean>;
  getString: (flagKey: string, defaultValue?: string) => Promise<string>;
  getNumber: (flagKey: string, defaultValue?: number) => Promise<number>;
  getJSON: <T = any>(flagKey: string, defaultValue?: T) => Promise<T>;
  
  // A/B Testing methods
  getVariant: (experimentKey: string) => Promise<string | null>;
  trackConversion: (experimentKey: string, value?: number) => Promise<void>;
  trackGoal: (experimentKey: string, goalName: string, value?: number) => Promise<void>;
  
  // Context management
  setUserContext: (context: Partial<UserContext>) => Promise<void>;
  
  // Override methods (for development/testing)
  overrideFlag: (flagKey: string, value: any, reason?: string) => Promise<void>;
  removeOverride: (flagKey: string) => Promise<void>;
  forceVariant: (experimentKey: string, variantKey: string) => Promise<void>;
  
  // State
  isInitialized: boolean;
  isLoading: boolean;
  error: string | null;
  
  // Utility methods
  refresh: () => Promise<void>;
}

const FeatureFlagContext = createContext<FeatureFlagContextType | null>(null);

interface FeatureFlagProviderProps {
  children: ReactNode;
  userContext?: Partial<UserContext>;
  enableDevMode?: boolean;
}

export const FeatureFlagProvider: React.FC<FeatureFlagProviderProps> = ({
  children,
  userContext,
  enableDevMode = __DEV__,
}) => {
  const [isInitialized, setIsInitialized] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const featureFlagService = FeatureFlagService.getInstance();
  const abTestingService = ABTestingService.getInstance();

  useEffect(() => {
    initializeServices();
  }, []);

  useEffect(() => {
    if (userContext && isInitialized) {
      featureFlagService.setUserContext(userContext);
    }
  }, [userContext, isInitialized]);

  const initializeServices = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Initialize services
      await Promise.all([
        featureFlagService.initialize(),
        abTestingService.initialize(),
      ]);

      // Set initial user context if provided
      if (userContext) {
        await featureFlagService.setUserContext(userContext);
      }

      setIsInitialized(true);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to initialize feature flags';
      setError(errorMessage);
      console.error('FeatureFlagProvider initialization error:', err);
    } finally {
      setIsLoading(false);
    }
  };

  const getFlag = async (flagKey: string, defaultValue?: any): Promise<any> => {
    try {
      return await featureFlagService.getFlag(flagKey, defaultValue);
    } catch (err) {
      console.error(`Error getting flag ${flagKey}:`, err);
      return defaultValue;
    }
  };

  const isEnabled = async (flagKey: string): Promise<boolean> => {
    try {
      return await featureFlagService.isEnabled(flagKey);
    } catch (err) {
      console.error(`Error checking if flag ${flagKey} is enabled:`, err);
      return false;
    }
  };

  const getString = async (flagKey: string, defaultValue: string = ''): Promise<string> => {
    try {
      return await featureFlagService.getString(flagKey, defaultValue);
    } catch (err) {
      console.error(`Error getting string flag ${flagKey}:`, err);
      return defaultValue;
    }
  };

  const getNumber = async (flagKey: string, defaultValue: number = 0): Promise<number> => {
    try {
      return await featureFlagService.getNumber(flagKey, defaultValue);
    } catch (err) {
      console.error(`Error getting number flag ${flagKey}:`, err);
      return defaultValue;
    }
  };

  const getJSON = async <T = any>(flagKey: string, defaultValue?: T): Promise<T> => {
    try {
      return await featureFlagService.getJSON(flagKey, defaultValue);
    } catch (err) {
      console.error(`Error getting JSON flag ${flagKey}:`, err);
      return defaultValue as T;
    }
  };

  const getVariant = async (experimentKey: string): Promise<string | null> => {
    try {
      const assignment = await abTestingService.getExperimentAssignment(experimentKey);
      return assignment?.variant_key || null;
    } catch (err) {
      console.error(`Error getting variant for experiment ${experimentKey}:`, err);
      return null;
    }
  };

  const trackConversion = async (experimentKey: string, value: number = 1): Promise<void> => {
    try {
      await abTestingService.trackConversion(experimentKey, {
        event_name: 'conversion',
        event_properties: {},
        conversion_value: value,
      });
    } catch (err) {
      console.error(`Error tracking conversion for experiment ${experimentKey}:`, err);
    }
  };

  const trackGoal = async (experimentKey: string, goalName: string, value?: number): Promise<void> => {
    try {
      await abTestingService.trackGoal(experimentKey, goalName, value);
    } catch (err) {
      console.error(`Error tracking goal ${goalName} for experiment ${experimentKey}:`, err);
    }
  };

  const setUserContext = async (context: Partial<UserContext>): Promise<void> => {
    try {
      await featureFlagService.setUserContext(context);
    } catch (err) {
      console.error('Error setting user context:', err);
    }
  };

  const overrideFlag = async (flagKey: string, value: any, reason?: string): Promise<void> => {
    if (!enableDevMode) {
      console.warn('Flag overrides are only available in development mode');
      return;
    }

    try {
      await featureFlagService.overrideFlag(flagKey, value, reason);
    } catch (err) {
      console.error(`Error overriding flag ${flagKey}:`, err);
      throw err;
    }
  };

  const removeOverride = async (flagKey: string): Promise<void> => {
    if (!enableDevMode) {
      console.warn('Flag overrides are only available in development mode');
      return;
    }

    try {
      await featureFlagService.removeOverride(flagKey);
    } catch (err) {
      console.error(`Error removing override for flag ${flagKey}:`, err);
      throw err;
    }
  };

  const forceVariant = async (experimentKey: string, variantKey: string): Promise<void> => {
    if (!enableDevMode) {
      console.warn('Experiment variant forcing is only available in development mode');
      return;
    }

    try {
      await abTestingService.forceVariant(experimentKey, variantKey);
    } catch (err) {
      console.error(`Error forcing variant ${variantKey} for experiment ${experimentKey}:`, err);
      throw err;
    }
  };

  const refresh = async (): Promise<void> => {
    try {
      setIsLoading(true);
      setError(null);
      
      await featureFlagService.refresh();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to refresh feature flags';
      setError(errorMessage);
      console.error('Error refreshing feature flags:', err);
    } finally {
      setIsLoading(false);
    }
  };

  const contextValue: FeatureFlagContextType = {
    // Feature Flag methods
    getFlag,
    isEnabled,
    getString,
    getNumber,
    getJSON,
    
    // A/B Testing methods
    getVariant,
    trackConversion,
    trackGoal,
    
    // Context management
    setUserContext,
    
    // Override methods
    overrideFlag,
    removeOverride,
    forceVariant,
    
    // State
    isInitialized,
    isLoading,
    error,
    
    // Utility methods
    refresh,
  };

  return (
    <FeatureFlagContext.Provider value={contextValue}>
      {children}
    </FeatureFlagContext.Provider>
  );
};

/**
 * Hook to use the FeatureFlagContext
 */
export const useFeatureFlagContext = (): FeatureFlagContextType => {
  const context = useContext(FeatureFlagContext);
  
  if (!context) {
    throw new Error('useFeatureFlagContext must be used within a FeatureFlagProvider');
  }
  
  return context;
};

/**
 * Higher-order component for feature flag gating
 */
interface WithFeatureFlagProps {
  flagKey: string;
  fallback?: ReactNode;
  children: ReactNode;
}

export const WithFeatureFlag: React.FC<WithFeatureFlagProps> = ({
  flagKey,
  fallback = null,
  children,
}) => {
  const [isEnabled, setIsEnabled] = useState(false);
  const [loading, setLoading] = useState(true);
  const { getFlag } = useFeatureFlagContext();

  useEffect(() => {
    let mounted = true;

    const checkFlag = async () => {
      try {
        const enabled = await getFlag(flagKey, false);
        if (mounted) {
          setIsEnabled(Boolean(enabled));
        }
      } catch (err) {
        console.error(`Error checking feature flag ${flagKey}:`, err);
        if (mounted) {
          setIsEnabled(false);
        }
      } finally {
        if (mounted) {
          setLoading(false);
        }
      }
    };

    checkFlag();

    return () => {
      mounted = false;
    };
  }, [flagKey, getFlag]);

  if (loading) {
    return null; // or a loading spinner
  }

  return isEnabled ? <>{children}</> : <>{fallback}</>;
};

/**
 * Higher-order component for A/B testing
 */
interface WithExperimentProps {
  experimentKey: string;
  variants: { [variantKey: string]: ReactNode };
  fallback?: ReactNode;
}

export const WithExperiment: React.FC<WithExperimentProps> = ({
  experimentKey,
  variants,
  fallback = null,
}) => {
  const [variant, setVariant] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const { getVariant } = useFeatureFlagContext();

  useEffect(() => {
    let mounted = true;

    const checkVariant = async () => {
      try {
        const variantKey = await getVariant(experimentKey);
        if (mounted) {
          setVariant(variantKey);
        }
      } catch (err) {
        console.error(`Error getting variant for experiment ${experimentKey}:`, err);
        if (mounted) {
          setVariant(null);
        }
      } finally {
        if (mounted) {
          setLoading(false);
        }
      }
    };

    checkVariant();

    return () => {
      mounted = false;
    };
  }, [experimentKey, getVariant]);

  if (loading) {
    return null; // or a loading spinner
  }

  if (variant && variants[variant]) {
    return <>{variants[variant]}</>;
  }

  return <>{fallback}</>;
};

export default FeatureFlagProvider;
