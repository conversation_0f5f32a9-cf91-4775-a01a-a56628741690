import { supabase } from '../lib/supabase';
import * as Notifications from 'expo-notifications';
import * as Device from 'expo-device';
import { Platform } from 'react-native';

export interface NotificationData {
  id: string;
  user_id: string;
  type: 'identification_complete' | 'daily_reminder' | 'achievement' | 'system';
  title: string;
  message: string;
  data: any;
  is_read: boolean;
  is_sent: boolean;
  scheduled_for: string | null;
  sent_at: string | null;
  created_at: string;
}

export interface PushNotificationToken {
  token: string;
  platform: 'ios' | 'android' | 'web';
  device_id: string;
}

export class NotificationService {
  private expoPushToken: string | null = null;

  constructor() {
    this.configureNotifications();
  }

  /**
   * Configure notification behavior
   */
  private configureNotifications() {
    Notifications.setNotificationHandler({
      handleNotification: async () => ({
        shouldShowAlert: true,
        shouldPlaySound: true,
        shouldSetBadge: true,
      }),
    });
  }

  /**
   * Request notification permissions and get push token
   */
  async requestPermissions(): Promise<boolean> {
    try {
      if (!Device.isDevice) {
        console.log('Push notifications only work on physical devices');
        return false;
      }

      const { status: existingStatus } = await Notifications.getPermissionsAsync();
      let finalStatus = existingStatus;

      if (existingStatus !== 'granted') {
        const { status } = await Notifications.requestPermissionsAsync();
        finalStatus = status;
      }

      if (finalStatus !== 'granted') {
        console.log('Failed to get push token for push notification!');
        return false;
      }

      // Get the push token
      const token = await Notifications.getExpoPushTokenAsync({
        projectId: process.env.EXPO_PUBLIC_PROJECT_ID,
      });

      this.expoPushToken = token.data;
      console.log('Push token:', this.expoPushToken);

      // Save token to database
      await this.savePushToken(this.expoPushToken);

      // Configure notification channels for Android
      if (Platform.OS === 'android') {
        await this.setupAndroidChannels();
      }

      return true;
    } catch (error) {
      console.error('Error requesting notification permissions:', error);
      return false;
    }
  }

  /**
   * Setup Android notification channels
   */
  private async setupAndroidChannels() {
    await Notifications.setNotificationChannelAsync('default', {
      name: 'Default',
      importance: Notifications.AndroidImportance.MAX,
      vibrationPattern: [0, 250, 250, 250],
      lightColor: '#22C55E',
    });

    await Notifications.setNotificationChannelAsync('identifications', {
      name: 'Species Identifications',
      description: 'Notifications for completed species identifications',
      importance: Notifications.AndroidImportance.HIGH,
      vibrationPattern: [0, 250, 250, 250],
      lightColor: '#3B82F6',
    });

    await Notifications.setNotificationChannelAsync('achievements', {
      name: 'Achievements',
      description: 'Notifications for unlocked achievements',
      importance: Notifications.AndroidImportance.DEFAULT,
      vibrationPattern: [0, 250],
      lightColor: '#F59E0B',
    });

    await Notifications.setNotificationChannelAsync('reminders', {
      name: 'Daily Reminders',
      description: 'Daily nature discovery reminders',
      importance: Notifications.AndroidImportance.DEFAULT,
      vibrationPattern: [0, 250],
      lightColor: '#22C55E',
    });
  }

  /**
   * Save push token to database
   */
  private async savePushToken(token: string) {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return;

      const deviceId = await this.getDeviceId();
      const platform = Platform.OS as 'ios' | 'android';

      // Check if token already exists
      const { data: existingToken } = await supabase
        .from('push_tokens')
        .select('id')
        .eq('user_id', user.id)
        .eq('device_id', deviceId)
        .single();

      if (existingToken) {
        // Update existing token
        await supabase
          .from('push_tokens')
          .update({
            token,
            platform,
            updated_at: new Date().toISOString(),
          })
          .eq('id', existingToken.id);
      } else {
        // Insert new token
        await supabase
          .from('push_tokens')
          .insert({
            user_id: user.id,
            token,
            platform,
            device_id: deviceId,
          });
      }
    } catch (error) {
      console.error('Error saving push token:', error);
    }
  }

  /**
   * Get device ID
   */
  private async getDeviceId(): Promise<string> {
    try {
      // Use a combination of device info to create a unique ID
      const deviceName = Device.deviceName || 'unknown';
      const osVersion = Device.osVersion || 'unknown';
      const modelName = Device.modelName || 'unknown';
      
      return `${Platform.OS}-${deviceName}-${modelName}-${osVersion}`.replace(/\s+/g, '-');
    } catch (error) {
      console.error('Error getting device ID:', error);
      return `${Platform.OS}-${Date.now()}`;
    }
  }

  /**
   * Schedule daily reminder notification
   */
  async scheduleDailyReminder(hour: number = 10, minute: number = 0): Promise<void> {
    try {
      // Cancel existing daily reminders
      await this.cancelDailyReminders();

      // Schedule new daily reminder
      const trigger = {
        hour,
        minute,
        repeats: true,
      };

      await Notifications.scheduleNotificationAsync({
        content: {
          title: '🌿 Time to Explore Nature!',
          body: 'Discover something new in nature today. What will you find?',
          data: { type: 'daily_reminder' },
          categoryIdentifier: 'reminders',
        },
        trigger,
      });

      console.log(`Daily reminder scheduled for ${hour}:${minute.toString().padStart(2, '0')}`);
    } catch (error) {
      console.error('Error scheduling daily reminder:', error);
    }
  }

  /**
   * Cancel daily reminder notifications
   */
  async cancelDailyReminders(): Promise<void> {
    try {
      const scheduledNotifications = await Notifications.getAllScheduledNotificationsAsync();
      
      for (const notification of scheduledNotifications) {
        if (notification.content.data?.type === 'daily_reminder') {
          await Notifications.cancelScheduledNotificationAsync(notification.identifier);
        }
      }
    } catch (error) {
      console.error('Error canceling daily reminders:', error);
    }
  }

  /**
   * Send local notification
   */
  async sendLocalNotification(
    title: string,
    body: string,
    data: any = {},
    channelId: string = 'default'
  ): Promise<void> {
    try {
      await Notifications.scheduleNotificationAsync({
        content: {
          title,
          body,
          data,
          categoryIdentifier: channelId,
        },
        trigger: null, // Send immediately
      });
    } catch (error) {
      console.error('Error sending local notification:', error);
    }
  }

  /**
   * Get user notifications from database
   */
  async getUserNotifications(limit: number = 20, offset: number = 0): Promise<NotificationData[]> {
    try {
      const { data, error } = await supabase
        .from('notifications')
        .select('*')
        .order('created_at', { ascending: false })
        .range(offset, offset + limit - 1);

      if (error) {
        throw error;
      }

      return data || [];
    } catch (error) {
      console.error('Error fetching notifications:', error);
      throw error;
    }
  }

  /**
   * Mark notification as read
   */
  async markAsRead(notificationId: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('notifications')
        .update({ is_read: true })
        .eq('id', notificationId);

      if (error) {
        throw error;
      }
    } catch (error) {
      console.error('Error marking notification as read:', error);
      throw error;
    }
  }

  /**
   * Mark all notifications as read
   */
  async markAllAsRead(): Promise<void> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return;

      const { error } = await supabase
        .from('notifications')
        .update({ is_read: true })
        .eq('user_id', user.id)
        .eq('is_read', false);

      if (error) {
        throw error;
      }
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
      throw error;
    }
  }

  /**
   * Get unread notification count
   */
  async getUnreadCount(): Promise<number> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return 0;

      const { count, error } = await supabase
        .from('notifications')
        .select('*', { count: 'exact', head: true })
        .eq('user_id', user.id)
        .eq('is_read', false);

      if (error) {
        throw error;
      }

      return count || 0;
    } catch (error) {
      console.error('Error getting unread count:', error);
      return 0;
    }
  }

  /**
   * Delete notification
   */
  async deleteNotification(notificationId: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('notifications')
        .delete()
        .eq('id', notificationId);

      if (error) {
        throw error;
      }
    } catch (error) {
      console.error('Error deleting notification:', error);
      throw error;
    }
  }

  /**
   * Clear all notifications
   */
  async clearAllNotifications(): Promise<void> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return;

      const { error } = await supabase
        .from('notifications')
        .delete()
        .eq('user_id', user.id);

      if (error) {
        throw error;
      }
    } catch (error) {
      console.error('Error clearing all notifications:', error);
      throw error;
    }
  }

  /**
   * Setup notification listeners
   */
  setupNotificationListeners() {
    // Handle notification received while app is in foreground
    const foregroundSubscription = Notifications.addNotificationReceivedListener(notification => {
      console.log('Notification received in foreground:', notification);
    });

    // Handle notification response (user tapped notification)
    const responseSubscription = Notifications.addNotificationResponseReceivedListener(response => {
      console.log('Notification response:', response);
      
      const data = response.notification.request.content.data;
      
      // Handle different notification types
      if (data.type === 'identification_complete' && data.identification_id) {
        // Navigate to identification details
        // This would be handled by your navigation system
      } else if (data.type === 'achievement' && data.achievement_id) {
        // Navigate to achievements
      }
    });

    return () => {
      foregroundSubscription.remove();
      responseSubscription.remove();
    };
  }
}
