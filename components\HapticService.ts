import { Platform } from 'react-native';

export type HapticFeedbackType = 'light' | 'medium' | 'heavy' | 'success' | 'warning' | 'error';

export class HapticService {
  private static instance: HapticService;
  private isEnabled: boolean = true;

  static getInstance(): HapticService {
    if (!HapticService.instance) {
      HapticService.instance = new HapticService();
    }
    return HapticService.instance;
  }

  setEnabled(enabled: boolean): void {
    this.isEnabled = enabled;
  }

  async triggerFeedback(type: HapticFeedbackType = 'light'): Promise<void> {
    if (!this.isEnabled) return;

    if (Platform.OS === 'web') {
      // Web implementation using Vibration API
      if ('vibrator' in navigator || 'vibrate' in navigator) {
        try {
          const patterns = {
            light: [10],
            medium: [20],
            heavy: [30],
            success: [10, 50, 10],
            warning: [20, 100, 20],
            error: [50, 100, 50],
          };

          const pattern = patterns[type] || patterns.light;
          navigator.vibrate(pattern);
        } catch (error) {
          console.warn('Vibration not supported:', error);
        }
      }
    } else {
      // Native implementation would use expo-haptics
      try {
        const { Haptics } = await import('expo-haptics');
        
        switch (type) {
          case 'light':
            await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
            break;
          case 'medium':
            await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
            break;
          case 'heavy':
            await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Heavy);
            break;
          case 'success':
            await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
            break;
          case 'warning':
            await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Warning);
            break;
          case 'error':
            await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
            break;
        }
      } catch (error) {
        console.warn('Haptic feedback not available:', error);
      }
    }
  }

  async triggerSelectionFeedback(): Promise<void> {
    await this.triggerFeedback('light');
  }

  async triggerImpactFeedback(intensity: 'light' | 'medium' | 'heavy' = 'medium'): Promise<void> {
    await this.triggerFeedback(intensity);
  }

  async triggerNotificationFeedback(type: 'success' | 'warning' | 'error'): Promise<void> {
    await this.triggerFeedback(type);
  }
}

export default HapticService;