-- Row Level Security Policies for Privacy Management
-- This migration creates comprehensive RLS policies for privacy and data protection

-- Enable RLS on all privacy tables
ALTER TABLE data_export_requests ENABLE ROW LEVEL SECURITY;
ALTER TABLE data_deletion_requests ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_privacy_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE consent_log ENABLE ROW LEVEL SECURITY;
ALTER TABLE data_access_log ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_locations ENABLE ROW LEVEL SECURITY;
ALTER TABLE data_categories ENABLE ROW LEVEL SECURITY;

-- Data Export Requests Policies
CREATE POLICY "Users can view own export requests" ON data_export_requests
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create own export requests" ON data_export_requests
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own export requests" ON data_export_requests
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Service role can manage all export requests" ON data_export_requests
    FOR ALL USING (auth.role() = 'service_role');

CREATE POLICY "Admins can view all export requests" ON data_export_requests
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM user_profiles 
            WHERE id = auth.uid() 
            AND role IN ('admin', 'super_admin')
        )
    );

-- Data Deletion Requests Policies
CREATE POLICY "Users can view own deletion requests" ON data_deletion_requests
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create own deletion requests" ON data_deletion_requests
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own deletion requests" ON data_deletion_requests
    FOR UPDATE USING (
        auth.uid() = user_id 
        AND status IN ('pending', 'cancelled')
    );

CREATE POLICY "Service role can manage all deletion requests" ON data_deletion_requests
    FOR ALL USING (auth.role() = 'service_role');

CREATE POLICY "Admins can manage deletion requests" ON data_deletion_requests
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM user_profiles 
            WHERE id = auth.uid() 
            AND role IN ('admin', 'super_admin')
        )
    );

-- User Privacy Settings Policies
CREATE POLICY "Users can view own privacy settings" ON user_privacy_settings
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own privacy settings" ON user_privacy_settings
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own privacy settings" ON user_privacy_settings
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Service role can manage privacy settings" ON user_privacy_settings
    FOR ALL USING (auth.role() = 'service_role');

CREATE POLICY "Admins can view privacy settings for support" ON user_privacy_settings
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM user_profiles 
            WHERE id = auth.uid() 
            AND role IN ('admin', 'super_admin')
        )
    );

-- Consent Log Policies
CREATE POLICY "Users can view own consent log" ON consent_log
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Service role can manage consent log" ON consent_log
    FOR ALL USING (auth.role() = 'service_role');

CREATE POLICY "System can insert consent records" ON consent_log
    FOR INSERT WITH CHECK (true); -- Allow system to log consent

CREATE POLICY "Admins can view consent logs for compliance" ON consent_log
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM user_profiles 
            WHERE id = auth.uid() 
            AND role IN ('admin', 'super_admin')
        )
    );

-- Data Access Log Policies (Admin and audit only)
CREATE POLICY "Service role can manage access logs" ON data_access_log
    FOR ALL USING (auth.role() = 'service_role');

CREATE POLICY "Admins can view access logs" ON data_access_log
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM user_profiles 
            WHERE id = auth.uid() 
            AND role IN ('admin', 'super_admin')
        )
    );

CREATE POLICY "Users can view their own access logs" ON data_access_log
    FOR SELECT USING (auth.uid() = user_id);

-- User Locations Policies (Privacy-aware)
CREATE POLICY "Users can view own locations" ON user_locations
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own locations" ON user_locations
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own locations" ON user_locations
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own locations" ON user_locations
    FOR DELETE USING (auth.uid() = user_id);

CREATE POLICY "Service role can manage locations" ON user_locations
    FOR ALL USING (auth.role() = 'service_role');

-- Public can view approximate locations if user allows
CREATE POLICY "Public can view shared approximate locations" ON user_locations
    FOR SELECT USING (
        privacy_level IN ('city', 'region', 'country')
        AND EXISTS (
            SELECT 1 FROM user_privacy_settings ups
            WHERE ups.user_id = user_locations.user_id
            AND ups.share_location = true
            AND ups.public_profile = true
        )
    );

-- Data Categories Policies (Public read for transparency)
CREATE POLICY "Public can view data categories" ON data_categories
    FOR SELECT USING (true);

CREATE POLICY "Service role can manage data categories" ON data_categories
    FOR ALL USING (auth.role() = 'service_role');

CREATE POLICY "Admins can manage data categories" ON data_categories
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM user_profiles 
            WHERE id = auth.uid() 
            AND role IN ('admin', 'super_admin')
        )
    );

-- Create functions for privacy management
CREATE OR REPLACE FUNCTION get_user_privacy_settings(user_uuid UUID)
RETURNS TABLE (
    setting_name TEXT,
    setting_value BOOLEAN,
    category TEXT,
    description TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        'allow_analytics'::TEXT as setting_name,
        ups.allow_analytics as setting_value,
        'data_collection'::TEXT as category,
        'Allow collection of usage analytics'::TEXT as description
    FROM user_privacy_settings ups
    WHERE ups.user_id = user_uuid
    
    UNION ALL
    
    SELECT 
        'share_location'::TEXT,
        ups.share_location,
        'location'::TEXT,
        'Share location data with identifications'::TEXT
    FROM user_privacy_settings ups
    WHERE ups.user_id = user_uuid
    
    UNION ALL
    
    SELECT 
        'public_profile'::TEXT,
        ups.public_profile,
        'profile'::TEXT,
        'Make profile visible to other users'::TEXT
    FROM user_privacy_settings ups
    WHERE ups.user_id = user_uuid
    
    UNION ALL
    
    SELECT 
        'allow_marketing_emails'::TEXT,
        ups.allow_marketing_emails,
        'communication'::TEXT,
        'Receive marketing and promotional emails'::TEXT
    FROM user_privacy_settings ups
    WHERE ups.user_id = user_uuid
    
    UNION ALL
    
    SELECT 
        'allow_scientific_research'::TEXT,
        ups.allow_scientific_research,
        'data_sharing'::TEXT,
        'Allow data to be used for scientific research'::TEXT
    FROM user_privacy_settings ups
    WHERE ups.user_id = user_uuid;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to log data access for GDPR compliance
CREATE OR REPLACE FUNCTION log_data_access(
    target_user_id UUID,
    access_type_param TEXT,
    data_category_param TEXT,
    purpose_param TEXT DEFAULT NULL,
    legal_basis_param TEXT DEFAULT 'legitimate_interest'
)
RETURNS VOID AS $$
DECLARE
    current_user_id UUID;
    client_ip INET;
    client_user_agent TEXT;
BEGIN
    -- Get current user
    current_user_id := auth.uid();
    
    -- Get client info (would be passed from application in real implementation)
    client_ip := inet_client_addr();
    
    -- Log the access
    INSERT INTO data_access_log (
        user_id,
        accessed_by,
        access_type,
        data_category,
        ip_address,
        purpose,
        legal_basis
    ) VALUES (
        target_user_id,
        current_user_id,
        access_type_param,
        data_category_param,
        client_ip,
        purpose_param,
        legal_basis_param
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if user has given consent for specific purpose
CREATE OR REPLACE FUNCTION has_user_consent(
    user_uuid UUID,
    consent_type_param TEXT,
    consent_version_param TEXT DEFAULT NULL
)
RETURNS BOOLEAN AS $$
DECLARE
    latest_consent BOOLEAN;
BEGIN
    -- Get the most recent consent for this type
    SELECT consent_given INTO latest_consent
    FROM consent_log
    WHERE user_id = user_uuid
    AND consent_type = consent_type_param
    AND (consent_version_param IS NULL OR consent_version = consent_version_param)
    AND (expires_at IS NULL OR expires_at > NOW())
    ORDER BY created_at DESC
    LIMIT 1;
    
    RETURN COALESCE(latest_consent, false);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to anonymize location data based on privacy settings
CREATE OR REPLACE FUNCTION get_privacy_adjusted_location(
    user_uuid UUID,
    location_id UUID
)
RETURNS TABLE (
    latitude DECIMAL,
    longitude DECIMAL,
    precision_level TEXT,
    city TEXT,
    region TEXT,
    country TEXT
) AS $$
DECLARE
    privacy_level TEXT;
    location_record RECORD;
BEGIN
    -- Get user's location privacy setting
    SELECT location_precision INTO privacy_level
    FROM user_privacy_settings
    WHERE user_id = user_uuid;
    
    -- Get location record
    SELECT * INTO location_record
    FROM user_locations ul
    WHERE ul.id = location_id
    AND ul.user_id = user_uuid;
    
    -- Return data based on privacy level
    CASE privacy_level
        WHEN 'exact' THEN
            RETURN QUERY SELECT 
                location_record.latitude,
                location_record.longitude,
                'exact'::TEXT,
                location_record.city,
                location_record.region,
                location_record.country;
        WHEN 'approximate' THEN
            RETURN QUERY SELECT 
                location_record.approximate_latitude,
                location_record.approximate_longitude,
                'approximate'::TEXT,
                location_record.city,
                location_record.region,
                location_record.country;
        WHEN 'city' THEN
            RETURN QUERY SELECT 
                NULL::DECIMAL,
                NULL::DECIMAL,
                'city'::TEXT,
                location_record.city,
                location_record.region,
                location_record.country;
        WHEN 'region' THEN
            RETURN QUERY SELECT 
                NULL::DECIMAL,
                NULL::DECIMAL,
                'region'::TEXT,
                NULL::TEXT,
                location_record.region,
                location_record.country;
        WHEN 'country' THEN
            RETURN QUERY SELECT 
                NULL::DECIMAL,
                NULL::DECIMAL,
                'country'::TEXT,
                NULL::TEXT,
                NULL::TEXT,
                location_record.country;
        ELSE -- 'none'
            RETURN QUERY SELECT 
                NULL::DECIMAL,
                NULL::DECIMAL,
                'hidden'::TEXT,
                NULL::TEXT,
                NULL::TEXT,
                NULL::TEXT;
    END CASE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
