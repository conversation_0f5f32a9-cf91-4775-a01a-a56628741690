import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

const supabaseClient = createClient(
  Deno.env.get('SUPABASE_URL') ?? '',
  Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
)

interface ExportRequest {
  id: string;
  user_id: string;
  request_type: string;
  export_format: string;
  include_media: boolean;
  include_location: boolean;
  include_metadata: boolean;
  date_range_start?: string;
  date_range_end?: string;
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const { exportRequestId } = await req.json()

    // Get export request details
    const { data: exportRequest, error: requestError } = await supabaseClient
      .from('data_export_requests')
      .select('*')
      .eq('id', exportRequestId)
      .single()

    if (requestError || !exportRequest) {
      throw new Error('Export request not found')
    }

    // Update status to processing
    await supabaseClient
      .from('data_export_requests')
      .update({ status: 'processing' })
      .eq('id', exportRequestId)

    // Collect user data based on request type
    const userData = await collectUserData(exportRequest)

    // Generate export file
    const exportFile = await generateExportFile(userData, exportRequest.export_format)

    // Upload to storage
    const fileName = `export_${exportRequestId}.${exportRequest.export_format}`
    const filePath = `exports/${exportRequest.user_id}/${fileName}`

    const { data: uploadData, error: uploadError } = await supabaseClient.storage
      .from('data-exports')
      .upload(filePath, exportFile, {
        contentType: getContentType(exportRequest.export_format),
        upsert: true
      })

    if (uploadError) {
      throw uploadError
    }

    // Get public URL (with expiration)
    const { data: urlData } = supabaseClient.storage
      .from('data-exports')
      .getPublicUrl(filePath)

    // Update export request with completion details
    await supabaseClient
      .from('data_export_requests')
      .update({
        status: 'completed',
        file_url: urlData.publicUrl,
        file_size_bytes: exportFile.length,
        completed_at: new Date().toISOString()
      })
      .eq('id', exportRequestId)

    // Send notification to user
    await supabaseClient
      .from('notifications')
      .insert({
        user_id: exportRequest.user_id,
        type: 'system',
        title: 'Data Export Ready',
        message: 'Your data export is ready for download. The download link will expire in 7 days.',
        data: {
          export_id: exportRequestId,
          file_size: exportFile.length,
          format: exportRequest.export_format
        }
      })

    return new Response(
      JSON.stringify({ success: true, exportId: exportRequestId }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      }
    )

  } catch (error: any) {
    console.error('Error processing data export:', error)

    // Update export request status to failed
    const { exportRequestId } = await req.json().catch(() => ({}))
    if (exportRequestId) {
      await supabaseClient
        .from('data_export_requests')
        .update({
          status: 'failed',
          error_message: error.message
        })
        .eq('id', exportRequestId)
    }

    return new Response(
      JSON.stringify({ error: error.message }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400,
      }
    )
  }
})

async function collectUserData(exportRequest: ExportRequest): Promise<any> {
  const userId = exportRequest.user_id
  const userData: any = {}

  try {
    // Always include basic profile data
    const { data: profile } = await supabaseClient
      .from('user_profiles')
      .select('*')
      .eq('id', userId)
      .single()

    userData.profile = profile

    // Include privacy settings
    const { data: privacySettings } = await supabaseClient
      .from('user_privacy_settings')
      .select('*')
      .eq('user_id', userId)
      .single()

    userData.privacy_settings = privacySettings

    // Include subscription data if exists
    const { data: subscriptions } = await supabaseClient
      .from('user_subscriptions')
      .select('*, plan:subscription_plans(*)')
      .eq('user_id', userId)

    userData.subscriptions = subscriptions

    // Include identifications based on request type and date range
    if (['full_export', 'identifications'].includes(exportRequest.request_type)) {
      let query = supabaseClient
        .from('species_identifications')
        .select('*, species:species_id(*)')
        .eq('user_id', userId)

      if (exportRequest.date_range_start) {
        query = query.gte('created_at', exportRequest.date_range_start)
      }
      if (exportRequest.date_range_end) {
        query = query.lte('created_at', exportRequest.date_range_end)
      }

      const { data: identifications } = await query
      userData.identifications = identifications
    }

    // Include media data
    if (['full_export', 'media'].includes(exportRequest.request_type) && exportRequest.include_media) {
      let query = supabaseClient
        .from('media_uploads')
        .select('*')
        .eq('user_id', userId)

      if (exportRequest.date_range_start) {
        query = query.gte('created_at', exportRequest.date_range_start)
      }
      if (exportRequest.date_range_end) {
        query = query.lte('created_at', exportRequest.date_range_end)
      }

      const { data: media } = await query
      userData.media = media
    }

    // Include location data if permitted
    if (['full_export', 'location_data'].includes(exportRequest.request_type) && exportRequest.include_location) {
      // Check user's location sharing preferences
      const { data: locationData } = await supabaseClient
        .from('user_locations')
        .select('*')
        .eq('user_id', userId)

      userData.locations = locationData
    }

    // Include usage tracking data
    if (exportRequest.request_type === 'full_export') {
      const { data: usageData } = await supabaseClient
        .from('usage_tracking')
        .select('*')
        .eq('user_id', userId)

      userData.usage_tracking = usageData
    }

    // Include consent history
    const { data: consentHistory } = await supabaseClient
      .from('consent_log')
      .select('*')
      .eq('user_id', userId)

    userData.consent_history = consentHistory

    // Include data access log (user's own access)
    const { data: accessLog } = await supabaseClient
      .from('data_access_log')
      .select('*')
      .eq('user_id', userId)

    userData.data_access_log = accessLog

    // Add export metadata
    userData.export_metadata = {
      export_date: new Date().toISOString(),
      export_type: exportRequest.request_type,
      export_format: exportRequest.export_format,
      includes_media: exportRequest.include_media,
      includes_location: exportRequest.include_location,
      includes_metadata: exportRequest.include_metadata,
      date_range: {
        start: exportRequest.date_range_start,
        end: exportRequest.date_range_end
      },
      gdpr_compliance: {
        legal_basis: 'user_request',
        data_controller: 'Bioscan+',
        retention_policy: 'Data will be deleted after 7 days unless downloaded'
      }
    }

    return userData

  } catch (error) {
    console.error('Error collecting user data:', error)
    throw error
  }
}

async function generateExportFile(userData: any, format: string): Promise<Uint8Array> {
  const encoder = new TextEncoder()

  switch (format) {
    case 'json':
      return encoder.encode(JSON.stringify(userData, null, 2))

    case 'csv':
      return encoder.encode(convertToCSV(userData))

    case 'xml':
      return encoder.encode(convertToXML(userData))

    default:
      throw new Error(`Unsupported export format: ${format}`)
  }
}

function convertToCSV(userData: any): string {
  let csv = ''

  // Convert each data category to CSV sections
  for (const [category, data] of Object.entries(userData)) {
    if (Array.isArray(data) && data.length > 0) {
      csv += `\n--- ${category.toUpperCase()} ---\n`
      const headers = Object.keys(data[0])
      csv += headers.join(',') + '\n'

      for (const item of data) {
        const values = headers.map(header => {
          const value = item[header]
          if (typeof value === 'object' && value !== null) {
            return `"${JSON.stringify(value).replace(/"/g, '""')}"`
          }
          return `"${String(value || '').replace(/"/g, '""')}"`
        })
        csv += values.join(',') + '\n'
      }
    } else if (typeof data === 'object' && data !== null) {
      csv += `\n--- ${category.toUpperCase()} ---\n`
      for (const [key, value] of Object.entries(data)) {
        csv += `"${key}","${typeof value === 'object' ? JSON.stringify(value) : value}"\n`
      }
    }
  }

  return csv
}

function convertToXML(userData: any): string {
  let xml = '<?xml version="1.0" encoding="UTF-8"?>\n<user_data_export>\n'

  for (const [category, data] of Object.entries(userData)) {
    xml += `  <${category}>\n`

    if (Array.isArray(data)) {
      for (const item of data) {
        xml += '    <item>\n'
        for (const [key, value] of Object.entries(item)) {
          xml += `      <${key}>${escapeXML(String(value || ''))}</${key}>\n`
        }
        xml += '    </item>\n'
      }
    } else if (typeof data === 'object' && data !== null) {
      for (const [key, value] of Object.entries(data)) {
        xml += `    <${key}>${escapeXML(typeof value === 'object' ? JSON.stringify(value) : String(value || ''))}</${key}>\n`
      }
    }

    xml += `  </${category}>\n`
  }

  xml += '</user_data_export>'
  return xml
}

function escapeXML(str: string): string {
  return str
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#39;')
}

function getContentType(format: string): string {
  switch (format) {
    case 'json':
      return 'application/json'
    case 'csv':
      return 'text/csv'
    case 'xml':
      return 'application/xml'
    default:
      return 'text/plain'
  }
}
