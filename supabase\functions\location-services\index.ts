import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

interface LocationRequest {
  action: 'find_nearby_species' | 'check_geofences' | 'update_analytics' | 'generate_recommendations';
  data: any;
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_ANON_KEY') ?? '',
      {
        global: {
          headers: { Authorization: req.headers.get('Authorization')! },
        },
      }
    );

    const { action, data }: LocationRequest = await req.json();

    let result;
    switch (action) {
      case 'find_nearby_species':
        result = await findNearbySpecies(supabaseClient, data);
        break;
      case 'check_geofences':
        result = await checkGeofences(supabaseClient, data);
        break;
      case 'update_analytics':
        result = await updateLocationAnalytics(supabaseClient, data);
        break;
      case 'generate_recommendations':
        result = await generateLocationRecommendations(supabaseClient, data);
        break;
      default:
        throw new Error(`Unknown action: ${action}`);
    }

    return new Response(JSON.stringify(result), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });
  } catch (error) {
    console.error('Location services error:', error);
    return new Response(JSON.stringify({ error: error.message }), {
      status: 400,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });
  }
});

/**
 * Find nearby species based on location and radius
 */
async function findNearbySpecies(supabaseClient: any, data: {
  latitude: number;
  longitude: number;
  radius_km: number;
  user_id?: string;
}) {
  const { latitude, longitude, radius_km, user_id } = data;

  // Convert radius to meters for PostGIS
  const radiusMeters = radius_km * 1000;

  // Find species within radius
  const { data: nearbySpecies, error } = await supabaseClient.rpc('find_species_within_radius', {
    center_lat: latitude,
    center_lng: longitude,
    radius_meters: radiusMeters,
  });

  if (error) throw error;

  // Calculate distances and add user-specific data
  const enrichedSpecies = await Promise.all(
    nearbySpecies.map(async (species: any) => {
      // Calculate distance
      const distance = calculateDistance(
        latitude,
        longitude,
        species.latitude,
        species.longitude
      );

      // Check if user has identified this species
      let userHasIdentified = false;
      if (user_id) {
        const { data: userIdentifications } = await supabaseClient
          .from('species_identifications')
          .select('id')
          .eq('user_id', user_id)
          .eq('species_id', species.species_id)
          .limit(1);

        userHasIdentified = userIdentifications && userIdentifications.length > 0;
      }

      return {
        ...species,
        distance_meters: Math.round(distance),
        user_has_identified: userHasIdentified,
      };
    })
  );

  // Sort by distance and relevance
  enrichedSpecies.sort((a, b) => {
    // Prioritize species user hasn't identified
    if (!a.user_has_identified && b.user_has_identified) return -1;
    if (a.user_has_identified && !b.user_has_identified) return 1;
    
    // Then sort by distance
    return a.distance_meters - b.distance_meters;
  });

  return enrichedSpecies.slice(0, 50); // Limit to 50 results
}

/**
 * Check if user location triggers any geofences
 */
async function checkGeofences(supabaseClient: any, data: {
  latitude: number;
  longitude: number;
  user_id: string;
}) {
  const { latitude, longitude, user_id } = data;

  // Find geofences that contain this point
  const { data: triggeredGeofences, error } = await supabaseClient.rpc('check_point_in_geofences', {
    point_lat: latitude,
    point_lng: longitude,
    check_user_id: user_id,
  });

  if (error) throw error;

  // Process geofence triggers
  const results = [];
  for (const geofence of triggeredGeofences) {
    // Log geofence entry
    await supabaseClient
      .from('geofence_events')
      .insert({
        user_id,
        geofence_id: geofence.id,
        event_type: 'entry',
        latitude,
        longitude,
        triggered_at: new Date().toISOString(),
      });

    // Execute geofence actions
    if (geofence.entry_action === 'notify') {
      // Trigger notification
      results.push({
        type: 'notification',
        geofence_id: geofence.id,
        message: `You've entered ${geofence.name}`,
        action_data: geofence.properties,
      });
    } else if (geofence.entry_action === 'suggest') {
      // Generate location suggestions
      const suggestions = await generateLocationSuggestions(supabaseClient, {
        latitude,
        longitude,
        geofence,
        user_id,
      });
      
      results.push({
        type: 'suggestions',
        geofence_id: geofence.id,
        suggestions,
      });
    }
  }

  return results;
}

/**
 * Update location analytics for user
 */
async function updateLocationAnalytics(supabaseClient: any, data: {
  user_id: string;
  date: string;
  location: { latitude: number; longitude: number };
  activity_type?: string;
}) {
  const { user_id, date, location, activity_type } = data;

  // Get or create analytics record for the date
  const { data: existingAnalytics } = await supabaseClient
    .from('location_analytics')
    .select('*')
    .eq('user_id', user_id)
    .eq('date', date)
    .single();

  if (existingAnalytics) {
    // Update existing record
    const updates: any = {
      total_locations_recorded: existingAnalytics.total_locations_recorded + 1,
    };

    // Calculate distance from last location
    if (existingAnalytics.last_location) {
      const distance = calculateDistance(
        existingAnalytics.last_location.latitude,
        existingAnalytics.last_location.longitude,
        location.latitude,
        location.longitude
      ) / 1000; // Convert to km

      updates.total_distance_traveled = existingAnalytics.total_distance_traveled + distance;
    }

    // Update location-specific counters
    if (activity_type === 'identification') {
      updates.identifications_with_location = existingAnalytics.identifications_with_location + 1;
    } else if (activity_type === 'discovery') {
      updates.discoveries_made = existingAnalytics.discoveries_made + 1;
    } else if (activity_type === 'public_share') {
      updates.public_locations_shared = existingAnalytics.public_locations_shared + 1;
    }

    // Get location metadata
    const locationMetadata = await getLocationMetadata(location);
    
    // Update geographic data
    const countries = new Set([...existingAnalytics.countries_visited, locationMetadata.country]);
    const cities = new Set([...existingAnalytics.cities_visited, locationMetadata.city]);
    
    updates.countries_visited = Array.from(countries).filter(Boolean);
    updates.cities_visited = Array.from(cities).filter(Boolean);
    updates.last_location = location;

    await supabaseClient
      .from('location_analytics')
      .update(updates)
      .eq('user_id', user_id)
      .eq('date', date);
  } else {
    // Create new record
    const locationMetadata = await getLocationMetadata(location);
    
    await supabaseClient
      .from('location_analytics')
      .insert({
        user_id,
        date,
        total_locations_recorded: 1,
        unique_locations_visited: 1,
        total_distance_traveled: 0,
        identifications_with_location: activity_type === 'identification' ? 1 : 0,
        discoveries_made: activity_type === 'discovery' ? 1 : 0,
        public_locations_shared: activity_type === 'public_share' ? 1 : 0,
        countries_visited: locationMetadata.country ? [locationMetadata.country] : [],
        cities_visited: locationMetadata.city ? [locationMetadata.city] : [],
        last_location: location,
      });
  }

  return { success: true };
}

/**
 * Generate location recommendations for user
 */
async function generateLocationRecommendations(supabaseClient: any, data: {
  user_id: string;
  latitude: number;
  longitude: number;
  radius_km?: number;
  recommendation_type?: string;
}) {
  const { user_id, latitude, longitude, radius_km = 25, recommendation_type } = data;

  // Get user's identification history to understand preferences
  const { data: userHistory } = await supabaseClient
    .from('species_identifications')
    .select('species_id, species_category, habitat_type')
    .eq('user_id', user_id)
    .limit(100);

  // Analyze user preferences
  const preferredCategories = getPreferredCategories(userHistory || []);
  const preferredHabitats = getPreferredHabitats(userHistory || []);

  // Find locations with high species diversity
  const { data: hotspots } = await supabaseClient.rpc('find_biodiversity_hotspots', {
    center_lat: latitude,
    center_lng: longitude,
    radius_km,
    preferred_categories: preferredCategories,
    preferred_habitats: preferredHabitats,
  });

  // Generate recommendations based on different criteria
  const recommendations = [];

  for (const hotspot of hotspots || []) {
    const relevanceScore = calculateRelevanceScore(hotspot, preferredCategories, preferredHabitats);
    
    if (relevanceScore > 0.3) { // Minimum relevance threshold
      recommendations.push({
        latitude: hotspot.latitude,
        longitude: hotspot.longitude,
        recommendation_type: recommendation_type || 'exploration',
        title: hotspot.location_name || `Biodiversity Hotspot`,
        description: `${hotspot.species_count} species recorded here, including ${hotspot.rare_species_count} rare species.`,
        relevance_score: relevanceScore,
        difficulty_level: calculateDifficultyLevel(hotspot),
        expected_species: hotspot.common_species || [],
        species_count_estimate: hotspot.species_count,
        best_time_to_visit: hotspot.best_times || {},
        accessibility_info: hotspot.accessibility_notes,
        estimated_duration: estimateVisitDuration(hotspot.species_count),
      });
    }
  }

  // Sort by relevance score
  recommendations.sort((a, b) => b.relevance_score - a.relevance_score);

  // Save recommendations to database
  for (const rec of recommendations.slice(0, 10)) { // Limit to top 10
    await supabaseClient
      .from('location_recommendations')
      .upsert({
        user_id,
        ...rec,
        generated_at: new Date().toISOString(),
        expires_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days
        recommendation_source: 'algorithm',
      });
  }

  return recommendations.slice(0, 10);
}

// Helper functions

function calculateDistance(lat1: number, lon1: number, lat2: number, lon2: number): number {
  const R = 6371e3; // Earth's radius in meters
  const φ1 = lat1 * Math.PI / 180;
  const φ2 = lat2 * Math.PI / 180;
  const Δφ = (lat2 - lat1) * Math.PI / 180;
  const Δλ = (lon2 - lon1) * Math.PI / 180;

  const a = Math.sin(Δφ / 2) * Math.sin(Δφ / 2) +
            Math.cos(φ1) * Math.cos(φ2) *
            Math.sin(Δλ / 2) * Math.sin(Δλ / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

  return R * c;
}

async function getLocationMetadata(location: { latitude: number; longitude: number }) {
  // In a real implementation, this would use a geocoding service
  // For now, return mock data
  return {
    country: 'Unknown',
    city: 'Unknown',
    habitat_type: 'mixed',
    elevation: 0,
    climate_zone: 'temperate',
  };
}

function getPreferredCategories(history: any[]): string[] {
  const categoryCount: { [key: string]: number } = {};
  
  history.forEach(item => {
    if (item.species_category) {
      categoryCount[item.species_category] = (categoryCount[item.species_category] || 0) + 1;
    }
  });

  return Object.entries(categoryCount)
    .sort(([,a], [,b]) => b - a)
    .slice(0, 3)
    .map(([category]) => category);
}

function getPreferredHabitats(history: any[]): string[] {
  const habitatCount: { [key: string]: number } = {};
  
  history.forEach(item => {
    if (item.habitat_type) {
      habitatCount[item.habitat_type] = (habitatCount[item.habitat_type] || 0) + 1;
    }
  });

  return Object.entries(habitatCount)
    .sort(([,a], [,b]) => b - a)
    .slice(0, 3)
    .map(([habitat]) => habitat);
}

function calculateRelevanceScore(hotspot: any, preferredCategories: string[], preferredHabitats: string[]): number {
  let score = 0.5; // Base score

  // Boost score for preferred categories
  if (hotspot.dominant_categories) {
    const matchingCategories = hotspot.dominant_categories.filter((cat: string) => 
      preferredCategories.includes(cat)
    );
    score += matchingCategories.length * 0.1;
  }

  // Boost score for preferred habitats
  if (preferredHabitats.includes(hotspot.habitat_type)) {
    score += 0.2;
  }

  // Boost score for species diversity
  if (hotspot.species_count > 50) score += 0.1;
  if (hotspot.rare_species_count > 5) score += 0.1;

  return Math.min(score, 1.0);
}

function calculateDifficultyLevel(hotspot: any): string {
  const accessibility = hotspot.accessibility_score || 0.5;
  const terrain = hotspot.terrain_difficulty || 0.5;
  
  const difficulty = (1 - accessibility) + terrain;
  
  if (difficulty < 0.3) return 'easy';
  if (difficulty < 0.6) return 'moderate';
  if (difficulty < 0.8) return 'challenging';
  return 'expert';
}

function estimateVisitDuration(speciesCount: number): string {
  if (speciesCount < 10) return '1-2 hours';
  if (speciesCount < 25) return '2-4 hours';
  if (speciesCount < 50) return '4-6 hours';
  return 'Full day';
}

async function generateLocationSuggestions(supabaseClient: any, data: any) {
  // Generate contextual suggestions based on geofence properties
  return [
    {
      type: 'species_hunt',
      title: 'Look for local species',
      description: 'This area is known for diverse wildlife',
    },
  ];
}
