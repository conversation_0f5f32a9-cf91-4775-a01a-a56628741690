import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  RefreshControl,
  Modal,
  TextInput,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import {
  TestTube2,
  Users,
  MessageSquare,
  BarChart3,
  Settings,
  Plus,
  Eye,
  Clock,
  CheckCircle,
  AlertCircle,
  Star,
  TrendingUp,
  Activity,
  Send,
  FileText,
  Download,
  Bug,
  Lightbulb,
  Target,
} from 'lucide-react-native';
import { BetaTestingService, BetaProgram, BetaParticipant, BetaFeedback } from '../services/BetaTestingService';

interface BetaProgramDashboardProps {
  userRole?: 'admin' | 'beta_manager' | 'participant';
}

export const BetaProgramDashboard: React.FC<BetaProgramDashboardProps> = ({
  userRole = 'participant',
}) => {
  const [programs, setPrograms] = useState<BetaProgram[]>([]);
  const [participations, setParticipations] = useState<BetaParticipant[]>([]);
  const [feedback, setFeedback] = useState<BetaFeedback[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [activeTab, setActiveTab] = useState<'programs' | 'feedback' | 'analytics'>('programs');
  const [showApplicationModal, setShowApplicationModal] = useState(false);
  const [selectedProgram, setSelectedProgram] = useState<BetaProgram | null>(null);
  const [applicationReason, setApplicationReason] = useState('');

  const betaTestingService = BetaTestingService.getInstance();

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      
      await Promise.all([
        loadPrograms(),
        loadParticipations(),
        loadFeedback(),
      ]);
    } catch (error) {
      console.error('Error loading dashboard data:', error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const loadPrograms = async () => {
    try {
      if (userRole === 'participant') {
        const availablePrograms = await betaTestingService.getAvailablePrograms();
        setPrograms(availablePrograms);
      } else {
        // Admin/manager would load all programs
        setPrograms([]);
      }
    } catch (error) {
      console.error('Error loading programs:', error);
    }
  };

  const loadParticipations = async () => {
    try {
      const userParticipations = await betaTestingService.getUserParticipations();
      setParticipations(userParticipations);
    } catch (error) {
      console.error('Error loading participations:', error);
    }
  };

  const loadFeedback = async () => {
    try {
      // Load feedback for user's active programs
      const allFeedback: BetaFeedback[] = [];
      
      for (const participation of participations) {
        if (participation.status === 'active') {
          const programFeedback = await betaTestingService.getProgramFeedback(participation.program_id);
          allFeedback.push(...programFeedback);
        }
      }
      
      setFeedback(allFeedback);
    } catch (error) {
      console.error('Error loading feedback:', error);
    }
  };

  const onRefresh = () => {
    setRefreshing(true);
    loadDashboardData();
  };

  const handleApplyToProgram = async () => {
    if (!selectedProgram || !applicationReason.trim()) {
      Alert.alert('Error', 'Please provide a reason for joining this beta program');
      return;
    }

    try {
      await betaTestingService.applyToProgram(
        selectedProgram.id,
        applicationReason,
        {}
      );

      Alert.alert(
        'Application Submitted',
        selectedProgram.auto_approve 
          ? 'You have been automatically approved for this beta program!'
          : 'Your application has been submitted and is pending review.'
      );

      setShowApplicationModal(false);
      setApplicationReason('');
      setSelectedProgram(null);
      await loadDashboardData();
    } catch (error) {
      Alert.alert('Error', error instanceof Error ? error.message : 'Failed to apply to program');
    }
  };

  const handleLeaveProgram = async (programId: string) => {
    Alert.alert(
      'Leave Beta Program',
      'Are you sure you want to leave this beta program?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Leave',
          style: 'destructive',
          onPress: async () => {
            try {
              await betaTestingService.leaveProgram(programId);
              Alert.alert('Success', 'You have left the beta program');
              await loadDashboardData();
            } catch (error) {
              Alert.alert('Error', 'Failed to leave program');
            }
          },
        },
      ]
    );
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return '#10B981';
      case 'recruiting': return '#3B82F6';
      case 'pending': return '#F59E0B';
      case 'approved': return '#10B981';
      case 'completed': return '#6B7280';
      case 'paused': return '#F59E0B';
      default: return '#6B7280';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active': return CheckCircle;
      case 'recruiting': return Users;
      case 'pending': return Clock;
      case 'approved': return CheckCircle;
      case 'completed': return CheckCircle;
      case 'paused': return AlertCircle;
      default: return AlertCircle;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'critical': return '#EF4444';
      case 'high': return '#F59E0B';
      case 'medium': return '#3B82F6';
      case 'low': return '#6B7280';
      default: return '#6B7280';
    }
  };

  const getFeedbackTypeIcon = (type: string) => {
    switch (type) {
      case 'bug_report': return Bug;
      case 'feature_request': return Lightbulb;
      case 'usability': return Users;
      case 'performance': return Activity;
      default: return MessageSquare;
    }
  };

  const renderPrograms = () => (
    <View style={styles.section}>
      <View style={styles.sectionHeader}>
        <Text style={styles.sectionTitle}>Available Programs ({programs.length})</Text>
      </View>

      {programs.map((program) => {
        const participation = participations.find(p => p.program_id === program.id);
        const StatusIcon = getStatusIcon(participation?.status || program.status);
        
        return (
          <View key={program.id} style={styles.programCard}>
            <View style={styles.programHeader}>
              <View style={styles.programInfo}>
                <Text style={styles.programName}>{program.program_name}</Text>
                <Text style={styles.programType}>{program.program_type.replace('_', ' ')}</Text>
                {program.description && (
                  <Text style={styles.programDescription}>{program.description}</Text>
                )}
              </View>
              
              <View style={styles.programStatus}>
                <View style={[styles.statusBadge, { backgroundColor: getStatusColor(participation?.status || program.status) }]}>
                  <StatusIcon size={12} color="white" />
                  <Text style={styles.statusText}>
                    {participation?.status?.toUpperCase() || program.status.toUpperCase()}
                  </Text>
                </View>
              </View>
            </View>

            <View style={styles.programMetrics}>
              <View style={styles.metricItem}>
                <Users size={16} color="#6B7280" />
                <Text style={styles.metricText}>
                  {program.current_participants}
                  {program.max_participants && `/${program.max_participants}`} participants
                </Text>
              </View>
              
              <View style={styles.metricItem}>
                <Clock size={16} color="#6B7280" />
                <Text style={styles.metricText}>
                  {program.feedback_frequency} feedback
                </Text>
              </View>
              
              {program.platform_restrictions.length > 0 && (
                <View style={styles.metricItem}>
                  <Target size={16} color="#6B7280" />
                  <Text style={styles.metricText}>
                    {program.platform_restrictions.join(', ')}
                  </Text>
                </View>
              )}
            </View>

            {program.tags.length > 0 && (
              <View style={styles.programTags}>
                {program.tags.map((tag, index) => (
                  <View key={index} style={styles.tag}>
                    <Text style={styles.tagText}>{tag}</Text>
                  </View>
                ))}
              </View>
            )}

            <View style={styles.programActions}>
              {!participation && program.status === 'recruiting' && (
                <TouchableOpacity
                  style={styles.primaryButton}
                  onPress={() => {
                    setSelectedProgram(program);
                    setShowApplicationModal(true);
                  }}
                >
                  <Plus size={16} color="white" />
                  <Text style={styles.primaryButtonText}>Apply</Text>
                </TouchableOpacity>
              )}
              
              {participation && participation.status === 'active' && (
                <>
                  <TouchableOpacity
                    style={styles.secondaryButton}
                    onPress={() => handleLeaveProgram(program.id)}
                  >
                    <Text style={styles.secondaryButtonText}>Leave Program</Text>
                  </TouchableOpacity>
                  
                  <TouchableOpacity style={styles.primaryButton}>
                    <MessageSquare size={16} color="white" />
                    <Text style={styles.primaryButtonText}>Give Feedback</Text>
                  </TouchableOpacity>
                </>
              )}
              
              {participation && participation.status === 'pending' && (
                <View style={styles.pendingIndicator}>
                  <Clock size={16} color="#F59E0B" />
                  <Text style={styles.pendingText}>Application Pending</Text>
                </View>
              )}
            </View>
          </View>
        );
      })}
    </View>
  );

  const renderFeedback = () => (
    <View style={styles.section}>
      <View style={styles.sectionHeader}>
        <Text style={styles.sectionTitle}>Recent Feedback ({feedback.length})</Text>
      </View>

      {feedback.slice(0, 10).map((item) => {
        const TypeIcon = getFeedbackTypeIcon(item.feedback_type);
        
        return (
          <View key={item.id} style={styles.feedbackCard}>
            <View style={styles.feedbackHeader}>
              <View style={styles.feedbackInfo}>
                <View style={styles.feedbackTitleRow}>
                  <TypeIcon size={16} color="#6B7280" />
                  <Text style={styles.feedbackTitle}>{item.title}</Text>
                </View>
                <Text style={styles.feedbackDescription} numberOfLines={2}>
                  {item.description}
                </Text>
              </View>
              
              <View style={styles.feedbackMeta}>
                <View style={[styles.priorityBadge, { backgroundColor: getPriorityColor(item.priority) }]}>
                  <Text style={styles.priorityText}>{item.priority.toUpperCase()}</Text>
                </View>
                <Text style={styles.feedbackDate}>
                  {new Date(item.created_at).toLocaleDateString()}
                </Text>
              </View>
            </View>

            <View style={styles.feedbackStats}>
              <View style={styles.feedbackStat}>
                <TrendingUp size={14} color="#10B981" />
                <Text style={styles.feedbackStatText}>{item.upvotes}</Text>
              </View>
              
              <View style={styles.feedbackStat}>
                <MessageSquare size={14} color="#6B7280" />
                <Text style={styles.feedbackStatText}>{item.comments_count}</Text>
              </View>
              
              <View style={[styles.statusIndicator, { backgroundColor: getStatusColor(item.status) }]}>
                <Text style={styles.statusIndicatorText}>{item.status.replace('_', ' ')}</Text>
              </View>
            </View>
          </View>
        );
      })}
    </View>
  );

  const renderAnalytics = () => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>Your Beta Activity</Text>
      
      <View style={styles.analyticsGrid}>
        <View style={styles.analyticsCard}>
          <TestTube2 size={24} color="#3B82F6" />
          <Text style={styles.analyticsValue}>{participations.length}</Text>
          <Text style={styles.analyticsLabel}>Programs Joined</Text>
        </View>
        
        <View style={styles.analyticsCard}>
          <MessageSquare size={24} color="#10B981" />
          <Text style={styles.analyticsValue}>
            {participations.reduce((sum, p) => sum + p.feedback_submissions, 0)}
          </Text>
          <Text style={styles.analyticsLabel}>Feedback Submitted</Text>
        </View>
        
        <View style={styles.analyticsCard}>
          <Bug size={24} color="#EF4444" />
          <Text style={styles.analyticsValue}>
            {participations.reduce((sum, p) => sum + p.bug_reports, 0)}
          </Text>
          <Text style={styles.analyticsLabel}>Bugs Reported</Text>
        </View>
        
        <View style={styles.analyticsCard}>
          <Star size={24} color="#F59E0B" />
          <Text style={styles.analyticsValue}>
            {(participations.reduce((sum, p) => sum + p.engagement_score, 0) / participations.length || 0).toFixed(1)}
          </Text>
          <Text style={styles.analyticsLabel}>Avg Engagement</Text>
        </View>
      </View>
    </View>
  );

  const renderApplicationModal = () => (
    <Modal
      visible={showApplicationModal}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={() => setShowApplicationModal(false)}
    >
      <View style={styles.modalContainer}>
        <View style={styles.modalHeader}>
          <Text style={styles.modalTitle}>Apply to Beta Program</Text>
          <TouchableOpacity onPress={() => setShowApplicationModal(false)}>
            <Text style={styles.closeButton}>Cancel</Text>
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.modalContent}>
          {selectedProgram && (
            <>
              <Text style={styles.modalProgramName}>{selectedProgram.program_name}</Text>
              <Text style={styles.modalProgramDescription}>{selectedProgram.description}</Text>
              
              {selectedProgram.instructions && (
                <View style={styles.instructionsSection}>
                  <Text style={styles.instructionsTitle}>Instructions:</Text>
                  <Text style={styles.instructionsText}>{selectedProgram.instructions}</Text>
                </View>
              )}

              <View style={styles.inputSection}>
                <Text style={styles.inputLabel}>Why do you want to join this beta program?</Text>
                <TextInput
                  style={styles.textInput}
                  multiline
                  numberOfLines={4}
                  value={applicationReason}
                  onChangeText={setApplicationReason}
                  placeholder="Tell us about your interest and relevant experience..."
                  placeholderTextColor="#9CA3AF"
                />
              </View>

              <TouchableOpacity
                style={[styles.submitButton, !applicationReason.trim() && styles.disabledButton]}
                onPress={handleApplyToProgram}
                disabled={!applicationReason.trim()}
              >
                <Send size={16} color="white" />
                <Text style={styles.submitButtonText}>Submit Application</Text>
              </TouchableOpacity>
            </>
          )}
        </ScrollView>
      </View>
    </Modal>
  );

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <Activity size={32} color="#3B82F6" />
        <Text style={styles.loadingText}>Loading beta programs...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Header */}
      <LinearGradient
        colors={['#10B981', '#059669']}
        style={styles.header}
      >
        <TestTube2 size={32} color="white" />
        <Text style={styles.headerTitle}>Beta Testing</Text>
        <Text style={styles.headerSubtitle}>
          Join beta programs and help shape the future of Bioscan+
        </Text>
      </LinearGradient>

      {/* Tab Navigation */}
      <View style={styles.tabContainer}>
        {[
          { key: 'programs', label: 'Programs', icon: TestTube2 },
          { key: 'feedback', label: 'Feedback', icon: MessageSquare },
          { key: 'analytics', label: 'Activity', icon: BarChart3 },
        ].map(({ key, label, icon: Icon }) => (
          <TouchableOpacity
            key={key}
            style={[styles.tab, activeTab === key && styles.activeTab]}
            onPress={() => setActiveTab(key as any)}
          >
            <Icon size={18} color={activeTab === key ? '#10B981' : '#6B7280'} />
            <Text style={[styles.tabText, activeTab === key && styles.activeTabText]}>
              {label}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      {/* Content */}
      <ScrollView
        style={styles.content}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {activeTab === 'programs' && renderPrograms()}
        {activeTab === 'feedback' && renderFeedback()}
        {activeTab === 'analytics' && renderAnalytics()}
      </ScrollView>

      {/* Application Modal */}
      {renderApplicationModal()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#F9FAFB',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#6B7280',
  },
  header: {
    padding: 24,
    paddingTop: 60,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: 'white',
    marginTop: 8,
  },
  headerSubtitle: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.8)',
    marginTop: 4,
    textAlign: 'center',
  },
  tabContainer: {
    flexDirection: 'row',
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  tab: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    gap: 6,
  },
  activeTab: {
    borderBottomWidth: 2,
    borderBottomColor: '#10B981',
  },
  tabText: {
    fontSize: 12,
    fontWeight: '500',
    color: '#6B7280',
  },
  activeTabText: {
    color: '#10B981',
  },
  content: {
    flex: 1,
    padding: 20,
  },
  section: {
    marginBottom: 24,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827',
  },
  programCard: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  programHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  programInfo: {
    flex: 1,
    marginRight: 12,
  },
  programName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 2,
  },
  programType: {
    fontSize: 12,
    color: '#6B7280',
    textTransform: 'capitalize',
    marginBottom: 4,
  },
  programDescription: {
    fontSize: 14,
    color: '#6B7280',
    lineHeight: 18,
  },
  programStatus: {
    alignItems: 'flex-end',
  },
  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 4,
    gap: 4,
  },
  statusText: {
    fontSize: 10,
    fontWeight: '600',
    color: 'white',
  },
  programMetrics: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
    marginBottom: 12,
  },
  metricItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  metricText: {
    fontSize: 12,
    color: '#6B7280',
  },
  programTags: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 6,
    marginBottom: 12,
  },
  tag: {
    backgroundColor: '#F3F4F6',
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 4,
  },
  tagText: {
    fontSize: 10,
    color: '#6B7280',
  },
  programActions: {
    flexDirection: 'row',
    gap: 8,
    alignItems: 'center',
  },
  primaryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#10B981',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
    gap: 4,
  },
  primaryButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: 'white',
  },
  secondaryButton: {
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
  },
  secondaryButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#6B7280',
  },
  pendingIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  pendingText: {
    fontSize: 14,
    color: '#F59E0B',
    fontWeight: '500',
  },
  feedbackCard: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  feedbackHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  feedbackInfo: {
    flex: 1,
    marginRight: 12,
  },
  feedbackTitleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
    marginBottom: 4,
  },
  feedbackTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#111827',
    flex: 1,
  },
  feedbackDescription: {
    fontSize: 14,
    color: '#6B7280',
    lineHeight: 18,
  },
  feedbackMeta: {
    alignItems: 'flex-end',
    gap: 4,
  },
  priorityBadge: {
    borderRadius: 8,
    paddingHorizontal: 6,
    paddingVertical: 2,
  },
  priorityText: {
    fontSize: 10,
    fontWeight: '600',
    color: 'white',
  },
  feedbackDate: {
    fontSize: 12,
    color: '#9CA3AF',
  },
  feedbackStats: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  feedbackStat: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  feedbackStatText: {
    fontSize: 12,
    color: '#6B7280',
  },
  statusIndicator: {
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 4,
    marginLeft: 'auto',
  },
  statusIndicatorText: {
    fontSize: 10,
    fontWeight: '600',
    color: 'white',
    textTransform: 'capitalize',
  },
  analyticsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  analyticsCard: {
    flex: 1,
    minWidth: '45%',
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  analyticsValue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#111827',
    marginTop: 8,
  },
  analyticsLabel: {
    fontSize: 12,
    color: '#6B7280',
    marginTop: 4,
    textAlign: 'center',
  },
  modalContainer: {
    flex: 1,
    backgroundColor: 'white',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827',
  },
  closeButton: {
    fontSize: 16,
    color: '#10B981',
    fontWeight: '500',
  },
  modalContent: {
    flex: 1,
    padding: 20,
  },
  modalProgramName: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#111827',
    marginBottom: 8,
  },
  modalProgramDescription: {
    fontSize: 16,
    color: '#6B7280',
    lineHeight: 22,
    marginBottom: 20,
  },
  instructionsSection: {
    backgroundColor: '#F9FAFB',
    borderRadius: 8,
    padding: 16,
    marginBottom: 20,
  },
  instructionsTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 8,
  },
  instructionsText: {
    fontSize: 14,
    color: '#6B7280',
    lineHeight: 20,
  },
  inputSection: {
    marginBottom: 24,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: '#111827',
    marginBottom: 8,
  },
  textInput: {
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    color: '#111827',
    textAlignVertical: 'top',
    minHeight: 100,
  },
  submitButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#10B981',
    borderRadius: 8,
    padding: 16,
    gap: 8,
  },
  disabledButton: {
    backgroundColor: '#9CA3AF',
  },
  submitButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: 'white',
  },
});

export default BetaProgramDashboard;
