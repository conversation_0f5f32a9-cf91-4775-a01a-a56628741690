import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Dimensions,
  TouchableOpacity,
  ScrollView,
  SafeAreaView,
  Platform,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import {
  Camera,
  Sparkles,
  BookOpen,
  Users,
  Target,
  Zap,
  Award,
  ArrowRight,
  ArrowLeft,
  Check,
} from 'lucide-react-native';

const { width, height } = Dimensions.get('window');

interface OnboardingStep {
  id: string;
  title: string;
  subtitle: string;
  description: string;
  icon: any;
  gradient: string[];
  features: string[];
}

const onboardingSteps: OnboardingStep[] = [
  {
    id: 'welcome',
    title: 'Welcome to BioScan',
    subtitle: 'Discover Nature with AI',
    description: 'Transform your curiosity into knowledge. Identify any plant, animal, or insect instantly using the power of artificial intelligence.',
    icon: Sparkles,
    gradient: ['#22C55E', '#16A34A'],
    features: [
      'AI-powered species identification',
      'Instant results with high accuracy',
      'Learn about biodiversity around you',
    ],
  },
  {
    id: 'scan',
    title: 'Scan Anything',
    subtitle: 'Point, Shoot, Discover',
    description: 'Simply point your camera at any living thing and let our advanced AI identify it for you. Works with plants, animals, insects, and more.',
    icon: Camera,
    gradient: ['#3B82F6', '#1D4ED8'],
    features: [
      'Real-time camera scanning',
      'Photo upload from gallery',
      'Voice description support',
    ],
  },
  {
    id: 'collect',
    title: 'Build Your Collection',
    subtitle: 'Organize Your Discoveries',
    description: 'Keep track of all your findings in a beautiful digital collection. Add notes, photos, and locations to create your personal nature journal.',
    icon: BookOpen,
    gradient: ['#8B5CF6', '#7C3AED'],
    features: [
      'Digital specimen collection',
      'Detailed species information',
      'Personal discovery journal',
    ],
  },
  {
    id: 'community',
    title: 'Join the Community',
    subtitle: 'Share and Learn Together',
    description: 'Connect with fellow nature enthusiasts, share your discoveries, and learn from experts around the world.',
    icon: Users,
    gradient: ['#F59E0B', '#D97706'],
    features: [
      'Share discoveries with others',
      'Learn from expert naturalists',
      'Participate in challenges',
    ],
  },
  {
    id: 'gamification',
    title: 'Earn Achievements',
    subtitle: 'Level Up Your Knowledge',
    description: 'Complete challenges, maintain streaks, and unlock achievements as you explore the natural world around you.',
    icon: Award,
    gradient: ['#EF4444', '#DC2626'],
    features: [
      'Daily scanning streaks',
      'Discovery achievements',
      'Leaderboards and challenges',
    ],
  },
];

interface OnboardingWalkthroughProps {
  onComplete: () => void;
}

export default function OnboardingWalkthrough({ onComplete }: OnboardingWalkthroughProps) {
  const [currentStep, setCurrentStep] = useState(0);
  const scrollViewRef = useRef<ScrollView>(null);

  const handleNext = () => {
    if (currentStep < onboardingSteps.length - 1) {
      const nextStep = currentStep + 1;
      setCurrentStep(nextStep);
      scrollViewRef.current?.scrollTo({
        x: nextStep * width,
        animated: true,
      });
    } else {
      onComplete();
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      const prevStep = currentStep - 1;
      setCurrentStep(prevStep);
      scrollViewRef.current?.scrollTo({
        x: prevStep * width,
        animated: true,
      });
    }
  };

  const handleSkip = () => {
    onComplete();
  };

  const renderStep = (step: OnboardingStep, index: number) => {
    const IconComponent = step.icon;
    
    return (
      <View key={step.id} style={styles.stepContainer}>
        <LinearGradient colors={step.gradient} style={styles.stepGradient}>
          <SafeAreaView style={styles.stepContent}>
            {/* Header */}
            <View style={styles.stepHeader}>
              <TouchableOpacity
                style={styles.skipButton}
                onPress={handleSkip}>
                <Text style={styles.skipText}>Skip</Text>
              </TouchableOpacity>
            </View>

            {/* Icon */}
            <View style={styles.iconContainer}>
              <View style={styles.iconBackground}>
                <IconComponent size={60} color="#FFFFFF" />
              </View>
            </View>

            {/* Content */}
            <View style={styles.textContainer}>
              <Text style={styles.stepTitle}>{step.title}</Text>
              <Text style={styles.stepSubtitle}>{step.subtitle}</Text>
              <Text style={styles.stepDescription}>{step.description}</Text>

              {/* Features */}
              <View style={styles.featuresContainer}>
                {step.features.map((feature, featureIndex) => (
                  <View key={featureIndex} style={styles.featureItem}>
                    <Check size={16} color="#FFFFFF" />
                    <Text style={styles.featureText}>{feature}</Text>
                  </View>
                ))}
              </View>
            </View>

            {/* Navigation */}
            <View style={styles.navigationContainer}>
              {/* Progress Indicators */}
              <View style={styles.progressContainer}>
                {onboardingSteps.map((_, progressIndex) => (
                  <View
                    key={progressIndex}
                    style={[
                      styles.progressDot,
                      progressIndex === index && styles.progressDotActive,
                    ]}
                  />
                ))}
              </View>

              {/* Navigation Buttons */}
              <View style={styles.buttonContainer}>
                {currentStep > 0 && (
                  <TouchableOpacity
                    style={styles.previousButton}
                    onPress={handlePrevious}>
                    <ArrowLeft size={20} color="#FFFFFF" />
                    <Text style={styles.previousButtonText}>Previous</Text>
                  </TouchableOpacity>
                )}

                <TouchableOpacity
                  style={[
                    styles.nextButton,
                    currentStep === 0 && styles.nextButtonFull,
                  ]}
                  onPress={handleNext}>
                  <Text style={styles.nextButtonText}>
                    {currentStep === onboardingSteps.length - 1 ? 'Get Started' : 'Next'}
                  </Text>
                  <ArrowRight size={20} color="#FFFFFF" />
                </TouchableOpacity>
              </View>
            </View>
          </SafeAreaView>
        </LinearGradient>
      </View>
    );
  };

  return (
    <View style={styles.container}>
      <ScrollView
        ref={scrollViewRef}
        horizontal
        pagingEnabled
        showsHorizontalScrollIndicator={false}
        scrollEnabled={false}
        style={styles.scrollView}>
        {onboardingSteps.map((step, index) => renderStep(step, index))}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  stepContainer: {
    width,
    height,
  },
  stepGradient: {
    flex: 1,
  },
  stepContent: {
    flex: 1,
    paddingHorizontal: 24,
  },
  stepHeader: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    paddingTop: Platform.OS === 'ios' ? 20 : 40,
    paddingBottom: 20,
  },
  skipButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 20,
  },
  skipText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#FFFFFF',
  },
  iconContainer: {
    alignItems: 'center',
    marginBottom: 40,
  },
  iconBackground: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.3,
    shadowRadius: 16,
    elevation: 12,
  },
  textContainer: {
    flex: 1,
    alignItems: 'center',
  },
  stepTitle: {
    fontSize: 32,
    fontFamily: 'Inter-Bold',
    color: '#FFFFFF',
    textAlign: 'center',
    marginBottom: 8,
  },
  stepSubtitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: '#FFFFFF',
    opacity: 0.9,
    textAlign: 'center',
    marginBottom: 16,
  },
  stepDescription: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#FFFFFF',
    opacity: 0.8,
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 32,
    paddingHorizontal: 16,
  },
  featuresContainer: {
    alignSelf: 'stretch',
    gap: 12,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
    paddingHorizontal: 16,
  },
  featureText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#FFFFFF',
    opacity: 0.9,
  },
  navigationContainer: {
    paddingBottom: Platform.OS === 'ios' ? 40 : 20,
  },
  progressContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 32,
    gap: 8,
  },
  progressDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
  },
  progressDotActive: {
    backgroundColor: '#FFFFFF',
    width: 24,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    gap: 16,
  },
  previousButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 12,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 25,
    gap: 8,
  },
  previousButtonText: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#FFFFFF',
  },
  nextButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 24,
    paddingVertical: 16,
    backgroundColor: '#FFFFFF',
    borderRadius: 25,
    gap: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 4,
  },
  nextButtonFull: {
    flex: 1,
  },
  nextButtonText: {
    fontSize: 16,
    fontFamily: 'Inter-Bold',
    color: '#111827',
  },
});