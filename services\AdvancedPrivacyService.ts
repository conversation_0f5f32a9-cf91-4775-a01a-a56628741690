import { supabase } from '../lib/supabase';
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as Location from 'expo-location';
import * as Device from 'expo-device';

export interface PrivacyConsent {
  id: string;
  consent_type: string;
  consent_status: boolean;
  consent_version: string;
  legal_basis: string;
  consent_source: string;
  consent_method: string;
  expires_at?: string;
  withdrawn_at?: string;
  created_at: string;
}

export interface AdvancedPrivacyPreferences {
  data_minimization_enabled: boolean;
  auto_delete_enabled: boolean;
  auto_delete_period: string;
  allow_third_party_sharing: boolean;
  allowed_third_parties: string[];
  data_sharing_purposes: string[];
  allow_behavioral_analytics: boolean;
  allow_cross_device_tracking: boolean;
  allow_fingerprinting: boolean;
  allow_profiling: boolean;
  allow_biometric_processing: boolean;
  biometric_retention_period: string;
  allow_health_data_inference: boolean;
  location_precision_level: 'exact' | 'neighborhood' | 'city' | 'region' | 'country' | 'none';
  location_history_retention: string;
  allow_location_inference: boolean;
  preferred_contact_method: 'email' | 'sms' | 'push' | 'in_app' | 'none';
  communication_frequency: 'minimal' | 'normal' | 'frequent';
  auto_export_enabled: boolean;
  export_frequency: string;
  export_format: 'json' | 'csv' | 'xml';
  require_2fa_for_sensitive: boolean;
  session_timeout: string;
  require_reauth_for_deletion: boolean;
}

export interface DataAccessEvent {
  access_type: 'read' | 'write' | 'update' | 'delete' | 'export' | 'share' | 'process';
  data_category: string;
  data_fields?: string[];
  accessor_type: 'user' | 'system' | 'admin' | 'third_party' | 'api';
  access_reason?: string;
}

export interface PrivacyRiskAssessment {
  assessment_type: string;
  risk_level: 'low' | 'medium' | 'high' | 'critical';
  risk_score: number;
  data_sensitivity_level: string;
  mitigation_measures: string[];
}

export class AdvancedPrivacyService {
  private static instance: AdvancedPrivacyService;
  private consentCache: Map<string, PrivacyConsent[]> = new Map();
  private preferencesCache: AdvancedPrivacyPreferences | null = null;

  static getInstance(): AdvancedPrivacyService {
    if (!AdvancedPrivacyService.instance) {
      AdvancedPrivacyService.instance = new AdvancedPrivacyService();
    }
    return AdvancedPrivacyService.instance;
  }

  /**
   * Initialize privacy service and load user preferences
   */
  async initialize(): Promise<void> {
    try {
      await this.loadPrivacyPreferences();
      await this.loadPrivacyConsents();
      await this.setupDataMinimization();
    } catch (error) {
      console.error('Error initializing AdvancedPrivacyService:', error);
    }
  }

  /**
   * Get user's privacy consents
   */
  async getPrivacyConsents(): Promise<PrivacyConsent[]> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      // Check cache first
      const cached = this.consentCache.get(user.id);
      if (cached) return cached;

      const { data, error } = await supabase
        .from('privacy_consents')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false });

      if (error) throw error;

      const consents = data || [];
      this.consentCache.set(user.id, consents);
      return consents;
    } catch (error) {
      console.error('Error getting privacy consents:', error);
      return [];
    }
  }

  /**
   * Update privacy consent
   */
  async updatePrivacyConsent(
    consentType: string,
    status: boolean,
    legalBasis: string = 'consent',
    source: string = 'settings'
  ): Promise<boolean> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      // Get device and location context
      const context = await this.getConsentContext();

      const consentData = {
        user_id: user.id,
        consent_type: consentType,
        consent_status: status,
        consent_version: '1.0',
        legal_basis: legalBasis,
        consent_source: source,
        consent_method: 'explicit_opt_in',
        ip_address: context.ipAddress,
        user_agent: context.userAgent,
        device_fingerprint: context.deviceFingerprint,
        geolocation: context.geolocation,
      };

      const { error } = await supabase
        .from('privacy_consents')
        .upsert(consentData);

      if (error) throw error;

      // Clear cache
      this.consentCache.delete(user.id);

      // Log the consent change
      await this.logDataAccess({
        access_type: 'update',
        data_category: 'privacy_consent',
        data_fields: [consentType],
        accessor_type: 'user',
        access_reason: `User ${status ? 'granted' : 'withdrew'} consent for ${consentType}`,
      });

      return true;
    } catch (error) {
      console.error('Error updating privacy consent:', error);
      return false;
    }
  }

  /**
   * Get advanced privacy preferences
   */
  async getAdvancedPrivacyPreferences(): Promise<AdvancedPrivacyPreferences | null> {
    try {
      if (this.preferencesCache) return this.preferencesCache;

      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      const { data, error } = await supabase
        .from('advanced_privacy_preferences')
        .select('*')
        .eq('user_id', user.id)
        .single();

      if (error && error.code !== 'PGRST116') throw error;

      const preferences = data || this.getDefaultPrivacyPreferences();
      this.preferencesCache = preferences;
      return preferences;
    } catch (error) {
      console.error('Error getting advanced privacy preferences:', error);
      return this.getDefaultPrivacyPreferences();
    }
  }

  /**
   * Update advanced privacy preferences
   */
  async updateAdvancedPrivacyPreferences(
    preferences: Partial<AdvancedPrivacyPreferences>
  ): Promise<boolean> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      const { error } = await supabase
        .from('advanced_privacy_preferences')
        .upsert({
          user_id: user.id,
          ...preferences,
          updated_at: new Date().toISOString(),
        });

      if (error) throw error;

      // Clear cache
      this.preferencesCache = null;

      // Log the preference change
      await this.logDataAccess({
        access_type: 'update',
        data_category: 'privacy_preferences',
        data_fields: Object.keys(preferences),
        accessor_type: 'user',
        access_reason: 'User updated privacy preferences',
      });

      // Apply data minimization if enabled
      if (preferences.data_minimization_enabled) {
        await this.applyDataMinimization();
      }

      return true;
    } catch (error) {
      console.error('Error updating advanced privacy preferences:', error);
      return false;
    }
  }

  /**
   * Check if user has given consent for specific data processing
   */
  async hasValidConsent(consentType: string): Promise<boolean> {
    try {
      const consents = await this.getPrivacyConsents();
      const relevantConsent = consents.find(c => 
        c.consent_type === consentType && 
        c.consent_status === true &&
        (!c.expires_at || new Date(c.expires_at) > new Date()) &&
        !c.withdrawn_at
      );

      return !!relevantConsent;
    } catch (error) {
      console.error('Error checking consent:', error);
      return false;
    }
  }

  /**
   * Log data access for audit trail
   */
  async logDataAccess(event: DataAccessEvent): Promise<void> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return;

      const context = await this.getAccessContext();

      await supabase
        .from('data_access_log')
        .insert({
          user_id: user.id,
          access_type: event.access_type,
          data_category: event.data_category,
          data_fields: event.data_fields || [],
          accessor_type: event.accessor_type,
          accessor_id: user.id,
          access_reason: event.access_reason,
          ip_address: context.ipAddress,
          user_agent: context.userAgent,
          access_granted: true,
        });
    } catch (error) {
      console.error('Error logging data access:', error);
    }
  }

  /**
   * Perform privacy risk assessment
   */
  async performRiskAssessment(
    assessmentType: string,
    dataCategories: string[],
    processingPurpose: string
  ): Promise<PrivacyRiskAssessment> {
    try {
      // Calculate risk score based on various factors
      let riskScore = 0;
      let riskLevel: 'low' | 'medium' | 'high' | 'critical' = 'low';
      const mitigationMeasures: string[] = [];

      // Assess data sensitivity
      const sensitiveCategories = ['biometric_data', 'health_data', 'location_data', 'financial_data'];
      const hasSensitiveData = dataCategories.some(cat => sensitiveCategories.includes(cat));
      
      if (hasSensitiveData) {
        riskScore += 0.3;
        mitigationMeasures.push('Enhanced encryption for sensitive data');
      }

      // Assess data volume
      const preferences = await this.getAdvancedPrivacyPreferences();
      if (preferences?.allow_profiling) {
        riskScore += 0.2;
        mitigationMeasures.push('User consent for profiling activities');
      }

      // Assess third-party sharing
      if (preferences?.allow_third_party_sharing) {
        riskScore += 0.25;
        mitigationMeasures.push('Data processing agreements with third parties');
      }

      // Assess cross-border transfers
      if (processingPurpose.includes('transfer') || processingPurpose.includes('sharing')) {
        riskScore += 0.15;
        mitigationMeasures.push('Adequacy decisions or appropriate safeguards');
      }

      // Determine risk level
      if (riskScore >= 0.7) riskLevel = 'critical';
      else if (riskScore >= 0.5) riskLevel = 'high';
      else if (riskScore >= 0.3) riskLevel = 'medium';
      else riskLevel = 'low';

      const assessment: PrivacyRiskAssessment = {
        assessment_type: assessmentType,
        risk_level: riskLevel,
        risk_score: Math.min(riskScore, 1),
        data_sensitivity_level: hasSensitiveData ? 'high' : 'medium',
        mitigation_measures: mitigationMeasures,
      };

      // Store assessment in database
      const { data: { user } } = await supabase.auth.getUser();
      if (user) {
        await supabase
          .from('privacy_risk_assessments')
          .insert({
            user_id: user.id,
            ...assessment,
            data_volume_category: 'medium',
            processing_complexity: 'medium',
          });
      }

      return assessment;
    } catch (error) {
      console.error('Error performing risk assessment:', error);
      return {
        assessment_type: assessmentType,
        risk_level: 'medium',
        risk_score: 0.5,
        data_sensitivity_level: 'medium',
        mitigation_measures: ['Standard security measures'],
      };
    }
  }

  /**
   * Apply data minimization based on user preferences
   */
  async applyDataMinimization(): Promise<void> {
    try {
      const preferences = await this.getAdvancedPrivacyPreferences();
      if (!preferences?.data_minimization_enabled) return;

      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return;

      // Apply location precision reduction
      if (preferences.location_precision_level !== 'exact') {
        await this.reduceLocationPrecision(preferences.location_precision_level);
      }

      // Clean up old data based on retention periods
      if (preferences.auto_delete_enabled) {
        await this.cleanupOldData(preferences.auto_delete_period);
      }

      // Remove unnecessary data fields
      await this.removeUnnecessaryData();

    } catch (error) {
      console.error('Error applying data minimization:', error);
    }
  }

  /**
   * Get user's data access history
   */
  async getDataAccessHistory(limit: number = 100): Promise<any[]> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      const { data, error } = await supabase
        .from('data_access_log')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false })
        .limit(limit);

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error getting data access history:', error);
      return [];
    }
  }

  /**
   * Request data processing activity log
   */
  async getDataProcessingActivities(): Promise<any[]> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      const { data, error } = await supabase
        .from('data_processing_activities')
        .select('*')
        .eq('user_id', user.id)
        .order('processing_start', { ascending: false });

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error getting processing activities:', error);
      return [];
    }
  }

  // Private helper methods

  private async loadPrivacyPreferences(): Promise<void> {
    await this.getAdvancedPrivacyPreferences();
  }

  private async loadPrivacyConsents(): Promise<void> {
    await this.getPrivacyConsents();
  }

  private async setupDataMinimization(): Promise<void> {
    const preferences = await this.getAdvancedPrivacyPreferences();
    if (preferences?.data_minimization_enabled) {
      await this.applyDataMinimization();
    }
  }

  private async getConsentContext(): Promise<any> {
    try {
      const deviceInfo = await Device.getDeviceTypeAsync();
      const deviceName = await Device.deviceName;
      
      return {
        ipAddress: null, // Would be populated by server
        userAgent: `${deviceInfo}/${deviceName}`,
        deviceFingerprint: await this.generateDeviceFingerprint(),
        geolocation: await this.getCurrentLocation(),
      };
    } catch (error) {
      return {
        ipAddress: null,
        userAgent: 'Unknown',
        deviceFingerprint: null,
        geolocation: null,
      };
    }
  }

  private async getAccessContext(): Promise<any> {
    return await this.getConsentContext();
  }

  private async generateDeviceFingerprint(): Promise<string> {
    try {
      const deviceInfo = {
        deviceType: await Device.getDeviceTypeAsync(),
        deviceName: await Device.deviceName,
        osName: Device.osName,
        osVersion: Device.osVersion,
      };
      
      return btoa(JSON.stringify(deviceInfo));
    } catch (error) {
      return 'unknown';
    }
  }

  private async getCurrentLocation(): Promise<any> {
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status !== 'granted') return null;

      const location = await Location.getCurrentPositionAsync({});
      return {
        latitude: location.coords.latitude,
        longitude: location.coords.longitude,
        accuracy: location.coords.accuracy,
      };
    } catch (error) {
      return null;
    }
  }

  private async reduceLocationPrecision(level: string): Promise<void> {
    // Implementation would reduce location precision based on user preference
    console.log(`Reducing location precision to ${level} level`);
  }

  private async cleanupOldData(retentionPeriod: string): Promise<void> {
    // Implementation would clean up old data based on retention period
    console.log(`Cleaning up data older than ${retentionPeriod}`);
  }

  private async removeUnnecessaryData(): Promise<void> {
    // Implementation would remove data fields not necessary for core functionality
    console.log('Removing unnecessary data fields');
  }

  private getDefaultPrivacyPreferences(): AdvancedPrivacyPreferences {
    return {
      data_minimization_enabled: true,
      auto_delete_enabled: false,
      auto_delete_period: '2 years',
      allow_third_party_sharing: false,
      allowed_third_parties: [],
      data_sharing_purposes: [],
      allow_behavioral_analytics: false,
      allow_cross_device_tracking: false,
      allow_fingerprinting: false,
      allow_profiling: false,
      allow_biometric_processing: false,
      biometric_retention_period: '30 days',
      allow_health_data_inference: false,
      location_precision_level: 'city',
      location_history_retention: '90 days',
      allow_location_inference: false,
      preferred_contact_method: 'email',
      communication_frequency: 'normal',
      auto_export_enabled: false,
      export_frequency: '1 month',
      export_format: 'json',
      require_2fa_for_sensitive: true,
      session_timeout: '24 hours',
      require_reauth_for_deletion: true,
    };
  }
}
