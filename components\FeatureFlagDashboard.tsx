import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Switch,
  Alert,
  RefreshControl,
  Modal,
  TextInput,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import {
  Flag,
  Settings,
  BarChart3,
  Users,
  TestTube,
  Eye,
  EyeOff,
  Plus,
  Edit,
  Trash2,
  TrendingUp,
  TrendingDown,
  Activity,
  Clock,
  CheckCircle,
  XCircle,
} from 'lucide-react-native';
import { supabase } from '../lib/supabase';
import { FeatureFlagService, FeatureFlag } from '../services/FeatureFlagService';
import { ABTestingService, ABExperiment, ExperimentResults } from '../services/ABTestingService';

interface FeatureFlagDashboardProps {
  onFlagToggle?: (flagName: string, enabled: boolean) => void;
}

interface ABTestResult {
  variant_name: string;
  unique_users: number;
  conversions: number;
  conversion_rate: number;
  confidence_level: number;
  is_statistically_significant: boolean;
}

export const FeatureFlagDashboard: React.FC<FeatureFlagDashboardProps> = ({
  onFlagToggle,
}) => {
  const [flags, setFlags] = useState<FeatureFlag[]>([]);
  const [variants, setVariants] = useState<{ [key: string]: FeatureFlagVariant[] }>({});
  const [abTestResults, setABTestResults] = useState<{ [key: string]: ABTestResult[] }>({});
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedFlag, setSelectedFlag] = useState<FeatureFlag | null>(null);
  const [modalVisible, setModalVisible] = useState(false);
  const [activeTab, setActiveTab] = useState<'flags' | 'analytics' | 'tests'>('flags');

  useEffect(() => {
    loadFeatureFlags();
  }, []);

  const loadFeatureFlags = async () => {
    try {
      setLoading(true);
      
      // Load feature flags
      const { data: flagsData, error: flagsError } = await supabase
        .from('feature_flags')
        .select('*')
        .order('created_at', { ascending: false });

      if (flagsError) throw flagsError;

      setFlags(flagsData || []);

      // Load variants for A/B tests
      const abTestFlags = flagsData?.filter(f => f.is_ab_test) || [];
      const variantsData: { [key: string]: FeatureFlagVariant[] } = {};
      const resultsData: { [key: string]: ABTestResult[] } = {};

      for (const flag of abTestFlags) {
        // Load variants
        const { data: flagVariants } = await supabase
          .from('feature_flag_variants')
          .select('*')
          .eq('flag_id', flag.id)
          .order('weight', { ascending: false });

        if (flagVariants) {
          variantsData[flag.id] = flagVariants;
        }

        // Load A/B test results
        const { data: results } = await supabase
          .from('ab_test_results')
          .select(`
            variant_id,
            unique_users,
            conversions,
            conversion_rate,
            confidence_level,
            is_statistically_significant,
            feature_flag_variants!inner(name)
          `)
          .eq('flag_id', flag.id)
          .gte('date', new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]);

        if (results) {
          resultsData[flag.id] = results.map(r => ({
            variant_name: (r as any).feature_flag_variants.name,
            unique_users: r.unique_users,
            conversions: r.conversions,
            conversion_rate: r.conversion_rate,
            confidence_level: r.confidence_level,
            is_statistically_significant: r.is_statistically_significant,
          }));
        }
      }

      setVariants(variantsData);
      setABTestResults(resultsData);

    } catch (error) {
      console.error('Error loading feature flags:', error);
      Alert.alert('Error', 'Failed to load feature flags');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const onRefresh = () => {
    setRefreshing(true);
    loadFeatureFlags();
  };

  const toggleFlag = async (flag: FeatureFlag) => {
    try {
      const newEnabled = !flag.is_enabled;
      
      const { error } = await supabase
        .from('feature_flags')
        .update({ 
          is_enabled: newEnabled,
          updated_at: new Date().toISOString()
        })
        .eq('id', flag.id);

      if (error) throw error;

      // Update local state
      setFlags(prev => prev.map(f => 
        f.id === flag.id ? { ...f, is_enabled: newEnabled } : f
      ));

      // Log audit entry
      await supabase
        .from('feature_flag_audit_log')
        .insert({
          flag_id: flag.id,
          action: newEnabled ? 'enabled' : 'disabled',
          new_values: { is_enabled: newEnabled },
          change_reason: 'Admin dashboard toggle'
        });

      if (onFlagToggle) {
        onFlagToggle(flag.name, newEnabled);
      }

    } catch (error) {
      console.error('Error toggling flag:', error);
      Alert.alert('Error', 'Failed to toggle feature flag');
    }
  };

  const updateRolloutPercentage = async (flag: FeatureFlag, percentage: number) => {
    try {
      const { error } = await supabase
        .from('feature_flags')
        .update({ 
          rollout_percentage: percentage,
          updated_at: new Date().toISOString()
        })
        .eq('id', flag.id);

      if (error) throw error;

      // Update local state
      setFlags(prev => prev.map(f => 
        f.id === flag.id ? { ...f, rollout_percentage: percentage } : f
      ));

      // Log audit entry
      await supabase
        .from('feature_flag_audit_log')
        .insert({
          flag_id: flag.id,
          action: 'updated',
          old_values: { rollout_percentage: flag.rollout_percentage },
          new_values: { rollout_percentage: percentage },
          change_reason: 'Rollout percentage updated'
        });

    } catch (error) {
      console.error('Error updating rollout:', error);
      Alert.alert('Error', 'Failed to update rollout percentage');
    }
  };

  const getFlagStatusColor = (flag: FeatureFlag) => {
    if (!flag.is_enabled) return '#6B7280';
    if (flag.rollout_percentage === 100) return '#10B981';
    if (flag.rollout_percentage > 0) return '#F59E0B';
    return '#EF4444';
  };

  const getFlagStatusText = (flag: FeatureFlag) => {
    if (!flag.is_enabled) return 'Disabled';
    if (flag.rollout_percentage === 100) return 'Fully Rolled Out';
    if (flag.rollout_percentage > 0) return `${flag.rollout_percentage}% Rollout`;
    return 'Enabled (0%)';
  };

  const renderFlagCard = (flag: FeatureFlag) => {
    const statusColor = getFlagStatusColor(flag);
    const statusText = getFlagStatusText(flag);

    return (
      <View key={flag.id} style={styles.flagCard}>
        <View style={styles.flagHeader}>
          <View style={styles.flagInfo}>
            <Text style={styles.flagName}>{flag.name}</Text>
            <Text style={styles.flagDescription}>{flag.description}</Text>
            <View style={styles.flagMeta}>
              <Text style={styles.flagCategory}>{flag.category}</Text>
              <Text style={[styles.flagStatus, { color: statusColor }]}>
                {statusText}
              </Text>
            </View>
          </View>
          
          <View style={styles.flagControls}>
            <Switch
              value={flag.is_enabled}
              onValueChange={() => toggleFlag(flag)}
              trackColor={{ false: '#E5E7EB', true: '#3B82F6' }}
              thumbColor={flag.is_enabled ? '#FFFFFF' : '#F3F4F6'}
            />
          </View>
        </View>

        {flag.is_enabled && (
          <View style={styles.flagDetails}>
            <View style={styles.rolloutContainer}>
              <Text style={styles.rolloutLabel}>
                Rollout: {flag.rollout_percentage}%
              </Text>
              <View style={styles.rolloutBar}>
                <View 
                  style={[
                    styles.rolloutFill, 
                    { 
                      width: `${flag.rollout_percentage}%`,
                      backgroundColor: statusColor
                    }
                  ]} 
                />
              </View>
            </View>

            {flag.is_ab_test && variants[flag.id] && (
              <View style={styles.abTestInfo}>
                <Text style={styles.abTestLabel}>A/B Test Variants:</Text>
                {variants[flag.id].map(variant => (
                  <View key={variant.id} style={styles.variantRow}>
                    <Text style={styles.variantName}>
                      {variant.name} {variant.is_control && '(Control)'}
                    </Text>
                    <Text style={styles.variantWeight}>{variant.weight}%</Text>
                  </View>
                ))}
              </View>
            )}
          </View>
        )}

        <View style={styles.flagActions}>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => {
              setSelectedFlag(flag);
              setModalVisible(true);
            }}
          >
            <Eye size={16} color="#3B82F6" />
            <Text style={styles.actionButtonText}>Details</Text>
          </TouchableOpacity>

          {flag.is_ab_test && abTestResults[flag.id] && (
            <TouchableOpacity style={styles.actionButton}>
              <BarChart3 size={16} color="#10B981" />
              <Text style={styles.actionButtonText}>Results</Text>
            </TouchableOpacity>
          )}
        </View>
      </View>
    );
  };

  const renderAnalytics = () => (
    <View style={styles.analyticsContainer}>
      <Text style={styles.sectionTitle}>Feature Flag Analytics</Text>
      
      <View style={styles.metricsGrid}>
        <View style={styles.metricCard}>
          <Flag size={24} color="#3B82F6" />
          <Text style={styles.metricValue}>{flags.length}</Text>
          <Text style={styles.metricLabel}>Total Flags</Text>
        </View>
        
        <View style={styles.metricCard}>
          <CheckCircle size={24} color="#10B981" />
          <Text style={styles.metricValue}>
            {flags.filter(f => f.is_enabled).length}
          </Text>
          <Text style={styles.metricLabel}>Active Flags</Text>
        </View>
        
        <View style={styles.metricCard}>
          <TestTube size={24} color="#F59E0B" />
          <Text style={styles.metricValue}>
            {flags.filter(f => f.is_ab_test).length}
          </Text>
          <Text style={styles.metricLabel}>A/B Tests</Text>
        </View>
        
        <View style={styles.metricCard}>
          <TrendingUp size={24} color="#8B5CF6" />
          <Text style={styles.metricValue}>
            {flags.filter(f => f.rollout_percentage === 100).length}
          </Text>
          <Text style={styles.metricLabel}>Full Rollout</Text>
        </View>
      </View>
    </View>
  );

  const renderABTests = () => (
    <View style={styles.testsContainer}>
      <Text style={styles.sectionTitle}>A/B Test Results</Text>
      
      {flags.filter(f => f.is_ab_test).map(flag => (
        <View key={flag.id} style={styles.testCard}>
          <Text style={styles.testName}>{flag.name}</Text>
          
          {abTestResults[flag.id] && (
            <View style={styles.testResults}>
              {abTestResults[flag.id].map(result => (
                <View key={result.variant_name} style={styles.resultRow}>
                  <View style={styles.resultInfo}>
                    <Text style={styles.resultVariant}>{result.variant_name}</Text>
                    <Text style={styles.resultUsers}>
                      {result.unique_users} users
                    </Text>
                  </View>
                  
                  <View style={styles.resultMetrics}>
                    <Text style={styles.resultConversion}>
                      {result.conversion_rate.toFixed(2)}%
                    </Text>
                    {result.is_statistically_significant && (
                      <CheckCircle size={16} color="#10B981" />
                    )}
                  </View>
                </View>
              ))}
            </View>
          )}
        </View>
      ))}
    </View>
  );

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <Activity size={32} color="#3B82F6" />
        <Text style={styles.loadingText}>Loading feature flags...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Header */}
      <LinearGradient
        colors={['#3B82F6', '#1D4ED8']}
        style={styles.header}
      >
        <Flag size={32} color="white" />
        <Text style={styles.headerTitle}>Feature Flags</Text>
        <Text style={styles.headerSubtitle}>
          Manage feature rollouts and A/B tests
        </Text>
      </LinearGradient>

      {/* Tab Navigation */}
      <View style={styles.tabContainer}>
        {[
          { key: 'flags', label: 'Flags', icon: Flag },
          { key: 'analytics', label: 'Analytics', icon: BarChart3 },
          { key: 'tests', label: 'A/B Tests', icon: TestTube },
        ].map(({ key, label, icon: Icon }) => (
          <TouchableOpacity
            key={key}
            style={[styles.tab, activeTab === key && styles.activeTab]}
            onPress={() => setActiveTab(key as any)}
          >
            <Icon size={20} color={activeTab === key ? '#3B82F6' : '#6B7280'} />
            <Text style={[styles.tabText, activeTab === key && styles.activeTabText]}>
              {label}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      <ScrollView
        style={styles.content}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {activeTab === 'flags' && (
          <View style={styles.flagsContainer}>
            {flags.map(renderFlagCard)}
          </View>
        )}

        {activeTab === 'analytics' && renderAnalytics()}
        {activeTab === 'tests' && renderABTests()}
      </ScrollView>

      {/* Flag Details Modal */}
      <Modal
        visible={modalVisible}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setModalVisible(false)}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>
              {selectedFlag?.name || 'Flag Details'}
            </Text>
            <TouchableOpacity onPress={() => setModalVisible(false)}>
              <XCircle size={24} color="#6B7280" />
            </TouchableOpacity>
          </View>

          {selectedFlag && (
            <ScrollView style={styles.modalContent}>
              <View style={styles.detailSection}>
                <Text style={styles.detailLabel}>Description</Text>
                <Text style={styles.detailValue}>
                  {selectedFlag.description || 'No description'}
                </Text>
              </View>

              <View style={styles.detailSection}>
                <Text style={styles.detailLabel}>Category</Text>
                <Text style={styles.detailValue}>{selectedFlag.category}</Text>
              </View>

              <View style={styles.detailSection}>
                <Text style={styles.detailLabel}>Environment</Text>
                <Text style={styles.detailValue}>{selectedFlag.environment}</Text>
              </View>

              <View style={styles.detailSection}>
                <Text style={styles.detailLabel}>Default Value</Text>
                <Text style={styles.detailValue}>
                  {JSON.stringify(selectedFlag.default_value)}
                </Text>
              </View>

              <View style={styles.detailSection}>
                <Text style={styles.detailLabel}>Rollout Percentage</Text>
                <Text style={styles.detailValue}>
                  {selectedFlag.rollout_percentage}%
                </Text>
              </View>

              {selectedFlag.tags && selectedFlag.tags.length > 0 && (
                <View style={styles.detailSection}>
                  <Text style={styles.detailLabel}>Tags</Text>
                  <View style={styles.tagsContainer}>
                    {selectedFlag.tags.map((tag, index) => (
                      <View key={index} style={styles.tag}>
                        <Text style={styles.tagText}>{tag}</Text>
                      </View>
                    ))}
                  </View>
                </View>
              )}
            </ScrollView>
          )}
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#F9FAFB',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#6B7280',
  },
  header: {
    padding: 24,
    paddingTop: 60,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: 'white',
    marginTop: 8,
  },
  headerSubtitle: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.8)',
    marginTop: 4,
    textAlign: 'center',
  },
  tabContainer: {
    flexDirection: 'row',
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  tab: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    gap: 8,
  },
  activeTab: {
    borderBottomWidth: 2,
    borderBottomColor: '#3B82F6',
  },
  tabText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#6B7280',
  },
  activeTabText: {
    color: '#3B82F6',
  },
  content: {
    flex: 1,
  },
  flagsContainer: {
    padding: 20,
  },
  flagCard: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  flagHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  flagInfo: {
    flex: 1,
    marginRight: 16,
  },
  flagName: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 4,
  },
  flagDescription: {
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 8,
  },
  flagMeta: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  flagCategory: {
    fontSize: 12,
    color: '#9CA3AF',
    backgroundColor: '#F3F4F6',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  flagStatus: {
    fontSize: 12,
    fontWeight: '600',
  },
  flagControls: {
    alignItems: 'center',
  },
  flagDetails: {
    borderTopWidth: 1,
    borderTopColor: '#F3F4F6',
    paddingTop: 12,
    marginBottom: 12,
  },
  rolloutContainer: {
    marginBottom: 12,
  },
  rolloutLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#374151',
    marginBottom: 8,
  },
  rolloutBar: {
    height: 8,
    backgroundColor: '#E5E7EB',
    borderRadius: 4,
    overflow: 'hidden',
  },
  rolloutFill: {
    height: '100%',
    borderRadius: 4,
  },
  abTestInfo: {
    marginTop: 12,
  },
  abTestLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#374151',
    marginBottom: 8,
  },
  variantRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 4,
  },
  variantName: {
    fontSize: 14,
    color: '#6B7280',
  },
  variantWeight: {
    fontSize: 14,
    fontWeight: '500',
    color: '#374151',
  },
  flagActions: {
    flexDirection: 'row',
    gap: 12,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F3F4F6',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 6,
    gap: 6,
  },
  actionButtonText: {
    fontSize: 12,
    fontWeight: '500',
    color: '#374151',
  },
  analyticsContainer: {
    padding: 20,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 20,
  },
  metricsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 16,
  },
  metricCard: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 20,
    alignItems: 'center',
    width: '47%',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  metricValue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#111827',
    marginTop: 8,
  },
  metricLabel: {
    fontSize: 14,
    color: '#6B7280',
    marginTop: 4,
    textAlign: 'center',
  },
  testsContainer: {
    padding: 20,
  },
  testCard: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  testName: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 12,
  },
  testResults: {
    gap: 8,
  },
  resultRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
  },
  resultInfo: {
    flex: 1,
  },
  resultVariant: {
    fontSize: 16,
    fontWeight: '500',
    color: '#111827',
  },
  resultUsers: {
    fontSize: 14,
    color: '#6B7280',
  },
  resultMetrics: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  resultConversion: {
    fontSize: 16,
    fontWeight: '600',
    color: '#10B981',
  },
  modalContainer: {
    flex: 1,
    backgroundColor: 'white',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827',
  },
  modalContent: {
    flex: 1,
    padding: 20,
  },
  detailSection: {
    marginBottom: 20,
  },
  detailLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#374151',
    marginBottom: 8,
  },
  detailValue: {
    fontSize: 16,
    color: '#111827',
    backgroundColor: '#F9FAFB',
    padding: 12,
    borderRadius: 8,
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  tag: {
    backgroundColor: '#EBF8FF',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  tagText: {
    fontSize: 12,
    color: '#1E40AF',
    fontWeight: '500',
  },
});

export default FeatureFlagDashboard;
