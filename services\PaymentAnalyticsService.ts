import { supabase } from '../lib/supabase';

export interface RevenueMetrics {
  mrr: number; // Monthly Recurring Revenue
  arr: number; // Annual Recurring Revenue
  totalRevenue: number;
  revenueGrowth: number;
  period: string;
}

export interface SubscriptionMetrics {
  totalSubscriptions: number;
  activeSubscriptions: number;
  trialSubscriptions: number;
  canceledSubscriptions: number;
  churnRate: number;
  conversionRate: number;
  averageRevenuePerUser: number;
}

export interface PlanMetrics {
  planId: string;
  planName: string;
  subscribers: number;
  revenue: number;
  conversionRate: number;
  churnRate: number;
  averageLifetimeValue: number;
}

export interface PaymentMetrics {
  totalTransactions: number;
  successfulTransactions: number;
  failedTransactions: number;
  successRate: number;
  averageTransactionValue: number;
  totalVolume: number;
}

export interface UserAnalytics {
  userId: string;
  totalSpent: number;
  subscriptionHistory: any[];
  paymentHistory: any[];
  lifetimeValue: number;
  riskScore: number;
  lastActivity: string;
}

export interface DashboardMetrics {
  revenue: RevenueMetrics;
  subscriptions: SubscriptionMetrics;
  payments: PaymentMetrics;
  topPlans: PlanMetrics[];
  recentActivity: any[];
}

export class PaymentAnalyticsService {
  
  /**
   * Get comprehensive dashboard metrics
   */
  static async getDashboardMetrics(period: 'day' | 'week' | 'month' | 'year' = 'month'): Promise<DashboardMetrics> {
    try {
      const [revenue, subscriptions, payments, topPlans, recentActivity] = await Promise.all([
        this.getRevenueMetrics(period),
        this.getSubscriptionMetrics(period),
        this.getPaymentMetrics(period),
        this.getTopPerformingPlans(5),
        this.getRecentActivity(10)
      ]);

      return {
        revenue,
        subscriptions,
        payments,
        topPlans,
        recentActivity
      };
    } catch (error) {
      console.error('Error fetching dashboard metrics:', error);
      throw error;
    }
  }

  /**
   * Get revenue metrics for specified period
   */
  static async getRevenueMetrics(period: 'day' | 'week' | 'month' | 'year' = 'month'): Promise<RevenueMetrics> {
    try {
      const { data, error } = await supabase.rpc('get_revenue_metrics', {
        period_type: period
      });

      if (error) {
        throw error;
      }

      return data || {
        mrr: 0,
        arr: 0,
        totalRevenue: 0,
        revenueGrowth: 0,
        period
      };
    } catch (error) {
      console.error('Error fetching revenue metrics:', error);
      return {
        mrr: 0,
        arr: 0,
        totalRevenue: 0,
        revenueGrowth: 0,
        period
      };
    }
  }

  /**
   * Get subscription metrics
   */
  static async getSubscriptionMetrics(period: 'day' | 'week' | 'month' | 'year' = 'month'): Promise<SubscriptionMetrics> {
    try {
      const { data, error } = await supabase.rpc('get_subscription_metrics', {
        period_type: period
      });

      if (error) {
        throw error;
      }

      return data || {
        totalSubscriptions: 0,
        activeSubscriptions: 0,
        trialSubscriptions: 0,
        canceledSubscriptions: 0,
        churnRate: 0,
        conversionRate: 0,
        averageRevenuePerUser: 0
      };
    } catch (error) {
      console.error('Error fetching subscription metrics:', error);
      return {
        totalSubscriptions: 0,
        activeSubscriptions: 0,
        trialSubscriptions: 0,
        canceledSubscriptions: 0,
        churnRate: 0,
        conversionRate: 0,
        averageRevenuePerUser: 0
      };
    }
  }

  /**
   * Get payment transaction metrics
   */
  static async getPaymentMetrics(period: 'day' | 'week' | 'month' | 'year' = 'month'): Promise<PaymentMetrics> {
    try {
      const { data, error } = await supabase.rpc('get_payment_metrics', {
        period_type: period
      });

      if (error) {
        throw error;
      }

      return data || {
        totalTransactions: 0,
        successfulTransactions: 0,
        failedTransactions: 0,
        successRate: 0,
        averageTransactionValue: 0,
        totalVolume: 0
      };
    } catch (error) {
      console.error('Error fetching payment metrics:', error);
      return {
        totalTransactions: 0,
        successfulTransactions: 0,
        failedTransactions: 0,
        successRate: 0,
        averageTransactionValue: 0,
        totalVolume: 0
      };
    }
  }

  /**
   * Get top performing subscription plans
   */
  static async getTopPerformingPlans(limit: number = 5): Promise<PlanMetrics[]> {
    try {
      const { data, error } = await supabase.rpc('get_top_performing_plans', {
        plan_limit: limit
      });

      if (error) {
        throw error;
      }

      return data || [];
    } catch (error) {
      console.error('Error fetching top performing plans:', error);
      return [];
    }
  }

  /**
   * Get recent payment and subscription activity
   */
  static async getRecentActivity(limit: number = 10): Promise<any[]> {
    try {
      const { data, error } = await supabase.rpc('get_recent_payment_activity', {
        activity_limit: limit
      });

      if (error) {
        throw error;
      }

      return data || [];
    } catch (error) {
      console.error('Error fetching recent activity:', error);
      return [];
    }
  }

  /**
   * Get user-specific analytics
   */
  static async getUserAnalytics(userId: string): Promise<UserAnalytics | null> {
    try {
      const { data, error } = await supabase.rpc('get_user_analytics', {
        user_uuid: userId
      });

      if (error) {
        throw error;
      }

      return data;
    } catch (error) {
      console.error('Error fetching user analytics:', error);
      return null;
    }
  }

  /**
   * Get revenue trends over time
   */
  static async getRevenueTrends(
    period: 'day' | 'week' | 'month' = 'month',
    duration: number = 12
  ): Promise<Array<{ period: string; revenue: number; subscriptions: number }>> {
    try {
      const { data, error } = await supabase.rpc('get_revenue_trends', {
        period_type: period,
        duration_count: duration
      });

      if (error) {
        throw error;
      }

      return data || [];
    } catch (error) {
      console.error('Error fetching revenue trends:', error);
      return [];
    }
  }

  /**
   * Get churn analysis
   */
  static async getChurnAnalysis(): Promise<{
    churnRate: number;
    churnReasons: Array<{ reason: string; count: number; percentage: number }>;
    churnTrends: Array<{ period: string; churnRate: number }>;
  }> {
    try {
      const { data, error } = await supabase.rpc('get_churn_analysis');

      if (error) {
        throw error;
      }

      return data || {
        churnRate: 0,
        churnReasons: [],
        churnTrends: []
      };
    } catch (error) {
      console.error('Error fetching churn analysis:', error);
      return {
        churnRate: 0,
        churnReasons: [],
        churnTrends: []
      };
    }
  }

  /**
   * Get fraud detection statistics
   */
  static async getFraudStats(): Promise<{
    totalFraudAttempts: number;
    blockedTransactions: number;
    riskDistribution: Array<{ riskLevel: string; count: number }>;
    topRiskFactors: Array<{ factor: string; count: number }>;
  }> {
    try {
      const { data, error } = await supabase.rpc('get_fraud_statistics');

      if (error) {
        throw error;
      }

      return data || {
        totalFraudAttempts: 0,
        blockedTransactions: 0,
        riskDistribution: [],
        topRiskFactors: []
      };
    } catch (error) {
      console.error('Error fetching fraud statistics:', error);
      return {
        totalFraudAttempts: 0,
        blockedTransactions: 0,
        riskDistribution: [],
        topRiskFactors: []
      };
    }
  }

  /**
   * Export analytics data to CSV
   */
  static async exportAnalytics(
    type: 'revenue' | 'subscriptions' | 'payments' | 'users',
    startDate: string,
    endDate: string
  ): Promise<string> {
    try {
      const { data, error } = await supabase.rpc('export_analytics_data', {
        export_type: type,
        start_date: startDate,
        end_date: endDate
      });

      if (error) {
        throw error;
      }

      return data || '';
    } catch (error) {
      console.error('Error exporting analytics data:', error);
      throw error;
    }
  }

  /**
   * Get real-time metrics (for live dashboard)
   */
  static async getRealTimeMetrics(): Promise<{
    activeUsers: number;
    ongoingTransactions: number;
    recentSignups: number;
    currentMRR: number;
  }> {
    try {
      const { data, error } = await supabase.rpc('get_realtime_metrics');

      if (error) {
        throw error;
      }

      return data || {
        activeUsers: 0,
        ongoingTransactions: 0,
        recentSignups: 0,
        currentMRR: 0
      };
    } catch (error) {
      console.error('Error fetching real-time metrics:', error);
      return {
        activeUsers: 0,
        ongoingTransactions: 0,
        recentSignups: 0,
        currentMRR: 0
      };
    }
  }
}
