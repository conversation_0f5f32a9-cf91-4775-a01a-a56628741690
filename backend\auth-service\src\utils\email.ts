import nodemailer from 'nodemailer';
import { logger } from './logger';
import { generateVerificationToken, generateResetToken } from './password';
import { prisma } from '../index';

// Email configuration
const emailConfig = {
  host: process.env.SMTP_HOST || 'smtp.gmail.com',
  port: parseInt(process.env.SMTP_PORT || '587'),
  secure: process.env.SMTP_SECURE === 'true',
  auth: {
    user: process.env.SMTP_USER,
    pass: process.env.SMTP_PASS
  }
};

// Create transporter
const transporter = nodemailer.createTransporter(emailConfig);

// Verify transporter configuration
transporter.verify((error, success) => {
  if (error) {
    logger.error('Email transporter configuration error:', error);
  } else {
    logger.info('Email transporter is ready');
  }
});

// Email templates
const emailTemplates = {
  verification: {
    subject: 'Verify Your BioScan Account',
    html: (verificationUrl: string, firstName: string) => `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Verify Your Account</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: linear-gradient(135deg, #22C55E, #16A34A); color: white; padding: 30px; text-align: center; border-radius: 8px 8px 0 0; }
          .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 8px 8px; }
          .button { display: inline-block; background: #22C55E; color: white; padding: 12px 30px; text-decoration: none; border-radius: 6px; margin: 20px 0; }
          .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>🌿 BioScan</h1>
            <p>Welcome to the nature identification community!</p>
          </div>
          <div class="content">
            <h2>Hi ${firstName}!</h2>
            <p>Thank you for joining BioScan. To complete your registration and start discovering nature around you, please verify your email address.</p>
            <p>Click the button below to verify your account:</p>
            <a href="${verificationUrl}" class="button">Verify My Account</a>
            <p>Or copy and paste this link into your browser:</p>
            <p style="word-break: break-all; color: #666;">${verificationUrl}</p>
            <p><strong>This link will expire in 24 hours.</strong></p>
            <p>If you didn't create a BioScan account, you can safely ignore this email.</p>
          </div>
          <div class="footer">
            <p>© 2024 BioScan. All rights reserved.</p>
            <p>This is an automated message, please do not reply.</p>
          </div>
        </div>
      </body>
      </html>
    `
  },
  passwordReset: {
    subject: 'Reset Your BioScan Password',
    html: (resetUrl: string, firstName: string) => `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Reset Your Password</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: linear-gradient(135deg, #EF4444, #DC2626); color: white; padding: 30px; text-align: center; border-radius: 8px 8px 0 0; }
          .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 8px 8px; }
          .button { display: inline-block; background: #EF4444; color: white; padding: 12px 30px; text-decoration: none; border-radius: 6px; margin: 20px 0; }
          .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
          .warning { background: #FEF3C7; border: 1px solid #F59E0B; padding: 15px; border-radius: 6px; margin: 20px 0; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>🔒 Password Reset</h1>
            <p>BioScan Account Security</p>
          </div>
          <div class="content">
            <h2>Hi ${firstName}!</h2>
            <p>We received a request to reset your BioScan account password. If you made this request, click the button below to set a new password:</p>
            <a href="${resetUrl}" class="button">Reset My Password</a>
            <p>Or copy and paste this link into your browser:</p>
            <p style="word-break: break-all; color: #666;">${resetUrl}</p>
            <div class="warning">
              <strong>⚠️ Security Notice:</strong>
              <ul>
                <li>This link will expire in 1 hour</li>
                <li>You can only use this link once</li>
                <li>If you didn't request this reset, please ignore this email</li>
              </ul>
            </div>
            <p>For your security, we recommend:</p>
            <ul>
              <li>Using a strong, unique password</li>
              <li>Enabling two-factor authentication</li>
              <li>Not sharing your account credentials</li>
            </ul>
          </div>
          <div class="footer">
            <p>© 2024 BioScan. All rights reserved.</p>
            <p>This is an automated message, please do not reply.</p>
          </div>
        </div>
      </body>
      </html>
    `
  },
  welcomeComplete: {
    subject: 'Welcome to BioScan! 🌿',
    html: (firstName: string) => `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Welcome to BioScan</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: linear-gradient(135deg, #22C55E, #16A34A); color: white; padding: 30px; text-align: center; border-radius: 8px 8px 0 0; }
          .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 8px 8px; }
          .feature { background: white; padding: 20px; margin: 15px 0; border-radius: 6px; border-left: 4px solid #22C55E; }
          .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>🌿 Welcome to BioScan!</h1>
            <p>Your nature identification journey begins now</p>
          </div>
          <div class="content">
            <h2>Hi ${firstName}!</h2>
            <p>Your email has been verified and your BioScan account is now active! You're ready to start discovering and identifying the amazing nature around you.</p>
            
            <h3>What you can do with BioScan:</h3>
            
            <div class="feature">
              <h4>📸 Instant Species Identification</h4>
              <p>Take a photo of any plant, animal, or insect and get detailed information powered by AI.</p>
            </div>
            
            <div class="feature">
              <h4>📚 Learn & Discover</h4>
              <p>Access comprehensive species information, conservation status, and interesting facts.</p>
            </div>
            
            <div class="feature">
              <h4>🗺️ Track Your Discoveries</h4>
              <p>Keep a personal log of all your nature discoveries with location data.</p>
            </div>
            
            <div class="feature">
              <h4>🧠 Test Your Knowledge</h4>
              <p>Take quizzes based on your discoveries to deepen your understanding.</p>
            </div>
            
            <p>Ready to start exploring? Open the BioScan app and take your first nature photo!</p>
            
            <p>Happy discovering! 🌱</p>
          </div>
          <div class="footer">
            <p>© 2024 BioScan. All rights reserved.</p>
            <p>Need help? Contact <NAME_EMAIL></p>
          </div>
        </div>
      </body>
      </html>
    `
  }
};

/**
 * Send email verification
 */
export async function sendVerificationEmail(email: string, userId: string): Promise<void> {
  try {
    const token = generateVerificationToken();
    const expiresAt = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours

    // Store verification token
    await prisma.user.update({
      where: { id: userId },
      data: {
        emailVerificationToken: token,
        emailVerificationExpires: expiresAt
      }
    });

    // Get user details
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { firstName: true, email: true }
    });

    if (!user) {
      throw new Error('User not found');
    }

    const verificationUrl = `${process.env.FRONTEND_URL}/auth/verify-email?token=${token}`;

    const mailOptions = {
      from: `"BioScan" <${process.env.SMTP_FROM || process.env.SMTP_USER}>`,
      to: email,
      subject: emailTemplates.verification.subject,
      html: emailTemplates.verification.html(verificationUrl, user.firstName || 'there')
    };

    await transporter.sendMail(mailOptions);
    logger.info('Verification email sent', { email, userId });
  } catch (error) {
    logger.error('Failed to send verification email:', error);
    throw new Error('Failed to send verification email');
  }
}

/**
 * Send password reset email
 */
export async function sendPasswordResetEmail(email: string): Promise<void> {
  try {
    const user = await prisma.user.findUnique({
      where: { email },
      select: { id: true, firstName: true }
    });

    if (!user) {
      // Don't reveal if email exists or not
      logger.info('Password reset requested for non-existent email', { email });
      return;
    }

    const { token, hash } = generateResetToken();
    const expiresAt = new Date(Date.now() + 60 * 60 * 1000); // 1 hour

    // Store reset token hash
    await prisma.user.update({
      where: { id: user.id },
      data: {
        passwordResetToken: hash,
        passwordResetExpires: expiresAt
      }
    });

    const resetUrl = `${process.env.FRONTEND_URL}/auth/reset-password?token=${token}`;

    const mailOptions = {
      from: `"BioScan Security" <${process.env.SMTP_FROM || process.env.SMTP_USER}>`,
      to: email,
      subject: emailTemplates.passwordReset.subject,
      html: emailTemplates.passwordReset.html(resetUrl, user.firstName || 'there')
    };

    await transporter.sendMail(mailOptions);
    logger.info('Password reset email sent', { email, userId: user.id });
  } catch (error) {
    logger.error('Failed to send password reset email:', error);
    throw new Error('Failed to send password reset email');
  }
}

/**
 * Send welcome email after email verification
 */
export async function sendWelcomeEmail(email: string, firstName: string): Promise<void> {
  try {
    const mailOptions = {
      from: `"BioScan Team" <${process.env.SMTP_FROM || process.env.SMTP_USER}>`,
      to: email,
      subject: emailTemplates.welcomeComplete.subject,
      html: emailTemplates.welcomeComplete.html(firstName || 'there')
    };

    await transporter.sendMail(mailOptions);
    logger.info('Welcome email sent', { email });
  } catch (error) {
    logger.error('Failed to send welcome email:', error);
    // Don't throw error for welcome email as it's not critical
  }
}
