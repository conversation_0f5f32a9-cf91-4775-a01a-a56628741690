import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  SafeAreaView,
  Dimensions,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import {
  Search,
  MapPin,
  TrendingUp,
  Award,
  Users,
  Star,
  Eye,
  Calendar,
  Leaf,
  Bug,
  Bird,
  Fish,
} from 'lucide-react-native';

const { width } = Dimensions.get('window');

export default function DiscoverScreen() {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('All');

  const categories = [
    { id: 'All', label: 'All', icon: Star },
    { id: 'Plants', label: 'Plants', icon: Leaf },
    { id: 'Insects', label: 'Insects', icon: Bug },
    { id: 'Birds', label: 'Birds', icon: Bird },
    { id: 'Aquatic', label: 'Aquatic', icon: Fish },
  ];

  const featuredDiscoveries = [
    {
      id: 1,
      name: 'Rare Blue Morpho Butterfly',
      discoverer: 'NatureLover2024',
      location: 'Amazon Rainforest',
      views: 2847,
      date: '2 days ago',
      category: 'Insects',
      rarity: 'Extremely Rare',
      confidence: 98,
    },
    {
      id: 2,
      name: 'Ancient Sequoia Sapling',
      discoverer: 'TreeExplorer',
      location: 'California',
      views: 1523,
      date: '1 week ago',
      category: 'Plants',
      rarity: 'Rare',
      confidence: 94,
    },
    {
      id: 3,
      name: 'Painted Bunting',
      discoverer: 'BirdWatcher99',
      location: 'Texas',
      views: 892,
      date: '3 days ago',
      category: 'Birds',
      rarity: 'Uncommon',
      confidence: 96,
    },
  ];

  const trendingSpecies = [
    { name: 'Monarch Butterfly', scans: '+234%', color: '#F59E0B' },
    { name: 'Red Oak', scans: '+189%', color: '#22C55E' },
    { name: 'Cardinal', scans: '+156%', color: '#EF4444' },
    { name: 'Sunflower', scans: '+142%', color: '#F59E0B' },
  ];

  const getRarityColor = (rarity: string) => {
    switch (rarity) {
      case 'Extremely Rare': return '#8B5CF6';
      case 'Rare': return '#3B82F6';
      case 'Uncommon': return '#22C55E';
      default: return '#6B7280';
    }
  };

  const CategoryIcon = ({ category }: { category: string }) => {
    const categoryData = categories.find(c => c.id === category);
    const IconComponent = categoryData?.icon || Star;
    return <IconComponent size={16} color="#6B7280" />;
  };

  return (
    <SafeAreaView style={styles.container}>
      <LinearGradient
        colors={['#F0F9FF', '#E0F2FE', '#BAE6FD']}
        style={styles.gradient}>
        <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
          {/* Header */}
          <View style={styles.header}>
            <Text style={styles.title}>Discover</Text>
            <Text style={styles.subtitle}>Explore nature's wonders</Text>
          </View>

          {/* Search Bar */}
          <View style={styles.searchContainer}>
            <View style={styles.searchBar}>
              <Search size={20} color="#6B7280" />
              <TextInput
                style={styles.searchInput}
                placeholder="Search species, locations..."
                value={searchQuery}
                onChangeText={setSearchQuery}
                placeholderTextColor="#9CA3AF"
              />
            </View>
          </View>

          {/* Categories */}
          <View style={styles.categoriesContainer}>
            <ScrollView horizontal showsHorizontalScrollIndicator={false}>
              <View style={styles.categoriesScrollContent}>
                {categories.map((category) => {
                  const IconComponent = category.icon;
                  return (
                    <TouchableOpacity
                      key={category.id}
                      style={[
                        styles.categoryButton,
                        selectedCategory === category.id && styles.categoryButtonActive
                      ]}
                      onPress={() => setSelectedCategory(category.id)}>
                      <IconComponent
                        size={18}
                        color={selectedCategory === category.id ? '#FFFFFF' : '#6B7280'}
                      />
                      <Text
                        style={[
                          styles.categoryText,
                          selectedCategory === category.id && styles.categoryTextActive
                        ]}>
                        {category.label}
                      </Text>
                    </TouchableOpacity>
                  );
                })}
              </View>
            </ScrollView>
          </View>

          {/* Trending Species */}
          <View style={styles.section}>
            <View style={styles.sectionHeader}>
              <View style={styles.sectionTitleContainer}>
                <TrendingUp size={20} color="#3B82F6" />
                <Text style={styles.sectionTitle}>Trending This Week</Text>
              </View>
            </View>
            
            <View style={styles.trendingGrid}>
              {trendingSpecies.map((species, index) => (
                <View key={index} style={styles.trendingCard}>
                  <View style={styles.trendingHeader}>
                    <Text style={styles.trendingName}>{species.name}</Text>
                    <View style={[styles.trendingBadge, { backgroundColor: species.color + '20' }]}>
                      <Text style={[styles.trendingBadgeText, { color: species.color }]}>
                        {species.scans}
                      </Text>
                    </View>
                  </View>
                  <View style={styles.trendingBar}>
                    <View
                      style={[
                        styles.trendingFill,
                        { backgroundColor: species.color, width: `${80 - index * 15}%` }
                      ]}
                    />
                  </View>
                </View>
              ))}
            </View>
          </View>

          {/* Featured Discoveries */}
          <View style={styles.section}>
            <View style={styles.sectionHeader}>
              <View style={styles.sectionTitleContainer}>
                <Award size={20} color="#F59E0B" />
                <Text style={styles.sectionTitle}>Featured Discoveries</Text>
              </View>
              <TouchableOpacity>
                <Text style={styles.seeAllText}>See All</Text>
              </TouchableOpacity>
            </View>

            {featuredDiscoveries.map((discovery) => (
              <TouchableOpacity key={discovery.id} style={styles.discoveryCard}>
                <View style={styles.discoveryHeader}>
                  <View style={styles.discoveryImageContainer}>
                    <LinearGradient
                      colors={['#F3F4F6', '#E5E7EB']}
                      style={styles.discoveryImage}>
                      <CategoryIcon category={discovery.category} />
                    </LinearGradient>
                  </View>
                  
                  <View style={styles.discoveryInfo}>
                    <View style={styles.discoveryTitleRow}>
                      <Text style={styles.discoveryName}>{discovery.name}</Text>
                      <View
                        style={[
                          styles.rarityBadge,
                          { backgroundColor: getRarityColor(discovery.rarity) }
                        ]}>
                        <Text style={styles.rarityBadgeText}>{discovery.rarity}</Text>
                      </View>
                    </View>
                    
                    <View style={styles.discoveryMeta}>
                      <View style={styles.metaItem}>
                        <Users size={14} color="#6B7280" />
                        <Text style={styles.metaText}>{discovery.discoverer}</Text>
                      </View>
                      <View style={styles.metaItem}>
                        <MapPin size={14} color="#6B7280" />
                        <Text style={styles.metaText}>{discovery.location}</Text>
                      </View>
                    </View>
                    
                    <View style={styles.discoveryStats}>
                      <View style={styles.statItem}>
                        <Eye size={12} color="#6B7280" />
                        <Text style={styles.statText}>{discovery.views.toLocaleString()}</Text>
                      </View>
                      <View style={styles.statItem}>
                        <Calendar size={12} color="#6B7280" />
                        <Text style={styles.statText}>{discovery.date}</Text>
                      </View>
                      <View style={styles.statItem}>
                        <Star size={12} color="#F59E0B" />
                        <Text style={styles.statText}>{discovery.confidence}%</Text>
                      </View>
                    </View>
                  </View>
                </View>
              </TouchableOpacity>
            ))}
          </View>

          {/* Community Stats */}
          <View style={styles.section}>
            <View style={styles.communityStatsContainer}>
              <LinearGradient
                colors={['#22C55E', '#16A34A']}
                style={styles.communityStatsCard}>
                <Text style={styles.communityStatsTitle}>Community Impact</Text>
                <View style={styles.communityStatsGrid}>
                  <View style={styles.communityStat}>
                    <Text style={styles.communityStatNumber}>12.5K</Text>
                    <Text style={styles.communityStatLabel}>Species Documented</Text>
                  </View>
                  <View style={styles.communityStat}>
                    <Text style={styles.communityStatNumber}>89K</Text>
                    <Text style={styles.communityStatLabel}>Active Scanners</Text>
                  </View>
                  <View style={styles.communityStat}>
                    <Text style={styles.communityStatNumber}>2.1M</Text>
                    <Text style={styles.communityStatLabel}>Total Scans</Text>
                  </View>
                  <View style={styles.communityStat}>
                    <Text style={styles.communityStatNumber}>156</Text>
                    <Text style={styles.communityStatLabel}>Countries</Text>
                  </View>
                </View>
              </LinearGradient>
            </View>
          </View>

          {/* Bottom Spacing */}
          <View style={styles.bottomSpacing} />
        </ScrollView>
      </LinearGradient>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F0F9FF',
  },
  gradient: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 10,
  },
  title: {
    fontSize: 32,
    fontFamily: 'Inter-Bold',
    color: '#0C4A6E',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
  },
  searchContainer: {
    paddingHorizontal: 20,
    marginBottom: 20,
  },
  searchBar: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    paddingHorizontal: 16,
    paddingVertical: 12,
    gap: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#111827',
  },
  categoriesContainer: {
    marginBottom: 24,
  },
  categoriesScrollContent: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    gap: 12,
  },
  categoryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 10,
    gap: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  categoryButtonActive: {
    backgroundColor: '#3B82F6',
  },
  categoryText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
  },
  categoryTextActive: {
    color: '#FFFFFF',
  },
  section: {
    paddingHorizontal: 20,
    marginBottom: 24,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  sectionTitle: {
    fontSize: 20,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
  },
  seeAllText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#3B82F6',
  },
  trendingGrid: {
    gap: 12,
  },
  trendingCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  trendingHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  trendingName: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    flex: 1,
  },
  trendingBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
  },
  trendingBadgeText: {
    fontSize: 12,
    fontFamily: 'Inter-Bold',
  },
  trendingBar: {
    height: 4,
    backgroundColor: '#E5E7EB',
    borderRadius: 2,
  },
  trendingFill: {
    height: '100%',
    borderRadius: 2,
  },
  discoveryCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  discoveryHeader: {
    flexDirection: 'row',
    gap: 16,
  },
  discoveryImageContainer: {
    width: 60,
    height: 60,
  },
  discoveryImage: {
    width: '100%',
    height: '100%',
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  discoveryInfo: {
    flex: 1,
  },
  discoveryTitleRow: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  discoveryName: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    flex: 1,
    marginRight: 8,
  },
  rarityBadge: {
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 6,
  },
  rarityBadgeText: {
    fontSize: 10,
    fontFamily: 'Inter-Bold',
    color: '#FFFFFF',
  },
  discoveryMeta: {
    gap: 4,
    marginBottom: 8,
  },
  metaItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  metaText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
  },
  discoveryStats: {
    flexDirection: 'row',
    gap: 16,
  },
  statItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  statText: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
  },
  communityStatsContainer: {
    marginBottom: 20,
  },
  communityStatsCard: {
    borderRadius: 20,
    padding: 24,
  },
  communityStatsTitle: {
    fontSize: 20,
    fontFamily: 'Inter-Bold',
    color: '#FFFFFF',
    marginBottom: 20,
    textAlign: 'center',
  },
  communityStatsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  communityStat: {
    width: '48%',
    alignItems: 'center',
    marginBottom: 16,
  },
  communityStatNumber: {
    fontSize: 28,
    fontFamily: 'Inter-Bold',
    color: '#FFFFFF',
    marginBottom: 4,
  },
  communityStatLabel: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    color: '#FFFFFF',
    opacity: 0.9,
    textAlign: 'center',
  },
  bottomSpacing: {
    height: 100,
  },
});