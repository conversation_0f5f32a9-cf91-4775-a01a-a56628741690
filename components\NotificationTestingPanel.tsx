import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
  Switch,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import {
  TestTube,
  Send,
  Bell,
  Settings,
  BarChart3,
  Users,
  MapPin,
  Trophy,
  BookOpen,
  Clock,
  Zap,
  CheckCircle,
  XCircle,
} from 'lucide-react-native';
import { PushNotificationService } from '../services/PushNotificationService';
import { NotificationTriggerService } from '../services/NotificationTriggerService';

interface NotificationTestingPanelProps {
  isVisible: boolean;
  onClose: () => void;
}

export const NotificationTestingPanel: React.FC<NotificationTestingPanelProps> = ({
  isVisible,
  onClose,
}) => {
  const [testResults, setTestResults] = useState<{ [key: string]: boolean }>({});
  const [customTitle, setCustomTitle] = useState('Test Notification');
  const [customBody, setCustomBody] = useState('This is a test notification from Bioscan+');
  const [selectedTemplate, setSelectedTemplate] = useState('daily_nature_tip');
  const [testData, setTestData] = useState('{"species_name": "Red Cardinal", "streak_count": 7}');
  const [isLoading, setIsLoading] = useState(false);

  const pushService = PushNotificationService.getInstance();
  const triggerService = NotificationTriggerService.getInstance();

  const notificationTemplates = [
    { id: 'daily_nature_tip', name: 'Daily Nature Tip', icon: BookOpen },
    { id: 'new_species_discovered', name: 'New Species Discovery', icon: Trophy },
    { id: 'identification_streak', name: 'Identification Streak', icon: Zap },
    { id: 'location_reminder', name: 'Location Reminder', icon: MapPin },
    { id: 'community_highlight', name: 'Community Highlight', icon: Users },
    { id: 'achievement', name: 'Achievement Unlocked', icon: Trophy },
  ];

  const testScenarios = [
    {
      id: 'basic_notification',
      name: 'Basic Push Notification',
      description: 'Test basic notification delivery',
      icon: Bell,
    },
    {
      id: 'personalized_notification',
      name: 'Personalized Notification',
      description: 'Test template-based personalization',
      icon: Users,
    },
    {
      id: 'achievement_trigger',
      name: 'Achievement Trigger',
      description: 'Test achievement notification flow',
      icon: Trophy,
    },
    {
      id: 'location_trigger',
      name: 'Location-Based Trigger',
      description: 'Test location-based notifications',
      icon: MapPin,
    },
    {
      id: 'streak_notification',
      name: 'Streak Notification',
      description: 'Test streak achievement notification',
      icon: Zap,
    },
    {
      id: 'permission_check',
      name: 'Permission Status',
      description: 'Check notification permissions',
      icon: Settings,
    },
  ];

  if (!isVisible) return null;

  const runTest = async (testId: string) => {
    setIsLoading(true);
    let success = false;

    try {
      switch (testId) {
        case 'basic_notification':
          success = await testBasicNotification();
          break;
        case 'personalized_notification':
          success = await testPersonalizedNotification();
          break;
        case 'achievement_trigger':
          success = await testAchievementTrigger();
          break;
        case 'location_trigger':
          success = await testLocationTrigger();
          break;
        case 'streak_notification':
          success = await testStreakNotification();
          break;
        case 'permission_check':
          success = await testPermissionStatus();
          break;
        default:
          success = false;
      }

      setTestResults(prev => ({ ...prev, [testId]: success }));
      
      if (success) {
        Alert.alert('Test Passed ✅', `${testId} completed successfully`);
      } else {
        Alert.alert('Test Failed ❌', `${testId} encountered an error`);
      }

    } catch (error) {
      console.error(`Test ${testId} failed:`, error);
      setTestResults(prev => ({ ...prev, [testId]: false }));
      Alert.alert('Test Error', `${testId}: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  const testBasicNotification = async (): Promise<boolean> => {
    try {
      const notificationId = await pushService.scheduleLocalNotification({
        title: customTitle,
        body: customBody,
        data: { test: true, timestamp: Date.now() },
        priority: 'normal',
      });

      return notificationId !== null;
    } catch (error) {
      console.error('Basic notification test failed:', error);
      return false;
    }
  };

  const testPersonalizedNotification = async (): Promise<boolean> => {
    try {
      let parsedData = {};
      try {
        parsedData = JSON.parse(testData);
      } catch {
        parsedData = { test_value: 'default' };
      }

      const success = await pushService.sendPersonalizedNotification(
        selectedTemplate,
        parsedData
      );

      return success;
    } catch (error) {
      console.error('Personalized notification test failed:', error);
      return false;
    }
  };

  const testAchievementTrigger = async (): Promise<boolean> => {
    try {
      await triggerService.onNewSpeciesIdentified({
        speciesName: 'Quercus alba',
        commonName: 'White Oak',
        isFirstTime: true,
        totalIdentifications: 42,
        userId: 'test-user-id',
      });

      return true;
    } catch (error) {
      console.error('Achievement trigger test failed:', error);
      return false;
    }
  };

  const testLocationTrigger = async (): Promise<boolean> => {
    try {
      await triggerService.onLocationUpdate({
        latitude: 40.7128,
        longitude: -74.0060,
        userId: 'test-user-id',
      });

      return true;
    } catch (error) {
      console.error('Location trigger test failed:', error);
      return false;
    }
  };

  const testStreakNotification = async (): Promise<boolean> => {
    try {
      await triggerService.onIdentificationStreak({
        streakCount: 7,
        userId: 'test-user-id',
        lastIdentificationDate: new Date().toISOString(),
      });

      return true;
    } catch (error) {
      console.error('Streak notification test failed:', error);
      return false;
    }
  };

  const testPermissionStatus = async (): Promise<boolean> => {
    try {
      const preferences = await pushService.getNotificationPreferences();
      console.log('Current notification preferences:', preferences);
      
      Alert.alert(
        'Permission Status',
        `Notifications: ${preferences?.notifications_enabled ? 'Enabled' : 'Disabled'}\n` +
        `Push: ${preferences?.push_enabled ? 'Enabled' : 'Disabled'}\n` +
        `Daily Tips: ${preferences?.daily_tips_enabled ? 'Enabled' : 'Disabled'}`
      );

      return true;
    } catch (error) {
      console.error('Permission check failed:', error);
      return false;
    }
  };

  const runAllTests = async () => {
    setIsLoading(true);
    const results: { [key: string]: boolean } = {};

    for (const scenario of testScenarios) {
      try {
        await new Promise(resolve => setTimeout(resolve, 1000)); // Delay between tests
        
        let success = false;
        switch (scenario.id) {
          case 'basic_notification':
            success = await testBasicNotification();
            break;
          case 'personalized_notification':
            success = await testPersonalizedNotification();
            break;
          case 'permission_check':
            success = await testPermissionStatus();
            break;
          default:
            success = true; // Skip complex tests in batch mode
        }
        
        results[scenario.id] = success;
      } catch (error) {
        results[scenario.id] = false;
      }
    }

    setTestResults(results);
    setIsLoading(false);

    const passedTests = Object.values(results).filter(Boolean).length;
    const totalTests = Object.keys(results).length;
    
    Alert.alert(
      'Test Suite Complete',
      `${passedTests}/${totalTests} tests passed`
    );
  };

  const clearTestResults = () => {
    setTestResults({});
  };

  return (
    <View style={styles.container}>
      {/* Header */}
      <LinearGradient
        colors={['#8B5CF6', '#7C3AED']}
        style={styles.header}
      >
        <TestTube size={32} color="white" />
        <Text style={styles.headerTitle}>Notification Testing</Text>
        <Text style={styles.headerSubtitle}>
          Test and debug push notification functionality
        </Text>
      </LinearGradient>

      <ScrollView style={styles.content}>
        {/* Custom Notification Test */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Custom Notification</Text>
          
          <TextInput
            style={styles.input}
            placeholder="Notification Title"
            value={customTitle}
            onChangeText={setCustomTitle}
          />
          
          <TextInput
            style={[styles.input, styles.textArea]}
            placeholder="Notification Body"
            value={customBody}
            onChangeText={setCustomBody}
            multiline
            numberOfLines={3}
          />
          
          <TouchableOpacity
            style={styles.testButton}
            onPress={() => runTest('basic_notification')}
            disabled={isLoading}
          >
            <Send size={20} color="white" />
            <Text style={styles.testButtonText}>Send Test Notification</Text>
          </TouchableOpacity>
        </View>

        {/* Template Testing */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Template Testing</Text>
          
          <Text style={styles.label}>Select Template:</Text>
          <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.templateScroll}>
            {notificationTemplates.map((template) => {
              const Icon = template.icon;
              return (
                <TouchableOpacity
                  key={template.id}
                  style={[
                    styles.templateCard,
                    selectedTemplate === template.id && styles.selectedTemplate
                  ]}
                  onPress={() => setSelectedTemplate(template.id)}
                >
                  <Icon size={24} color={selectedTemplate === template.id ? '#8B5CF6' : '#6B7280'} />
                  <Text style={[
                    styles.templateName,
                    selectedTemplate === template.id && styles.selectedTemplateName
                  ]}>
                    {template.name}
                  </Text>
                </TouchableOpacity>
              );
            })}
          </ScrollView>
          
          <Text style={styles.label}>Test Data (JSON):</Text>
          <TextInput
            style={[styles.input, styles.textArea]}
            placeholder='{"species_name": "Red Cardinal", "streak_count": 7}'
            value={testData}
            onChangeText={setTestData}
            multiline
            numberOfLines={3}
          />
          
          <TouchableOpacity
            style={styles.testButton}
            onPress={() => runTest('personalized_notification')}
            disabled={isLoading}
          >
            <Users size={20} color="white" />
            <Text style={styles.testButtonText}>Test Template</Text>
          </TouchableOpacity>
        </View>

        {/* Test Scenarios */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Test Scenarios</Text>
          
          {testScenarios.map((scenario) => {
            const Icon = scenario.icon;
            const testResult = testResults[scenario.id];
            
            return (
              <View key={scenario.id} style={styles.scenarioCard}>
                <View style={styles.scenarioInfo}>
                  <Icon size={24} color="#374151" />
                  <View style={styles.scenarioText}>
                    <Text style={styles.scenarioName}>{scenario.name}</Text>
                    <Text style={styles.scenarioDescription}>{scenario.description}</Text>
                  </View>
                </View>
                
                <View style={styles.scenarioActions}>
                  {testResult !== undefined && (
                    testResult ? (
                      <CheckCircle size={20} color="#10B981" />
                    ) : (
                      <XCircle size={20} color="#EF4444" />
                    )
                  )}
                  
                  <TouchableOpacity
                    style={styles.runTestButton}
                    onPress={() => runTest(scenario.id)}
                    disabled={isLoading}
                  >
                    <Text style={styles.runTestButtonText}>Run</Text>
                  </TouchableOpacity>
                </View>
              </View>
            );
          })}
        </View>

        {/* Batch Actions */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Batch Actions</Text>
          
          <View style={styles.batchActions}>
            <TouchableOpacity
              style={[styles.batchButton, styles.primaryButton]}
              onPress={runAllTests}
              disabled={isLoading}
            >
              <BarChart3 size={20} color="white" />
              <Text style={styles.batchButtonText}>Run All Tests</Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[styles.batchButton, styles.secondaryButton]}
              onPress={clearTestResults}
            >
              <XCircle size={20} color="#6B7280" />
              <Text style={[styles.batchButtonText, styles.secondaryButtonText]}>Clear Results</Text>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>

      {/* Close Button */}
      <TouchableOpacity style={styles.closeButton} onPress={onClose}>
        <Text style={styles.closeButtonText}>Close Testing Panel</Text>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  header: {
    padding: 24,
    paddingTop: 60,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: 'white',
    marginTop: 8,
  },
  headerSubtitle: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.8)',
    marginTop: 4,
    textAlign: 'center',
  },
  content: {
    flex: 1,
    padding: 20,
  },
  section: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 16,
  },
  input: {
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    marginBottom: 12,
    backgroundColor: '#F9FAFB',
  },
  textArea: {
    height: 80,
    textAlignVertical: 'top',
  },
  label: {
    fontSize: 14,
    fontWeight: '500',
    color: '#374151',
    marginBottom: 8,
  },
  templateScroll: {
    marginBottom: 16,
  },
  templateCard: {
    alignItems: 'center',
    padding: 12,
    marginRight: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#E5E7EB',
    backgroundColor: '#F9FAFB',
    minWidth: 100,
  },
  selectedTemplate: {
    borderColor: '#8B5CF6',
    backgroundColor: '#F3F4F6',
  },
  templateName: {
    fontSize: 12,
    color: '#6B7280',
    marginTop: 4,
    textAlign: 'center',
  },
  selectedTemplateName: {
    color: '#8B5CF6',
    fontWeight: '500',
  },
  testButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#8B5CF6',
    padding: 12,
    borderRadius: 8,
    gap: 8,
  },
  testButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  scenarioCard: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 12,
    borderRadius: 8,
    backgroundColor: '#F9FAFB',
    marginBottom: 8,
  },
  scenarioInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    gap: 12,
  },
  scenarioText: {
    flex: 1,
  },
  scenarioName: {
    fontSize: 16,
    fontWeight: '500',
    color: '#111827',
  },
  scenarioDescription: {
    fontSize: 14,
    color: '#6B7280',
  },
  scenarioActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  runTestButton: {
    backgroundColor: '#3B82F6',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 6,
  },
  runTestButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '500',
  },
  batchActions: {
    flexDirection: 'row',
    gap: 12,
  },
  batchButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 12,
    borderRadius: 8,
    gap: 8,
  },
  primaryButton: {
    backgroundColor: '#10B981',
  },
  secondaryButton: {
    backgroundColor: '#F3F4F6',
    borderWidth: 1,
    borderColor: '#D1D5DB',
  },
  batchButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: 'white',
  },
  secondaryButtonText: {
    color: '#6B7280',
  },
  closeButton: {
    backgroundColor: '#EF4444',
    margin: 20,
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  closeButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default NotificationTestingPanel;
