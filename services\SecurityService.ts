import { supabase } from '../lib/supabase';
import * as Device from 'expo-device';
import * as Network from 'expo-network';
import { Platform } from 'react-native';

export interface SecurityEvent {
  id: string;
  user_id: string;
  event_type: 'login' | 'payment' | 'subscription_change' | 'suspicious_activity';
  event_data: any;
  risk_score: number;
  ip_address: string | null;
  device_info: any;
  location_data: any | null;
  created_at: string;
}

export interface RiskAssessment {
  risk_score: number;
  risk_level: 'low' | 'medium' | 'high' | 'critical';
  factors: string[];
  recommendations: string[];
  should_block: boolean;
}

export interface DeviceFingerprint {
  device_id: string;
  platform: string;
  os_version: string;
  app_version: string;
  device_name: string;
  is_emulator: boolean;
  screen_dimensions: string;
  timezone: string;
  language: string;
}

export class SecurityService {
  private readonly MAX_LOGIN_ATTEMPTS = 5;
  private readonly LOCKOUT_DURATION = 15 * 60 * 1000; // 15 minutes
  private readonly SUSPICIOUS_ACTIVITY_THRESHOLD = 10;

  /**
   * Generate device fingerprint
   */
  async generateDeviceFingerprint(): Promise<DeviceFingerprint> {
    try {
      const deviceInfo = {
        device_id: await this.getDeviceId(),
        platform: Platform.OS,
        os_version: Device.osVersion || 'unknown',
        app_version: process.env.EXPO_PUBLIC_APP_VERSION || '1.0.0',
        device_name: Device.deviceName || 'unknown',
        is_emulator: !Device.isDevice,
        screen_dimensions: `${Device.screenWidth}x${Device.screenHeight}`,
        timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
        language: Intl.DateTimeFormat().resolvedOptions().locale,
      };

      return deviceInfo;
    } catch (error) {
      console.error('Error generating device fingerprint:', error);
      throw error;
    }
  }

  /**
   * Get unique device identifier
   */
  private async getDeviceId(): Promise<string> {
    try {
      // Create a unique identifier based on device characteristics
      const deviceName = Device.deviceName || 'unknown';
      const osVersion = Device.osVersion || 'unknown';
      const modelName = Device.modelName || 'unknown';
      
      return `${Platform.OS}-${deviceName}-${modelName}-${osVersion}`.replace(/\s+/g, '-');
    } catch (error) {
      console.error('Error getting device ID:', error);
      return `${Platform.OS}-${Date.now()}`;
    }
  }

  /**
   * Get network information
   */
  private async getNetworkInfo(): Promise<any> {
    try {
      const networkState = await Network.getNetworkStateAsync();
      return {
        type: networkState.type,
        is_connected: networkState.isConnected,
        is_internet_reachable: networkState.isInternetReachable,
      };
    } catch (error) {
      console.error('Error getting network info:', error);
      return null;
    }
  }

  /**
   * Log security event
   */
  async logSecurityEvent(
    eventType: SecurityEvent['event_type'],
    eventData: any,
    riskScore: number = 0
  ): Promise<void> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return;

      const deviceInfo = await this.generateDeviceFingerprint();
      const networkInfo = await this.getNetworkInfo();

      const { error } = await supabase
        .from('security_events')
        .insert({
          user_id: user.id,
          event_type: eventType,
          event_data: eventData,
          risk_score: riskScore,
          ip_address: null, // Would be populated by server
          device_info: deviceInfo,
          network_info: networkInfo,
        });

      if (error) {
        console.error('Error logging security event:', error);
      }
    } catch (error) {
      console.error('Error logging security event:', error);
    }
  }

  /**
   * Assess payment risk
   */
  async assessPaymentRisk(paymentData: any): Promise<RiskAssessment> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        throw new Error('User not authenticated');
      }

      let riskScore = 0;
      const factors: string[] = [];
      const recommendations: string[] = [];

      // Check user account age
      const accountAge = Date.now() - new Date(user.created_at).getTime();
      const daysSinceCreation = accountAge / (1000 * 60 * 60 * 24);

      if (daysSinceCreation < 1) {
        riskScore += 30;
        factors.push('New account (less than 24 hours old)');
        recommendations.push('Verify email and phone number');
      } else if (daysSinceCreation < 7) {
        riskScore += 15;
        factors.push('Recent account creation');
      }

      // Check for multiple failed login attempts
      const { count: failedAttempts } = await supabase
        .from('security_events')
        .select('*', { count: 'exact', head: true })
        .eq('user_id', user.id)
        .eq('event_type', 'login')
        .eq('event_data->success', false)
        .gte('created_at', new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString());

      if (failedAttempts && failedAttempts > 3) {
        riskScore += 25;
        factors.push('Multiple failed login attempts in last 24 hours');
        recommendations.push('Require additional verification');
      }

      // Check for suspicious device patterns
      const deviceInfo = await this.generateDeviceFingerprint();
      if (deviceInfo.is_emulator) {
        riskScore += 40;
        factors.push('Payment from emulator/simulator');
        recommendations.push('Block payment from emulated devices');
      }

      // Check for rapid subscription changes
      const { count: recentSubscriptions } = await supabase
        .from('user_subscriptions')
        .select('*', { count: 'exact', head: true })
        .eq('user_id', user.id)
        .gte('created_at', new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString());

      if (recentSubscriptions && recentSubscriptions > 2) {
        riskScore += 20;
        factors.push('Multiple subscription changes in last 7 days');
        recommendations.push('Manual review required');
      }

      // Check payment amount patterns
      if (paymentData.amount > 10000) { // $100+
        riskScore += 15;
        factors.push('High-value transaction');
        recommendations.push('Additional verification for high-value payments');
      }

      // Determine risk level
      let riskLevel: RiskAssessment['risk_level'];
      if (riskScore >= 70) {
        riskLevel = 'critical';
      } else if (riskScore >= 50) {
        riskLevel = 'high';
      } else if (riskScore >= 30) {
        riskLevel = 'medium';
      } else {
        riskLevel = 'low';
      }

      const shouldBlock = riskScore >= 70;

      // Log the risk assessment
      await this.logSecurityEvent('payment', {
        ...paymentData,
        risk_assessment: {
          risk_score: riskScore,
          risk_level: riskLevel,
          factors,
        },
      }, riskScore);

      return {
        risk_score: riskScore,
        risk_level: riskLevel,
        factors,
        recommendations,
        should_block: shouldBlock,
      };
    } catch (error) {
      console.error('Error assessing payment risk:', error);
      throw error;
    }
  }

  /**
   * Check for account lockout
   */
  async checkAccountLockout(): Promise<{ locked: boolean; unlockTime?: Date }> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        return { locked: false };
      }

      // Check recent failed login attempts
      const { data: failedAttempts } = await supabase
        .from('security_events')
        .select('created_at')
        .eq('user_id', user.id)
        .eq('event_type', 'login')
        .eq('event_data->success', false)
        .gte('created_at', new Date(Date.now() - this.LOCKOUT_DURATION).toISOString())
        .order('created_at', { ascending: false })
        .limit(this.MAX_LOGIN_ATTEMPTS);

      if (failedAttempts && failedAttempts.length >= this.MAX_LOGIN_ATTEMPTS) {
        const lastAttempt = new Date(failedAttempts[0].created_at);
        const unlockTime = new Date(lastAttempt.getTime() + this.LOCKOUT_DURATION);

        if (Date.now() < unlockTime.getTime()) {
          return { locked: true, unlockTime };
        }
      }

      return { locked: false };
    } catch (error) {
      console.error('Error checking account lockout:', error);
      return { locked: false };
    }
  }

  /**
   * Detect suspicious activity patterns
   */
  async detectSuspiciousActivity(): Promise<boolean> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return false;

      // Check for rapid API calls
      const { count: recentEvents } = await supabase
        .from('security_events')
        .select('*', { count: 'exact', head: true })
        .eq('user_id', user.id)
        .gte('created_at', new Date(Date.now() - 5 * 60 * 1000).toISOString()); // Last 5 minutes

      if (recentEvents && recentEvents > this.SUSPICIOUS_ACTIVITY_THRESHOLD) {
        await this.logSecurityEvent('suspicious_activity', {
          reason: 'Rapid API calls',
          event_count: recentEvents,
          time_window: '5 minutes',
        }, 60);

        return true;
      }

      // Check for unusual device patterns
      const { data: deviceEvents } = await supabase
        .from('security_events')
        .select('device_info')
        .eq('user_id', user.id)
        .gte('created_at', new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString())
        .limit(10);

      if (deviceEvents && deviceEvents.length > 0) {
        const uniqueDevices = new Set(
          deviceEvents.map(event => event.device_info?.device_id).filter(Boolean)
        );

        if (uniqueDevices.size > 3) {
          await this.logSecurityEvent('suspicious_activity', {
            reason: 'Multiple devices in 24 hours',
            device_count: uniqueDevices.size,
          }, 40);

          return true;
        }
      }

      return false;
    } catch (error) {
      console.error('Error detecting suspicious activity:', error);
      return false;
    }
  }

  /**
   * Validate subscription change
   */
  async validateSubscriptionChange(
    oldPlanId: string | null,
    newPlanId: string
  ): Promise<{ valid: boolean; reason?: string }> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        return { valid: false, reason: 'User not authenticated' };
      }

      // Check for rapid subscription changes
      const { count: recentChanges } = await supabase
        .from('user_subscriptions')
        .select('*', { count: 'exact', head: true })
        .eq('user_id', user.id)
        .gte('created_at', new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString());

      if (recentChanges && recentChanges > 2) {
        await this.logSecurityEvent('subscription_change', {
          old_plan_id: oldPlanId,
          new_plan_id: newPlanId,
          reason: 'Too many changes in 24 hours',
        }, 50);

        return {
          valid: false,
          reason: 'Too many subscription changes in the last 24 hours. Please contact support.',
        };
      }

      // Assess overall risk
      const riskAssessment = await this.assessPaymentRisk({
        type: 'subscription_change',
        old_plan_id: oldPlanId,
        new_plan_id: newPlanId,
      });

      if (riskAssessment.should_block) {
        return {
          valid: false,
          reason: 'Subscription change blocked due to security concerns. Please contact support.',
        };
      }

      return { valid: true };
    } catch (error) {
      console.error('Error validating subscription change:', error);
      return { valid: false, reason: 'Validation error occurred' };
    }
  }

  /**
   * Get security summary for user
   */
  async getSecuritySummary(): Promise<any> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return null;

      const [lockoutStatus, suspiciousActivity, recentEvents] = await Promise.all([
        this.checkAccountLockout(),
        this.detectSuspiciousActivity(),
        supabase
          .from('security_events')
          .select('*')
          .eq('user_id', user.id)
          .order('created_at', { ascending: false })
          .limit(10),
      ]);

      return {
        account_locked: lockoutStatus.locked,
        unlock_time: lockoutStatus.unlockTime,
        suspicious_activity_detected: suspiciousActivity,
        recent_events: recentEvents.data || [],
        account_age_days: Math.floor(
          (Date.now() - new Date(user.created_at).getTime()) / (1000 * 60 * 60 * 24)
        ),
      };
    } catch (error) {
      console.error('Error getting security summary:', error);
      return null;
    }
  }
}
