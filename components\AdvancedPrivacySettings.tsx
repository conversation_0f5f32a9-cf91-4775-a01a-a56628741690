import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  Modal,
  Switch,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import {
  Shield,
  Eye,
  EyeOff,
  Download,
  FileText,
  Settings,
  Lock,
  Unlock,
  AlertTriangle,
  CheckCircle,
  Clock,
  Database,
  MapPin,
  Users,
  Activity,
  Fingerprint,
  Brain,
  Share2,
  Trash2,
  Info,
} from 'lucide-react-native';
import {
  AdvancedPrivacyService,
  AdvancedPrivacyPreferences,
} from '../services/AdvancedPrivacyService';
import {
  AdvancedDataExportService,
  ExportOptions,
  DataCategory,
} from '../services/AdvancedDataExportService';
import PrivacyControlCenter from './PrivacyControlCenter';
import PrivacyComplianceMonitor from './PrivacyComplianceMonitor';

interface AdvancedPrivacySettingsProps {
  onSettingsChange?: (settings: AdvancedPrivacyPreferences) => void;
}

export const AdvancedPrivacySettings: React.FC<AdvancedPrivacySettingsProps> = ({
  onSettingsChange,
}) => {
  const [preferences, setPreferences] = useState<AdvancedPrivacyPreferences | null>(null);
  const [dataCategories, setDataCategories] = useState<DataCategory[]>([]);
  const [exportEstimate, setExportEstimate] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [activeView, setActiveView] = useState<'settings' | 'control' | 'compliance' | 'export'>('settings');
  const [showExportModal, setShowExportModal] = useState(false);
  const [exportOptions, setExportOptions] = useState<ExportOptions>({
    format: 'json',
    includeMetadata: true,
    includeDeleted: false,
    categories: [],
  });

  const privacyService = AdvancedPrivacyService.getInstance();
  const exportService = AdvancedDataExportService.getInstance();

  useEffect(() => {
    loadPrivacySettings();
    loadDataCategories();
  }, []);

  const loadPrivacySettings = async () => {
    try {
      setLoading(true);
      const prefs = await privacyService.getAdvancedPrivacyPreferences();
      setPreferences(prefs);
    } catch (error) {
      console.error('Error loading privacy settings:', error);
      Alert.alert('Error', 'Failed to load privacy settings');
    } finally {
      setLoading(false);
    }
  };

  const loadDataCategories = async () => {
    try {
      const categories = exportService.getDataCategories();
      setDataCategories(categories);
      setExportOptions(prev => ({
        ...prev,
        categories: categories.map(c => c.name),
      }));
    } catch (error) {
      console.error('Error loading data categories:', error);
    }
  };

  const updatePreference = async (key: keyof AdvancedPrivacyPreferences, value: any) => {
    if (!preferences) return;

    const updatedPreferences = { ...preferences, [key]: value };
    setPreferences(updatedPreferences);

    const success = await privacyService.updateAdvancedPrivacyPreferences({ [key]: value });
    
    if (success) {
      if (onSettingsChange) {
        onSettingsChange(updatedPreferences);
      }
    } else {
      // Revert on failure
      setPreferences(preferences);
      Alert.alert('Error', 'Failed to update privacy setting');
    }
  };

  const handleDataExport = async () => {
    try {
      // Validate export request
      const validation = await exportService.validateExportRequest(exportOptions);
      
      if (!validation.valid) {
        Alert.alert(
          'Export Restricted',
          validation.restrictions.join('\n'),
          [{ text: 'OK' }]
        );
        return;
      }

      if (validation.warnings.length > 0) {
        Alert.alert(
          'Export Warning',
          validation.warnings.join('\n') + '\n\nDo you want to continue?',
          [
            { text: 'Cancel', style: 'cancel' },
            { text: 'Continue', onPress: () => performExport() }
          ]
        );
      } else {
        await performExport();
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to initiate data export');
    }
  };

  const performExport = async () => {
    try {
      setLoading(true);
      const result = await exportService.exportUserData(exportOptions);
      
      if (result.success && result.filePath) {
        Alert.alert(
          'Export Complete',
          `Your data has been exported successfully.\n\nFile size: ${(result.fileSize! / 1024).toFixed(1)} KB\nRecords: ${result.recordCount}`,
          [
            { text: 'OK' },
            { 
              text: 'Share', 
              onPress: () => exportService.shareExportFile(result.filePath!) 
            }
          ]
        );
      } else {
        Alert.alert('Export Failed', result.error || 'Unknown error occurred');
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to export data');
    } finally {
      setLoading(false);
      setShowExportModal(false);
    }
  };

  const estimateExportSize = async () => {
    try {
      const estimate = await exportService.estimateExportSize(exportOptions);
      setExportEstimate(estimate);
    } catch (error) {
      console.error('Error estimating export size:', error);
    }
  };

  const renderMainSettings = () => (
    <ScrollView style={styles.content}>
      {/* Quick Actions */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Privacy Actions</Text>
        
        <TouchableOpacity
          style={styles.actionCard}
          onPress={() => setActiveView('control')}
        >
          <Shield size={24} color="#3B82F6" />
          <View style={styles.actionContent}>
            <Text style={styles.actionTitle}>Privacy Control Center</Text>
            <Text style={styles.actionDescription}>
              Manage detailed privacy settings and data controls
            </Text>
          </View>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.actionCard}
          onPress={() => setShowExportModal(true)}
        >
          <Download size={24} color="#10B981" />
          <View style={styles.actionContent}>
            <Text style={styles.actionTitle}>Export My Data</Text>
            <Text style={styles.actionDescription}>
              Download a copy of all your personal data
            </Text>
          </View>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.actionCard}
          onPress={() => setActiveView('compliance')}
        >
          <FileText size={24} color="#F59E0B" />
          <View style={styles.actionContent}>
            <Text style={styles.actionTitle}>Compliance Monitor</Text>
            <Text style={styles.actionDescription}>
              View privacy compliance status and reports
            </Text>
          </View>
        </TouchableOpacity>
      </View>

      {/* Privacy Overview */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Privacy Overview</Text>
        
        <View style={styles.overviewGrid}>
          <View style={styles.overviewCard}>
            <Shield size={20} color="#10B981" />
            <Text style={styles.overviewValue}>
              {preferences?.data_minimization_enabled ? 'ON' : 'OFF'}
            </Text>
            <Text style={styles.overviewLabel}>Data Minimization</Text>
          </View>
          
          <View style={styles.overviewCard}>
            <Eye size={20} color={preferences?.allow_behavioral_analytics ? '#F59E0B' : '#10B981'} />
            <Text style={styles.overviewValue}>
              {preferences?.allow_behavioral_analytics ? 'ON' : 'OFF'}
            </Text>
            <Text style={styles.overviewLabel}>Analytics</Text>
          </View>
          
          <View style={styles.overviewCard}>
            <MapPin size={20} color="#3B82F6" />
            <Text style={styles.overviewValue}>
              {preferences?.location_precision_level?.toUpperCase() || 'CITY'}
            </Text>
            <Text style={styles.overviewLabel}>Location</Text>
          </View>
          
          <View style={styles.overviewCard}>
            <Share2 size={20} color={preferences?.allow_third_party_sharing ? '#F59E0B' : '#10B981'} />
            <Text style={styles.overviewValue}>
              {preferences?.allow_third_party_sharing ? 'ON' : 'OFF'}
            </Text>
            <Text style={styles.overviewLabel}>Sharing</Text>
          </View>
        </View>
      </View>

      {/* Key Settings */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Key Privacy Settings</Text>
        
        <SettingRow
          icon={Database}
          title="Data Minimization"
          description="Automatically reduce data collection to minimum necessary"
          value={preferences?.data_minimization_enabled ?? true}
          onValueChange={(value) => updatePreference('data_minimization_enabled', value)}
        />

        <SettingRow
          icon={Activity}
          title="Behavioral Analytics"
          description="Allow analysis of app usage patterns"
          value={preferences?.allow_behavioral_analytics ?? false}
          onValueChange={(value) => updatePreference('allow_behavioral_analytics', value)}
        />

        <SettingRow
          icon={Share2}
          title="Third-Party Sharing"
          description="Allow sharing data with trusted partners"
          value={preferences?.allow_third_party_sharing ?? false}
          onValueChange={(value) => updatePreference('allow_third_party_sharing', value)}
        />

        <SettingRow
          icon={Brain}
          title="User Profiling"
          description="Create personalized user profiles"
          value={preferences?.allow_profiling ?? false}
          onValueChange={(value) => updatePreference('allow_profiling', value)}
        />

        <SettingRow
          icon={Fingerprint}
          title="Biometric Processing"
          description="Allow processing of biometric identifiers"
          value={preferences?.allow_biometric_processing ?? false}
          onValueChange={(value) => updatePreference('allow_biometric_processing', value)}
        />
      </View>

      {/* Auto Export */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Automatic Data Export</Text>
        
        <SettingRow
          icon={Download}
          title="Auto Export"
          description="Automatically export your data periodically"
          value={preferences?.auto_export_enabled ?? false}
          onValueChange={(value) => updatePreference('auto_export_enabled', value)}
        />

        {preferences?.auto_export_enabled && (
          <View style={styles.autoExportDetails}>
            <Text style={styles.detailText}>
              Frequency: {preferences.export_frequency}
            </Text>
            <Text style={styles.detailText}>
              Format: {preferences.export_format.toUpperCase()}
            </Text>
          </View>
        )}
      </View>
    </ScrollView>
  );

  const renderExportModal = () => (
    <Modal
      visible={showExportModal}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={() => setShowExportModal(false)}
    >
      <View style={styles.modalContainer}>
        <View style={styles.modalHeader}>
          <Text style={styles.modalTitle}>Export My Data</Text>
          <TouchableOpacity onPress={() => setShowExportModal(false)}>
            <Text style={styles.closeButton}>Close</Text>
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.modalContent}>
          {/* Export Format */}
          <View style={styles.exportSection}>
            <Text style={styles.exportSectionTitle}>Export Format</Text>
            <View style={styles.formatOptions}>
              {['json', 'csv', 'xml'].map((format) => (
                <TouchableOpacity
                  key={format}
                  style={[
                    styles.formatOption,
                    exportOptions.format === format && styles.selectedFormat
                  ]}
                  onPress={() => setExportOptions(prev => ({ ...prev, format: format as any }))}
                >
                  <Text style={[
                    styles.formatText,
                    exportOptions.format === format && styles.selectedFormatText
                  ]}>
                    {format.toUpperCase()}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>

          {/* Data Categories */}
          <View style={styles.exportSection}>
            <Text style={styles.exportSectionTitle}>Data Categories</Text>
            {dataCategories.map((category) => (
              <View key={category.name} style={styles.categoryRow}>
                <View style={styles.categoryInfo}>
                  <Text style={styles.categoryName}>{category.description}</Text>
                  <Text style={styles.categorySensitivity}>
                    Sensitivity: {category.sensitivityLevel}
                  </Text>
                </View>
                <Switch
                  value={exportOptions.categories?.includes(category.name) ?? false}
                  onValueChange={(value) => {
                    const categories = exportOptions.categories || [];
                    if (value) {
                      setExportOptions(prev => ({
                        ...prev,
                        categories: [...categories, category.name]
                      }));
                    } else {
                      setExportOptions(prev => ({
                        ...prev,
                        categories: categories.filter(c => c !== category.name)
                      }));
                    }
                  }}
                />
              </View>
            ))}
          </View>

          {/* Export Options */}
          <View style={styles.exportSection}>
            <Text style={styles.exportSectionTitle}>Options</Text>
            
            <SettingRow
              icon={Info}
              title="Include Metadata"
              description="Include export metadata and timestamps"
              value={exportOptions.includeMetadata}
              onValueChange={(value) => setExportOptions(prev => ({ ...prev, includeMetadata: value }))}
            />

            <SettingRow
              icon={Trash2}
              title="Include Deleted Data"
              description="Include previously deleted records"
              value={exportOptions.includeDeleted}
              onValueChange={(value) => setExportOptions(prev => ({ ...prev, includeDeleted: value }))}
            />
          </View>

          {/* Export Estimate */}
          {exportEstimate && (
            <View style={styles.exportSection}>
              <Text style={styles.exportSectionTitle}>Export Estimate</Text>
              <Text style={styles.estimateText}>
                Estimated Size: {(exportEstimate.estimatedSize / 1024).toFixed(1)} KB
              </Text>
              <Text style={styles.estimateText}>
                Record Count: {exportEstimate.recordCount}
              </Text>
            </View>
          )}

          {/* Actions */}
          <View style={styles.exportActions}>
            <TouchableOpacity
              style={styles.estimateButton}
              onPress={estimateExportSize}
            >
              <Text style={styles.estimateButtonText}>Estimate Size</Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={styles.exportButton}
              onPress={handleDataExport}
            >
              <Text style={styles.exportButtonText}>Export Data</Text>
            </TouchableOpacity>
          </View>
        </ScrollView>
      </View>
    </Modal>
  );

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <Shield size={32} color="#3B82F6" />
        <Text style={styles.loadingText}>Loading privacy settings...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Header */}
      <LinearGradient
        colors={['#3B82F6', '#1D4ED8']}
        style={styles.header}
      >
        <Shield size={32} color="white" />
        <Text style={styles.headerTitle}>Advanced Privacy</Text>
        <Text style={styles.headerSubtitle}>
          Complete control over your personal data
        </Text>
      </LinearGradient>

      {/* Navigation */}
      <View style={styles.navigation}>
        {[
          { key: 'settings', label: 'Settings', icon: Settings },
          { key: 'control', label: 'Control', icon: Shield },
          { key: 'compliance', label: 'Compliance', icon: FileText },
        ].map(({ key, label, icon: Icon }) => (
          <TouchableOpacity
            key={key}
            style={[styles.navButton, activeView === key && styles.activeNavButton]}
            onPress={() => setActiveView(key as any)}
          >
            <Icon size={18} color={activeView === key ? '#3B82F6' : '#6B7280'} />
            <Text style={[styles.navText, activeView === key && styles.activeNavText]}>
              {label}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      {/* Content */}
      {activeView === 'settings' && renderMainSettings()}
      {activeView === 'control' && <PrivacyControlCenter />}
      {activeView === 'compliance' && <PrivacyComplianceMonitor />}

      {/* Export Modal */}
      {renderExportModal()}
    </View>
  );
};

interface SettingRowProps {
  icon: any;
  title: string;
  description: string;
  value: boolean;
  onValueChange: (value: boolean) => void;
}

const SettingRow: React.FC<SettingRowProps> = ({
  icon: Icon,
  title,
  description,
  value,
  onValueChange,
}) => (
  <View style={styles.settingRow}>
    <Icon size={20} color="#3B82F6" />
    <View style={styles.settingContent}>
      <Text style={styles.settingTitle}>{title}</Text>
      <Text style={styles.settingDescription}>{description}</Text>
    </View>
    <Switch
      value={value}
      onValueChange={onValueChange}
      trackColor={{ false: '#E5E7EB', true: '#3B82F6' }}
      thumbColor={value ? '#FFFFFF' : '#F3F4F6'}
    />
  </View>
);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#F9FAFB',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#6B7280',
  },
  header: {
    padding: 24,
    paddingTop: 60,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: 'white',
    marginTop: 8,
  },
  headerSubtitle: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.8)',
    marginTop: 4,
    textAlign: 'center',
  },
  navigation: {
    flexDirection: 'row',
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  navButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    gap: 6,
  },
  activeNavButton: {
    borderBottomWidth: 2,
    borderBottomColor: '#3B82F6',
  },
  navText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#6B7280',
  },
  activeNavText: {
    color: '#3B82F6',
  },
  content: {
    flex: 1,
    padding: 20,
  },
  section: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 16,
  },
  actionCard: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#F9FAFB',
    borderRadius: 8,
    marginBottom: 12,
    gap: 12,
  },
  actionContent: {
    flex: 1,
  },
  actionTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#111827',
    marginBottom: 4,
  },
  actionDescription: {
    fontSize: 14,
    color: '#6B7280',
    lineHeight: 18,
  },
  overviewGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  overviewCard: {
    flex: 1,
    minWidth: '45%',
    backgroundColor: '#F9FAFB',
    borderRadius: 8,
    padding: 12,
    alignItems: 'center',
  },
  overviewValue: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#111827',
    marginTop: 8,
  },
  overviewLabel: {
    fontSize: 12,
    color: '#6B7280',
    marginTop: 4,
  },
  settingRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
    gap: 12,
  },
  settingContent: {
    flex: 1,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#111827',
    marginBottom: 2,
  },
  settingDescription: {
    fontSize: 14,
    color: '#6B7280',
    lineHeight: 18,
  },
  autoExportDetails: {
    marginTop: 12,
    padding: 12,
    backgroundColor: '#F3F4F6',
    borderRadius: 8,
  },
  detailText: {
    fontSize: 14,
    color: '#374151',
    marginBottom: 4,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: 'white',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827',
  },
  closeButton: {
    fontSize: 16,
    color: '#3B82F6',
    fontWeight: '500',
  },
  modalContent: {
    flex: 1,
    padding: 20,
  },
  exportSection: {
    marginBottom: 24,
  },
  exportSectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 12,
  },
  formatOptions: {
    flexDirection: 'row',
    gap: 8,
  },
  formatOption: {
    flex: 1,
    padding: 12,
    backgroundColor: '#F3F4F6',
    borderRadius: 8,
    alignItems: 'center',
  },
  selectedFormat: {
    backgroundColor: '#3B82F6',
  },
  formatText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#374151',
  },
  selectedFormatText: {
    color: 'white',
  },
  categoryRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
  },
  categoryInfo: {
    flex: 1,
  },
  categoryName: {
    fontSize: 14,
    fontWeight: '500',
    color: '#111827',
    marginBottom: 2,
  },
  categorySensitivity: {
    fontSize: 12,
    color: '#6B7280',
  },
  estimateText: {
    fontSize: 14,
    color: '#374151',
    marginBottom: 4,
  },
  exportActions: {
    flexDirection: 'row',
    gap: 12,
    marginTop: 20,
  },
  estimateButton: {
    flex: 1,
    backgroundColor: '#F3F4F6',
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  estimateButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#374151',
  },
  exportButton: {
    flex: 1,
    backgroundColor: '#3B82F6',
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  exportButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: 'white',
  },
});

export default AdvancedPrivacySettings;
