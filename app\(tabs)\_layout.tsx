import { Tabs } from 'expo-router';
import { View, Platform } from 'react-native';
import { BlurView } from 'expo-blur';
import { 
  <PERSON>, 
  Compass, 
  BookOpen, 
  User,
  Sparkles,
  Settings
} from 'lucide-react-native';

export default function TabLayout() {
  return (
    <Tabs
      screenOptions={{
        headerShown: false,
        tabBarStyle: {
          position: 'absolute',
          bottom: 0,
          left: 0,
          right: 0,
          height: Platform.OS === 'ios' ? 90 : 70,
          paddingBottom: Platform.OS === 'ios' ? 25 : 10,
          paddingTop: 10,
          borderTopWidth: 0,
          backgroundColor: Platform.OS === 'web' ? 'rgba(255, 255, 255, 0.9)' : 'transparent',
          backdropFilter: Platform.OS === 'web' ? 'blur(20px)' : undefined,
        },
        tabBarBackground: Platform.OS !== 'web' ? () => (
          <BlurView
            intensity={100}
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              bottom: 0,
              right: 0,
            }}
          />
        ) : undefined,
        tabBarActiveTintColor: '#22C55E',
        tabBarInactiveTintColor: '#6B7280',
        tabBarLabelStyle: {
          fontFamily: 'Inter-Medium',
          fontSize: 12,
          marginTop: 4,
        },
      }}>
      <Tabs.Screen
        name="index"
        options={{
          title: 'Scan',
          tabBarIcon: ({ size, color, focused }) => (
            <View
              style={{
                padding: 8,
                borderRadius: 16,
                backgroundColor: focused ? 'rgba(34, 197, 94, 0.1)' : 'transparent',
              }}>
              {focused ? (
                <Sparkles size={size} color={color} />
              ) : (
                <Camera size={size} color={color} />
              )}
            </View>
          ),
        }}
      />
      <Tabs.Screen
        name="discover"
        options={{
          title: 'Discover',
          tabBarIcon: ({ size, color, focused }) => (
            <View
              style={{
                padding: 8,
                borderRadius: 16,
                backgroundColor: focused ? 'rgba(34, 197, 94, 0.1)' : 'transparent',
              }}>
              <Compass size={size} color={color} />
            </View>
          ),
        }}
      />
      <Tabs.Screen
        name="collection"
        options={{
          title: 'Collection',
          tabBarIcon: ({ size, color, focused }) => (
            <View
              style={{
                padding: 8,
                borderRadius: 16,
                backgroundColor: focused ? 'rgba(34, 197, 94, 0.1)' : 'transparent',
              }}>
              <BookOpen size={size} color={color} />
            </View>
          ),
        }}
      />
      <Tabs.Screen
        name="profile"
        options={{
          title: 'Profile',
          tabBarIcon: ({ size, color, focused }) => (
            <View
              style={{
                padding: 8,
                borderRadius: 16,
                backgroundColor: focused ? 'rgba(34, 197, 94, 0.1)' : 'transparent',
              }}>
              <User size={size} color={color} />
            </View>
          ),
        }}
      />
      <Tabs.Screen
        name="settings"
        options={{
          title: 'Settings',
          tabBarIcon: ({ size, color, focused }) => (
            <View
              style={{
                padding: 8,
                borderRadius: 16,
                backgroundColor: focused ? 'rgba(34, 197, 94, 0.1)' : 'transparent',
              }}>
              <Settings size={size} color={color} />
            </View>
          ),
        }}
      />
    </Tabs>
  );
}