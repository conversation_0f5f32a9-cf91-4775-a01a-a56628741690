import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';

interface UserSettings {
  apiKey: string;
  theme: 'light' | 'dark' | 'auto';
  notifications: {
    enabled: boolean;
    scanResults: boolean;
    achievements: boolean;
    community: boolean;
  };
  camera: {
    autoFocus: boolean;
    flashMode: 'auto' | 'on' | 'off';
    saveOriginals: boolean;
    imageQuality: 'low' | 'medium' | 'high';
  };
  identification: {
    confidenceThreshold: number;
    maxAlternatives: number;
    enableOfflineMode: boolean;
    autoSave: boolean;
  };
  privacy: {
    shareLocation: boolean;
    publicProfile: boolean;
    dataCollection: boolean;
  };
  accessibility: {
    soundEnabled: boolean;
    hapticFeedback: boolean;
    largeText: boolean;
    highContrast: boolean;
  };
  language: string;
  units: 'metric' | 'imperial';
}

const defaultSettings: UserSettings = {
  apiKey: '',
  theme: 'auto',
  notifications: {
    enabled: true,
    scanResults: true,
    achievements: true,
    community: false,
  },
  camera: {
    autoFocus: true,
    flashMode: 'auto',
    saveOriginals: true,
    imageQuality: 'high',
  },
  identification: {
    confidenceThreshold: 70,
    maxAlternatives: 3,
    enableOfflineMode: false,
    autoSave: true,
  },
  privacy: {
    shareLocation: false,
    publicProfile: false,
    dataCollection: true,
  },
  accessibility: {
    soundEnabled: true,
    hapticFeedback: true,
    largeText: false,
    highContrast: false,
  },
  language: 'en',
  units: 'metric',
};

interface SettingsContextType {
  settings: UserSettings;
  updateSettings: (newSettings: Partial<UserSettings>) => Promise<void>;
  resetSettings: () => Promise<void>;
  isLoading: boolean;
}

const SettingsContext = createContext<SettingsContextType | undefined>(undefined);

export const useSettings = () => {
  const context = useContext(SettingsContext);
  if (!context) {
    throw new Error('useSettings must be used within a SettingsProvider');
  }
  return context;
};

interface SettingsProviderProps {
  children: ReactNode;
}

export const SettingsProvider: React.FC<SettingsProviderProps> = ({ children }) => {
  const [settings, setSettings] = useState<UserSettings>(defaultSettings);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = async () => {
    try {
      const savedSettings = await AsyncStorage.getItem('userSettings');
      if (savedSettings) {
        const parsed = JSON.parse(savedSettings);
        setSettings({ ...defaultSettings, ...parsed });
      }
    } catch (error) {
      console.error('Failed to load settings:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const updateSettings = async (newSettings: Partial<UserSettings>) => {
    try {
      const updatedSettings = { ...settings, ...newSettings };
      await AsyncStorage.setItem('userSettings', JSON.stringify(updatedSettings));
      setSettings(updatedSettings);
    } catch (error) {
      console.error('Failed to save settings:', error);
      throw error;
    }
  };

  const resetSettings = async () => {
    try {
      await AsyncStorage.setItem('userSettings', JSON.stringify(defaultSettings));
      setSettings(defaultSettings);
    } catch (error) {
      console.error('Failed to reset settings:', error);
      throw error;
    }
  };

  return (
    <SettingsContext.Provider
      value={{
        settings,
        updateSettings,
        resetSettings,
        isLoading,
      }}>
      {children}
    </SettingsContext.Provider>
  );
};

export default SettingsContext;