import passport from 'passport';
import { Strategy as GoogleStrategy } from 'passport-google-oauth20';
import { Strategy as AppleStrategy } from 'passport-apple';
import { Strategy as JwtStrategy, ExtractJwt } from 'passport-jwt';
import { Application } from 'express';
import crypto from 'crypto';

import { prisma } from '../index';
import { logger } from '../utils/logger';
import { generateTokenPair } from '../utils/jwt';

export function passportConfig(app: Application): void {
  // Initialize Passport
  app.use(passport.initialize());
  app.use(passport.session());

  // Serialize user for session
  passport.serializeUser((user: any, done) => {
    done(null, user.id);
  });

  // Deserialize user from session
  passport.deserializeUser(async (id: string, done) => {
    try {
      const user = await prisma.user.findUnique({
        where: { id },
        select: {
          id: true,
          email: true,
          firstName: true,
          lastName: true,
          role: true,
          status: true
        }
      });
      done(null, user);
    } catch (error) {
      done(error, null);
    }
  });

  // JWT Strategy for API authentication
  passport.use(new JwtStrategy({
    jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
    secretOrKey: process.env.JWT_SECRET!,
    issuer: 'bioscan-auth',
    audience: 'bioscan-app'
  }, async (payload, done) => {
    try {
      const user = await prisma.user.findUnique({
        where: { id: payload.id },
        select: {
          id: true,
          email: true,
          firstName: true,
          lastName: true,
          role: true,
          status: true
        }
      });

      if (!user || user.status !== 'ACTIVE') {
        return done(null, false);
      }

      return done(null, user);
    } catch (error) {
      return done(error, false);
    }
  }));

  // Google OAuth2 Strategy with PKCE
  if (process.env.GOOGLE_CLIENT_ID && process.env.GOOGLE_CLIENT_SECRET) {
    passport.use(new GoogleStrategy({
      clientID: process.env.GOOGLE_CLIENT_ID,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET,
      callbackURL: '/auth/google/callback',
      scope: ['profile', 'email'],
      state: true // Enable state parameter for CSRF protection
    }, async (accessToken, refreshToken, profile, done) => {
      try {
        logger.info('Google OAuth callback', { profileId: profile.id });

        // Validate state parameter (CSRF protection)
        // Note: In a real implementation, you'd validate the state parameter here

        const email = profile.emails?.[0]?.value;
        if (!email) {
          return done(new Error('No email provided by Google'), null);
        }

        // Check if user already exists
        let user = await prisma.user.findFirst({
          where: {
            OR: [
              { email },
              { googleId: profile.id }
            ]
          }
        });

        if (user) {
          // Update Google ID if not set
          if (!user.googleId) {
            user = await prisma.user.update({
              where: { id: user.id },
              data: { googleId: profile.id }
            });
          }

          // Update last login
          await prisma.user.update({
            where: { id: user.id },
            data: { lastLoginAt: new Date() }
          });
        } else {
          // Create new user
          user = await prisma.user.create({
            data: {
              email,
              googleId: profile.id,
              firstName: profile.name?.givenName,
              lastName: profile.name?.familyName,
              emailVerified: true, // Google emails are pre-verified
              role: 'USER',
              status: 'ACTIVE',
              lastLoginAt: new Date()
            }
          });

          logger.info('New user created via Google OAuth', { userId: user.id });
        }

        // Log successful login
        await prisma.loginAttempt.create({
          data: {
            userId: user.id,
            email: user.email,
            ipAddress: '', // Will be set by middleware
            success: true
          }
        });

        return done(null, user);
      } catch (error) {
        logger.error('Google OAuth error:', error);
        return done(error, null);
      }
    }));
  }

  // Apple OAuth2 Strategy with PKCE
  if (process.env.APPLE_CLIENT_ID && process.env.APPLE_PRIVATE_KEY) {
    passport.use(new AppleStrategy({
      clientID: process.env.APPLE_CLIENT_ID,
      teamID: process.env.APPLE_TEAM_ID!,
      keyID: process.env.APPLE_KEY_ID!,
      privateKey: process.env.APPLE_PRIVATE_KEY,
      callbackURL: '/auth/apple/callback',
      scope: ['name', 'email'],
      state: true // Enable state parameter for CSRF protection
    }, async (accessToken, refreshToken, idToken, profile, done) => {
      try {
        logger.info('Apple OAuth callback', { profileId: profile.id });

        const email = profile.email;
        if (!email) {
          return done(new Error('No email provided by Apple'), null);
        }

        // Check if user already exists
        let user = await prisma.user.findFirst({
          where: {
            OR: [
              { email },
              { appleId: profile.id }
            ]
          }
        });

        if (user) {
          // Update Apple ID if not set
          if (!user.appleId) {
            user = await prisma.user.update({
              where: { id: user.id },
              data: { appleId: profile.id }
            });
          }

          // Update last login
          await prisma.user.update({
            where: { id: user.id },
            data: { lastLoginAt: new Date() }
          });
        } else {
          // Create new user
          user = await prisma.user.create({
            data: {
              email,
              appleId: profile.id,
              firstName: profile.name?.firstName,
              lastName: profile.name?.lastName,
              emailVerified: true, // Apple emails are pre-verified
              role: 'USER',
              status: 'ACTIVE',
              lastLoginAt: new Date()
            }
          });

          logger.info('New user created via Apple OAuth', { userId: user.id });
        }

        // Log successful login
        await prisma.loginAttempt.create({
          data: {
            userId: user.id,
            email: user.email,
            ipAddress: '', // Will be set by middleware
            success: true
          }
        });

        return done(null, user);
      } catch (error) {
        logger.error('Apple OAuth error:', error);
        return done(error, null);
      }
    }));
  }
}

/**
 * Generate OAuth state parameter for CSRF protection
 */
export function generateOAuthState(): string {
  return crypto.randomBytes(32).toString('hex');
}

/**
 * Validate OAuth state parameter
 */
export function validateOAuthState(sessionState: string, receivedState: string): boolean {
  return sessionState === receivedState;
}

/**
 * Handle OAuth success - generate tokens and redirect
 */
export async function handleOAuthSuccess(user: any, req: any, res: any): Promise<void> {
  try {
    // Generate token pair
    const tokens = await generateTokenPair(
      user.id,
      user.email,
      user.role,
      {
        userAgent: req.get('User-Agent'),
        ipAddress: req.ip
      }
    );

    // Set refresh token as HTTP-only cookie
    res.cookie('refreshToken', tokens.refreshToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
      path: '/auth/refresh'
    });

    // Redirect to frontend with access token
    const redirectUrl = new URL(process.env.FRONTEND_URL || 'http://localhost:8081');
    redirectUrl.searchParams.set('token', tokens.accessToken);
    redirectUrl.searchParams.set('expires', tokens.expiresIn.toString());

    res.redirect(redirectUrl.toString());
  } catch (error) {
    logger.error('OAuth success handler error:', error);
    res.redirect(`${process.env.FRONTEND_URL}/auth/error?message=oauth_error`);
  }
}

/**
 * Handle OAuth error
 */
export function handleOAuthError(error: any, req: any, res: any): void {
  logger.error('OAuth error:', error);
  res.redirect(`${process.env.FRONTEND_URL}/auth/error?message=oauth_failed`);
}
