import { supabase } from '../lib/supabase';
import * as Device from 'expo-device';
import * as Network from 'expo-network';
import AsyncStorage from '@react-native-async-storage/async-storage';

export interface FraudDetectionResult {
  isBlocked: boolean;
  riskScore: number;
  riskFactors: string[];
  requiresVerification: boolean;
  message?: string;
}

export interface PaymentValidationRequest {
  paymentMethodId: string;
  amount: number;
  currency?: string;
}

export class FraudDetectionService {
  private static readonly STORAGE_KEYS = {
    DEVICE_FINGERPRINT: 'device_fingerprint',
    PAYMENT_ATTEMPTS: 'payment_attempts',
    LAST_VALIDATION: 'last_validation'
  };

  private static readonly RATE_LIMITS = {
    MAX_ATTEMPTS_PER_HOUR: 5,
    MAX_ATTEMPTS_PER_DAY: 20,
    COOLDOWN_PERIOD: 60 * 60 * 1000 // 1 hour in milliseconds
  };

  /**
   * Validate a payment before processing
   */
  static async validatePayment(request: PaymentValidationRequest): Promise<FraudDetectionResult> {
    try {
      // Check local rate limits first
      const rateLimitResult = await this.checkLocalRateLimit();
      if (rateLimitResult.isBlocked) {
        return rateLimitResult;
      }

      // Generate device fingerprint
      const deviceFingerprint = await this.generateDeviceFingerprint();

      // Get network information
      const networkInfo = await Network.getNetworkStateAsync();

      // Call server-side validation
      const { data, error } = await supabase.functions.invoke('validate-payment', {
        body: {
          ...request,
          deviceFingerprint,
          networkInfo: {
            type: networkInfo.type,
            isConnected: networkInfo.isConnected,
            isInternetReachable: networkInfo.isInternetReachable
          }
        }
      });

      if (error) {
        console.error('Payment validation error:', error);
        return {
          isBlocked: false,
          riskScore: 50,
          riskFactors: ['validation_error'],
          requiresVerification: true,
          message: 'Unable to validate payment. Please try again.'
        };
      }

      // Update local attempt tracking
      await this.updateAttemptTracking(data.riskScore);

      return {
        isBlocked: data.blocked || false,
        riskScore: data.riskScore || 0,
        riskFactors: Object.keys(data.riskFactors || {}),
        requiresVerification: data.requiresVerification || false,
        message: data.blocked ? 'Payment blocked for security reasons.' : undefined
      };

    } catch (error) {
      console.error('Fraud detection error:', error);
      return {
        isBlocked: false,
        riskScore: 30,
        riskFactors: ['system_error'],
        requiresVerification: true,
        message: 'Security check failed. Please try again.'
      };
    }
  }

  /**
   * Check if user has exceeded local rate limits
   */
  private static async checkLocalRateLimit(): Promise<FraudDetectionResult> {
    try {
      const attemptsData = await AsyncStorage.getItem(this.STORAGE_KEYS.PAYMENT_ATTEMPTS);
      const attempts = attemptsData ? JSON.parse(attemptsData) : { hourly: [], daily: [] };

      const now = Date.now();
      const oneHourAgo = now - 60 * 60 * 1000;
      const oneDayAgo = now - 24 * 60 * 60 * 1000;

      // Filter recent attempts
      const hourlyAttempts = attempts.hourly.filter((timestamp: number) => timestamp > oneHourAgo);
      const dailyAttempts = attempts.daily.filter((timestamp: number) => timestamp > oneDayAgo);

      // Check limits
      if (hourlyAttempts.length >= this.RATE_LIMITS.MAX_ATTEMPTS_PER_HOUR) {
        return {
          isBlocked: true,
          riskScore: 100,
          riskFactors: ['rate_limit_exceeded'],
          requiresVerification: false,
          message: 'Too many payment attempts. Please wait before trying again.'
        };
      }

      if (dailyAttempts.length >= this.RATE_LIMITS.MAX_ATTEMPTS_PER_DAY) {
        return {
          isBlocked: true,
          riskScore: 100,
          riskFactors: ['daily_limit_exceeded'],
          requiresVerification: false,
          message: 'Daily payment limit reached. Please try again tomorrow.'
        };
      }

      return {
        isBlocked: false,
        riskScore: 0,
        riskFactors: [],
        requiresVerification: false
      };

    } catch (error) {
      console.error('Rate limit check error:', error);
      return {
        isBlocked: false,
        riskScore: 10,
        riskFactors: ['rate_limit_check_error'],
        requiresVerification: false
      };
    }
  }

  /**
   * Update local attempt tracking
   */
  private static async updateAttemptTracking(riskScore: number): Promise<void> {
    try {
      const attemptsData = await AsyncStorage.getItem(this.STORAGE_KEYS.PAYMENT_ATTEMPTS);
      const attempts = attemptsData ? JSON.parse(attemptsData) : { hourly: [], daily: [] };

      const now = Date.now();
      const oneHourAgo = now - 60 * 60 * 1000;
      const oneDayAgo = now - 24 * 60 * 60 * 1000;

      // Clean old attempts and add new one
      attempts.hourly = attempts.hourly.filter((timestamp: number) => timestamp > oneHourAgo);
      attempts.daily = attempts.daily.filter((timestamp: number) => timestamp > oneDayAgo);
      
      attempts.hourly.push(now);
      attempts.daily.push(now);

      await AsyncStorage.setItem(this.STORAGE_KEYS.PAYMENT_ATTEMPTS, JSON.stringify(attempts));

      // Store last validation info
      await AsyncStorage.setItem(this.STORAGE_KEYS.LAST_VALIDATION, JSON.stringify({
        timestamp: now,
        riskScore
      }));

    } catch (error) {
      console.error('Error updating attempt tracking:', error);
    }
  }

  /**
   * Generate device fingerprint for fraud detection
   */
  private static async generateDeviceFingerprint(): Promise<string> {
    try {
      // Check if we already have a fingerprint
      const existingFingerprint = await AsyncStorage.getItem(this.STORAGE_KEYS.DEVICE_FINGERPRINT);
      if (existingFingerprint) {
        return existingFingerprint;
      }

      // Generate new fingerprint
      const deviceInfo = {
        brand: Device.brand,
        manufacturer: Device.manufacturer,
        modelName: Device.modelName,
        osName: Device.osName,
        osVersion: Device.osVersion,
        platformApiLevel: Device.platformApiLevel,
        deviceType: Device.deviceType,
        isDevice: Device.isDevice,
        timestamp: Date.now()
      };

      // Create a hash-like fingerprint
      const fingerprintString = JSON.stringify(deviceInfo);
      const fingerprint = btoa(fingerprintString).replace(/[^a-zA-Z0-9]/g, '').substring(0, 32);

      // Store for future use
      await AsyncStorage.setItem(this.STORAGE_KEYS.DEVICE_FINGERPRINT, fingerprint);

      return fingerprint;

    } catch (error) {
      console.error('Error generating device fingerprint:', error);
      return 'unknown_device';
    }
  }

  /**
   * Clear fraud detection data (for testing or user request)
   */
  static async clearFraudData(): Promise<void> {
    try {
      await AsyncStorage.multiRemove([
        this.STORAGE_KEYS.DEVICE_FINGERPRINT,
        this.STORAGE_KEYS.PAYMENT_ATTEMPTS,
        this.STORAGE_KEYS.LAST_VALIDATION
      ]);
    } catch (error) {
      console.error('Error clearing fraud data:', error);
    }
  }

  /**
   * Get fraud detection statistics for user
   */
  static async getFraudStats(): Promise<{
    deviceFingerprint: string;
    recentAttempts: number;
    lastValidation?: { timestamp: number; riskScore: number };
  }> {
    try {
      const [fingerprint, attemptsData, lastValidationData] = await AsyncStorage.multiGet([
        this.STORAGE_KEYS.DEVICE_FINGERPRINT,
        this.STORAGE_KEYS.PAYMENT_ATTEMPTS,
        this.STORAGE_KEYS.LAST_VALIDATION
      ]);

      const attempts = attemptsData[1] ? JSON.parse(attemptsData[1]) : { hourly: [] };
      const lastValidation = lastValidationData[1] ? JSON.parse(lastValidationData[1]) : null;

      const oneHourAgo = Date.now() - 60 * 60 * 1000;
      const recentAttempts = attempts.hourly.filter((timestamp: number) => timestamp > oneHourAgo).length;

      return {
        deviceFingerprint: fingerprint[1] || 'not_generated',
        recentAttempts,
        lastValidation
      };

    } catch (error) {
      console.error('Error getting fraud stats:', error);
      return {
        deviceFingerprint: 'error',
        recentAttempts: 0
      };
    }
  }

  /**
   * Check if payment is allowed based on current state
   */
  static async isPaymentAllowed(): Promise<boolean> {
    try {
      const rateLimitResult = await this.checkLocalRateLimit();
      return !rateLimitResult.isBlocked;
    } catch (error) {
      console.error('Error checking payment allowance:', error);
      return true; // Allow on error to avoid blocking legitimate users
    }
  }
}
