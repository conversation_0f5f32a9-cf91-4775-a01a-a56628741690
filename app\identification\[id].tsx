import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Image,
  TouchableOpacity,
  Alert,
  Share,
  Dimensions,
  ActivityIndicator,
} from 'react-native';
import { useLocalSearchParams, router } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { BlurView } from 'expo-blur';
import * as Haptics from 'expo-haptics';

import { IdentificationService, SpeciesIdentification } from '../../services/IdentificationService';
import { useAuth } from '../../components/AuthContext';

const { width } = Dimensions.get('window');

export default function IdentificationResultScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const [identification, setIdentification] = useState<SpeciesIdentification | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isFavorite, setIsFavorite] = useState(false);
  const [userNotes, setUserNotes] = useState('');
  const [showFullDescription, setShowFullDescription] = useState(false);

  const { user } = useAuth();
  const identificationService = new IdentificationService();

  useEffect(() => {
    loadIdentification();
  }, [id]);

  const loadIdentification = async () => {
    try {
      setLoading(true);
      setError(null);

      if (!id) {
        throw new Error('No identification ID provided');
      }

      const result = await identificationService.getIdentification(id);
      if (!result) {
        throw new Error('Identification not found');
      }

      setIdentification(result);
      setUserNotes(result.user_notes || '');
      
      // Check if it's in user's favorites (you'd implement this in your service)
      // setIsFavorite(result.is_favorite);

    } catch (err) {
      console.error('Error loading identification:', err);
      setError(err instanceof Error ? err.message : 'Failed to load identification');
    } finally {
      setLoading(false);
    }
  };

  const handleShare = async () => {
    if (!identification || !identification.species) return;

    try {
      const species = identification.species;
      const message = `I just identified a ${species.common_name} (${species.scientific_name}) using BioScan! 🌿\n\nConfidence: ${identification.confidence_score}%\n\n${species.description}`;

      await Share.share({
        message,
        title: `Species Identification: ${species.common_name}`,
      });

      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
    } catch (error) {
      console.error('Error sharing:', error);
    }
  };

  const handleAddToFavorites = async () => {
    try {
      // Implement favorite toggle logic
      setIsFavorite(!isFavorite);
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
      
      // You would call your service here to update the favorite status
      // await identificationService.toggleFavorite(identification.id);
    } catch (error) {
      console.error('Error toggling favorite:', error);
    }
  };

  const handleSaveNotes = async () => {
    try {
      if (!identification) return;

      await identificationService.addUserNotes(identification.id, userNotes);
      
      Alert.alert('Success', 'Notes saved successfully!');
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
    } catch (error) {
      console.error('Error saving notes:', error);
      Alert.alert('Error', 'Failed to save notes. Please try again.');
    }
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 80) return '#22C55E';
    if (confidence >= 60) return '#F59E0B';
    return '#EF4444';
  };

  const getConfidenceLabel = (confidence: number) => {
    if (confidence >= 80) return 'High Confidence';
    if (confidence >= 60) return 'Medium Confidence';
    return 'Low Confidence';
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#22C55E" />
        <Text style={styles.loadingText}>Loading identification...</Text>
      </View>
    );
  }

  if (error || !identification) {
    return (
      <View style={styles.errorContainer}>
        <Ionicons name="alert-circle" size={64} color="#EF4444" />
        <Text style={styles.errorTitle}>Error</Text>
        <Text style={styles.errorText}>{error || 'Identification not found'}</Text>
        <TouchableOpacity
          style={styles.retryButton}
          onPress={() => router.back()}
        >
          <Text style={styles.retryButtonText}>Go Back</Text>
        </TouchableOpacity>
      </View>
    );
  }

  const species = identification.species;
  const media = identification.media;

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      {/* Header Image */}
      <View style={styles.imageContainer}>
        <Image
          source={{ uri: media?.public_url || media?.thumbnail_url }}
          style={styles.image}
          resizeMode="cover"
        />
        
        {/* Header Controls */}
        <BlurView intensity={20} style={styles.headerControls}>
          <TouchableOpacity
            style={styles.headerButton}
            onPress={() => router.back()}
          >
            <Ionicons name="arrow-back" size={24} color="white" />
          </TouchableOpacity>
          
          <View style={styles.headerActions}>
            <TouchableOpacity
              style={styles.headerButton}
              onPress={handleAddToFavorites}
            >
              <Ionicons
                name={isFavorite ? "heart" : "heart-outline"}
                size={24}
                color={isFavorite ? "#EF4444" : "white"}
              />
            </TouchableOpacity>
            
            <TouchableOpacity
              style={styles.headerButton}
              onPress={handleShare}
            >
              <Ionicons name="share-outline" size={24} color="white" />
            </TouchableOpacity>
          </View>
        </BlurView>

        {/* Status Badge */}
        <View style={styles.statusBadge}>
          <Text style={styles.statusText}>
            {identification.status === 'completed' ? 'Identified' : 'Processing'}
          </Text>
        </View>
      </View>

      {/* Content */}
      <View style={styles.content}>
        {species ? (
          <>
            {/* Species Info */}
            <View style={styles.speciesHeader}>
              <Text style={styles.commonName}>{species.common_name}</Text>
              <Text style={styles.scientificName}>{species.scientific_name}</Text>
              
              {/* Confidence Score */}
              <View style={styles.confidenceContainer}>
                <View
                  style={[
                    styles.confidenceBadge,
                    { backgroundColor: getConfidenceColor(identification.confidence_score || 0) }
                  ]}
                >
                  <Text style={styles.confidenceText}>
                    {identification.confidence_score}% {getConfidenceLabel(identification.confidence_score || 0)}
                  </Text>
                </View>
              </View>
            </View>

            {/* Description */}
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Description</Text>
              <Text
                style={styles.description}
                numberOfLines={showFullDescription ? undefined : 3}
              >
                {species.description}
              </Text>
              {species.description && species.description.length > 150 && (
                <TouchableOpacity
                  onPress={() => setShowFullDescription(!showFullDescription)}
                >
                  <Text style={styles.readMoreText}>
                    {showFullDescription ? 'Read Less' : 'Read More'}
                  </Text>
                </TouchableOpacity>
              )}
            </View>

            {/* Habitat */}
            {species.habitat && (
              <View style={styles.section}>
                <Text style={styles.sectionTitle}>Habitat</Text>
                <Text style={styles.sectionText}>{species.habitat}</Text>
              </View>
            )}

            {/* Conservation Status */}
            {species.conservation_status && (
              <View style={styles.section}>
                <Text style={styles.sectionTitle}>Conservation Status</Text>
                <Text style={styles.sectionText}>{species.conservation_status}</Text>
              </View>
            )}

            {/* Interesting Facts */}
            {species.interesting_facts && species.interesting_facts.length > 0 && (
              <View style={styles.section}>
                <Text style={styles.sectionTitle}>Interesting Facts</Text>
                {species.interesting_facts.map((fact, index) => (
                  <View key={index} style={styles.factItem}>
                    <Text style={styles.factBullet}>•</Text>
                    <Text style={styles.factText}>{fact}</Text>
                  </View>
                ))}
              </View>
            )}

            {/* Alternative Suggestions */}
            {identification.alternative_suggestions && identification.alternative_suggestions.length > 0 && (
              <View style={styles.section}>
                <Text style={styles.sectionTitle}>Alternative Possibilities</Text>
                {identification.alternative_suggestions.map((alt: any, index: number) => (
                  <View key={index} style={styles.alternativeItem}>
                    <Text style={styles.alternativeName}>{alt.common_name}</Text>
                    <Text style={styles.alternativeScientific}>{alt.scientific_name}</Text>
                    <Text style={styles.alternativeConfidence}>{alt.confidence_score}%</Text>
                  </View>
                ))}
              </View>
            )}

            {/* Location */}
            {identification.location_data && (
              <View style={styles.section}>
                <Text style={styles.sectionTitle}>Location</Text>
                <Text style={styles.sectionText}>
                  {identification.location_data.address || 
                   `${identification.location_data.latitude?.toFixed(6)}, ${identification.location_data.longitude?.toFixed(6)}`}
                </Text>
              </View>
            )}

          </>
        ) : (
          <View style={styles.noResultsContainer}>
            <Ionicons name="search" size={64} color="#9CA3AF" />
            <Text style={styles.noResultsTitle}>No Match Found</Text>
            <Text style={styles.noResultsText}>
              We couldn't identify this species. Try taking another photo with better lighting or a clearer view.
            </Text>
          </View>
        )}

        {/* Action Buttons */}
        <View style={styles.actionButtons}>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => router.push('/camera')}
          >
            <Ionicons name="camera" size={20} color="white" />
            <Text style={styles.actionButtonText}>Take Another</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.actionButton, styles.secondaryButton]}
            onPress={() => router.push('/history')}
          >
            <Ionicons name="time" size={20} color="#22C55E" />
            <Text style={[styles.actionButtonText, styles.secondaryButtonText]}>
              View History
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#F9FAFB',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#6B7280',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#F9FAFB',
  },
  errorTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1F2937',
    marginTop: 16,
    marginBottom: 8,
  },
  errorText: {
    fontSize: 16,
    color: '#6B7280',
    textAlign: 'center',
    marginBottom: 24,
  },
  retryButton: {
    backgroundColor: '#22C55E',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  retryButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  imageContainer: {
    position: 'relative',
    height: 300,
  },
  image: {
    width: '100%',
    height: '100%',
  },
  headerControls: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: 60,
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  headerButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerActions: {
    flexDirection: 'row',
    gap: 12,
  },
  statusBadge: {
    position: 'absolute',
    bottom: 16,
    left: 16,
    backgroundColor: '#22C55E',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  statusText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '600',
  },
  content: {
    padding: 20,
  },
  speciesHeader: {
    marginBottom: 24,
  },
  commonName: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#1F2937',
    marginBottom: 4,
  },
  scientificName: {
    fontSize: 18,
    fontStyle: 'italic',
    color: '#6B7280',
    marginBottom: 16,
  },
  confidenceContainer: {
    alignItems: 'flex-start',
  },
  confidenceBadge: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  confidenceText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '600',
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1F2937',
    marginBottom: 8,
  },
  sectionText: {
    fontSize: 16,
    color: '#4B5563',
    lineHeight: 24,
  },
  description: {
    fontSize: 16,
    color: '#4B5563',
    lineHeight: 24,
  },
  readMoreText: {
    color: '#22C55E',
    fontSize: 14,
    fontWeight: '600',
    marginTop: 8,
  },
  factItem: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  factBullet: {
    fontSize: 16,
    color: '#22C55E',
    marginRight: 8,
    marginTop: 2,
  },
  factText: {
    flex: 1,
    fontSize: 16,
    color: '#4B5563',
    lineHeight: 24,
  },
  alternativeItem: {
    backgroundColor: 'white',
    padding: 16,
    borderRadius: 12,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  alternativeName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1F2937',
  },
  alternativeScientific: {
    fontSize: 14,
    fontStyle: 'italic',
    color: '#6B7280',
    marginTop: 2,
  },
  alternativeConfidence: {
    fontSize: 14,
    color: '#22C55E',
    fontWeight: '600',
    marginTop: 4,
  },
  noResultsContainer: {
    alignItems: 'center',
    padding: 40,
  },
  noResultsTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1F2937',
    marginTop: 16,
    marginBottom: 8,
  },
  noResultsText: {
    fontSize: 16,
    color: '#6B7280',
    textAlign: 'center',
    lineHeight: 24,
  },
  actionButtons: {
    flexDirection: 'row',
    gap: 12,
    marginTop: 24,
  },
  actionButton: {
    flex: 1,
    backgroundColor: '#22C55E',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    borderRadius: 12,
    gap: 8,
  },
  secondaryButton: {
    backgroundColor: 'white',
    borderWidth: 1,
    borderColor: '#22C55E',
  },
  actionButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  secondaryButtonText: {
    color: '#22C55E',
  },
});
