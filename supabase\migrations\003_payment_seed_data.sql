-- Seed Data for Payment System
-- This migration inserts initial subscription plans and promotional codes

-- Insert subscription plans
INSERT INTO subscription_plans (
    id,
    name,
    description,
    price_monthly,
    price_yearly,
    features,
    max_identifications_per_month,
    is_active,
    trial_period_days
) VALUES 
(
    '00000000-0000-0000-0000-000000000001',
    'Free',
    'Perfect for casual nature enthusiasts',
    0.00,
    0.00,
    '["10 scans per day", "Basic species identification", "Limited collection storage", "Community access (read-only)", "Basic AR features"]',
    10,
    true,
    0
),
(
    '00000000-0000-0000-0000-000000000002',
    'Pro',
    'For serious nature explorers',
    4.99,
    49.99,
    '["Unlimited scans", "Advanced AI identification", "Full collection management", "Community participation", "Complete AR experience", "Priority support", "Export your data", "Advanced filters"]',
    -1,
    true,
    7
),
(
    '00000000-0000-0000-0000-000000000003',
    'Expert',
    'For professional researchers and educators',
    9.99,
    99.99,
    '["Everything in Pro", "Research-grade accuracy", "Bulk identification tools", "Advanced analytics", "API access", "Custom categories", "Team collaboration", "Priority customer support", "Early access to new features"]',
    -1,
    true,
    14
)
ON CONFLICT (id) DO UPDATE SET
    name = EXCLUDED.name,
    description = EXCLUDED.description,
    price_monthly = EXCLUDED.price_monthly,
    price_yearly = EXCLUDED.price_yearly,
    features = EXCLUDED.features,
    max_identifications_per_month = EXCLUDED.max_identifications_per_month,
    is_active = EXCLUDED.is_active,
    trial_period_days = EXCLUDED.trial_period_days,
    updated_at = NOW();

-- Insert sample promotional codes
INSERT INTO promotional_codes (
    code,
    discount_type,
    discount_value,
    max_redemptions,
    valid_from,
    valid_until,
    is_active
) VALUES 
(
    'WELCOME20',
    'percentage',
    20.00,
    1000,
    NOW(),
    NOW() + INTERVAL '3 months',
    true
),
(
    'STUDENT50',
    'percentage',
    50.00,
    500,
    NOW(),
    NOW() + INTERVAL '1 year',
    true
),
(
    'EARLYBIRD',
    'percentage',
    30.00,
    100,
    NOW(),
    NOW() + INTERVAL '1 month',
    true
)
ON CONFLICT (code) DO UPDATE SET
    discount_type = EXCLUDED.discount_type,
    discount_value = EXCLUDED.discount_value,
    max_redemptions = EXCLUDED.max_redemptions,
    valid_from = EXCLUDED.valid_from,
    valid_until = EXCLUDED.valid_until,
    is_active = EXCLUDED.is_active,
    updated_at = NOW();

-- Create view for subscription analytics dashboard
CREATE OR REPLACE VIEW subscription_metrics AS
SELECT 
    sp.name as plan_name,
    COUNT(CASE WHEN us.status = 'active' THEN 1 END) as active_subscriptions,
    COUNT(CASE WHEN us.status = 'trialing' THEN 1 END) as trial_subscriptions,
    COUNT(CASE WHEN us.status = 'canceled' THEN 1 END) as canceled_subscriptions,
    COUNT(CASE WHEN us.status = 'past_due' THEN 1 END) as past_due_subscriptions,
    SUM(CASE 
        WHEN us.status = 'active' AND us.current_period_end > us.current_period_start + INTERVAL '25 days' 
        THEN sp.price_yearly / 12 
        ELSE sp.price_monthly 
    END) as mrr,
    AVG(EXTRACT(EPOCH FROM (us.current_period_end - us.current_period_start)) / 86400) as avg_subscription_length_days
FROM subscription_plans sp
LEFT JOIN user_subscriptions us ON sp.id = us.plan_id
WHERE sp.is_active = true
GROUP BY sp.id, sp.name, sp.price_monthly, sp.price_yearly;

-- Create view for user subscription details
CREATE OR REPLACE VIEW user_subscription_details AS
SELECT 
    us.user_id,
    us.id as subscription_id,
    sp.name as plan_name,
    sp.description as plan_description,
    us.status,
    us.current_period_start,
    us.current_period_end,
    us.trial_start,
    us.trial_end,
    us.cancel_at_period_end,
    sp.max_identifications_per_month,
    sp.features,
    CASE 
        WHEN us.current_period_end > us.current_period_start + INTERVAL '25 days' 
        THEN 'yearly' 
        ELSE 'monthly' 
    END as billing_period,
    CASE 
        WHEN us.current_period_end > us.current_period_start + INTERVAL '25 days' 
        THEN sp.price_yearly 
        ELSE sp.price_monthly 
    END as current_price
FROM user_subscriptions us
JOIN subscription_plans sp ON us.plan_id = sp.id;

-- Create function to track usage
CREATE OR REPLACE FUNCTION track_feature_usage(
    user_uuid UUID,
    feature_name TEXT,
    usage_amount INTEGER DEFAULT 1
)
RETURNS BOOLEAN AS $$
DECLARE
    subscription_record RECORD;
    current_period_start TIMESTAMP WITH TIME ZONE;
    current_period_end TIMESTAMP WITH TIME ZONE;
BEGIN
    -- Get user's current subscription period
    SELECT us.current_period_start, us.current_period_end
    INTO current_period_start, current_period_end
    FROM user_subscriptions us
    WHERE us.user_id = user_uuid
    AND us.status IN ('active', 'trialing')
    ORDER BY us.created_at DESC
    LIMIT 1;
    
    -- If no subscription, use monthly period starting from today
    IF current_period_start IS NULL THEN
        current_period_start := date_trunc('month', NOW());
        current_period_end := current_period_start + INTERVAL '1 month';
    END IF;
    
    -- Insert or update usage tracking
    INSERT INTO usage_tracking (
        user_id,
        feature_type,
        usage_count,
        period_start,
        period_end
    ) VALUES (
        user_uuid,
        feature_name,
        usage_amount,
        current_period_start,
        current_period_end
    )
    ON CONFLICT (user_id, feature_type, period_start, period_end) 
    DO UPDATE SET 
        usage_count = usage_tracking.usage_count + usage_amount,
        created_at = NOW();
    
    RETURN true;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to get user usage stats
CREATE OR REPLACE FUNCTION get_user_usage_stats(user_uuid UUID)
RETURNS TABLE (
    feature_type TEXT,
    current_usage INTEGER,
    usage_limit INTEGER,
    usage_percentage DECIMAL,
    period_start TIMESTAMP WITH TIME ZONE,
    period_end TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    WITH user_sub AS (
        SELECT us.current_period_start, us.current_period_end, sp.max_identifications_per_month
        FROM user_subscriptions us
        JOIN subscription_plans sp ON us.plan_id = sp.id
        WHERE us.user_id = user_uuid
        AND us.status IN ('active', 'trialing')
        ORDER BY us.created_at DESC
        LIMIT 1
    )
    SELECT 
        ut.feature_type,
        COALESCE(ut.usage_count, 0) as current_usage,
        COALESCE(us.max_identifications_per_month, 10) as usage_limit,
        CASE 
            WHEN us.max_identifications_per_month = -1 THEN 0.0
            ELSE ROUND((COALESCE(ut.usage_count, 0)::DECIMAL / NULLIF(us.max_identifications_per_month, 0)) * 100, 2)
        END as usage_percentage,
        COALESCE(us.current_period_start, date_trunc('month', NOW())) as period_start,
        COALESCE(us.current_period_end, date_trunc('month', NOW()) + INTERVAL '1 month') as period_end
    FROM user_sub us
    LEFT JOIN usage_tracking ut ON ut.user_id = user_uuid
        AND ut.period_start <= NOW()
        AND ut.period_end >= NOW();
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
