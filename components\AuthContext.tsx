import * as LocalAuthentication from 'expo-local-authentication';
import * as SecureStore from 'expo-secure-store';
import React, { createContext, ReactNode, useContext, useEffect, useState } from 'react';
import { Alert } from 'react-native';
import { FirebaseUser } from '../lib/firebase';

interface AuthContextType {
  user: FirebaseUser | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  login: (email: string, password: string) => Promise<void>;
  register: (email: string, password: string, displayName?: string) => Promise<void>;
  logout: () => Promise<void>;
  sendPasswordReset: (email: string) => Promise<void>;
  updatePassword: (currentPassword: string, newPassword: string) => Promise<void>;
  updateProfile: (updates: { displayName?: string; photoURL?: string }) => Promise<void>;
  deleteAccount: (password: string) => Promise<void>;
  enableBiometrics: () => Promise<void>;
  disableBiometrics: () => Promise<void>;
  isBiometricsEnabled: boolean;
  biometricsAvailable: boolean;
  migrationStatus: {
    completed: boolean;
    migrationDate?: string;
  };
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<AuthUser | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isBiometricsEnabled, setIsBiometricsEnabled] = useState(false);
  const [biometricsAvailable, setBiometricsAvailable] = useState(false);

  const authService = new AuthService();

  useEffect(() => {
    initializeAuth();
    checkBiometricsAvailability();
    setupAuthListener();
  }, []);

  const setupAuthListener = () => {
    // Listen for auth state changes
    const { data: authListener } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        console.log('Auth state changed:', event, session?.user?.id);

        if (event === 'SIGNED_IN' && session) {
          const profile = await authService.getUserProfile(session.user.id);
          const authUser: AuthUser = {
            ...session.user,
            profile,
          };
          setUser(authUser);
          setSession(session);
        } else if (event === 'SIGNED_OUT') {
          setUser(null);
          setSession(null);
        } else if (event === 'TOKEN_REFRESHED' && session) {
          setSession(session);
        }

        setIsLoading(false);
      }
    );

    return () => {
      authListener.subscription.unsubscribe();
    };
  };

  const initializeAuth = async () => {
    try {
      setIsLoading(true);

      // Get current session from Supabase
      const { data: { session }, error } = await supabase.auth.getSession();

      if (error) {
        console.error('Session error:', error);
        return;
      }

      if (session) {
        const profile = await authService.getUserProfile(session.user.id);
        const authUser: AuthUser = {
          ...session.user,
          profile,
        };
        setUser(authUser);
        setSession(session);
      }

      // Check biometrics settings
      const biometricsEnabled = await SecureStore.getItemAsync('biometricsEnabled');
      setIsBiometricsEnabled(biometricsEnabled === 'true');
    } catch (error) {
      console.error('Auth initialization error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const checkBiometricsAvailability = async () => {
    try {
      const hasHardware = await LocalAuthentication.hasHardwareAsync();
      const isEnrolled = await LocalAuthentication.isEnrolledAsync();
      setBiometricsAvailable(hasHardware && isEnrolled);
    } catch (error) {
      console.error('Biometrics check error:', error);
      setBiometricsAvailable(false);
    }
  };

  const login = async (credentials: LoginCredentials) => {
    try {
      setIsLoading(true);
      const response = await authService.login(credentials);

      setUser(response.user);
      setSession(response.session);

      // If biometrics is enabled, store credentials for future biometric login
      if (isBiometricsEnabled) {
        await SecureStore.setItemAsync('biometricCredentials', JSON.stringify(credentials));
      }
    } catch (error) {
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const register = async (credentials: RegisterCredentials) => {
    try {
      setIsLoading(true);
      await authService.register(credentials);
      // Registration successful, user needs to verify email
    } catch (error) {
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const logout = async () => {
    try {
      setIsLoading(true);
      await authService.logout();

      // Clear stored credentials
      await SecureStore.deleteItemAsync('biometricCredentials');

      setUser(null);
      setSession(null);
    } catch (error) {
      console.error('Logout error:', error);
      // Clear local data even if server logout fails
      await SecureStore.deleteItemAsync('biometricCredentials');
      setUser(null);
      setSession(null);
    } finally {
      setIsLoading(false);
    }
  };

  const refreshToken = async () => {
    try {
      const newSession = await authService.refreshToken();
      setSession(newSession);
    } catch (error) {
      // Refresh failed, logout user
      await logout();
      throw error;
    }
  };

  const loginWithGoogle = async () => {
    try {
      setIsLoading(true);
      const response = await authService.loginWithGoogle();

      setUser(response.user);
      setSession(response.session);
    } catch (error) {
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const loginWithApple = async () => {
    try {
      setIsLoading(true);
      const response = await authService.loginWithApple();

      setUser(response.user);
      setSession(response.session);
    } catch (error) {
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const enableBiometrics = async () => {
    try {
      if (!biometricsAvailable) {
        throw new Error('Biometric authentication is not available on this device');
      }

      // Authenticate with biometrics to confirm
      const result = await LocalAuthentication.authenticateAsync({
        promptMessage: 'Enable biometric authentication for BioScan',
        fallbackLabel: 'Use passcode',
        cancelLabel: 'Cancel'
      });

      if (result.success) {
        await SecureStore.setItemAsync('biometricsEnabled', 'true');
        setIsBiometricsEnabled(true);

        Alert.alert(
          'Biometric Authentication Enabled',
          'You can now use biometric authentication to sign in to BioScan.'
        );
      } else {
        throw new Error('Biometric authentication failed');
      }
    } catch (error) {
      console.error('Enable biometrics error:', error);
      throw error;
    }
  };

  const disableBiometrics = async () => {
    try {
      await SecureStore.deleteItemAsync('biometricsEnabled');
      await SecureStore.deleteItemAsync('biometricCredentials');
      setIsBiometricsEnabled(false);

      Alert.alert(
        'Biometric Authentication Disabled',
        'Biometric authentication has been disabled for BioScan.'
      );
    } catch (error) {
      console.error('Disable biometrics error:', error);
      throw error;
    }
  };

  const authenticateWithBiometrics = async (): Promise<boolean> => {
    try {
      if (!biometricsAvailable || !isBiometricsEnabled) {
        return false;
      }

      const result = await LocalAuthentication.authenticateAsync({
        promptMessage: 'Sign in to BioScan',
        fallbackLabel: 'Use password',
        cancelLabel: 'Cancel'
      });

      if (result.success) {
        // Get stored credentials and login
        const storedCredentials = await SecureStore.getItemAsync('biometricCredentials');
        if (storedCredentials) {
          const credentials = JSON.parse(storedCredentials);
          await login(credentials.email, credentials.password);
          return true;
        }
      }

      return false;
    } catch (error) {
      console.error('Biometric authentication error:', error);
      return false;
    }
  };

  const value: AuthContextType = {
    user,
    isLoading,
    isAuthenticated: !!user,
    login,
    register,
    logout,
    sendPasswordReset,
    updatePassword,
    updateProfile,
    deleteAccount,
    enableBiometrics,
    disableBiometrics,
    isBiometricsEnabled,
    biometricsAvailable,
    migrationStatus
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
