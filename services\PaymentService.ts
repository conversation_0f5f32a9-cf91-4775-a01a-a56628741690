import { supabase } from '../lib/supabase';
import { Alert } from 'react-native';
import { FraudDetectionService, FraudDetectionResult } from './FraudDetectionService';

export interface SubscriptionPlan {
  id: string;
  name: string;
  description: string;
  price_monthly: number;
  price_yearly: number;
  features: string[];
  max_identifications_per_month: number;
  is_active: boolean;
  stripe_price_id_monthly?: string;
  stripe_price_id_yearly?: string;
}

export interface UserSubscription {
  id: string;
  user_id: string;
  plan_id: string;
  stripe_subscription_id: string;
  status: 'active' | 'canceled' | 'past_due' | 'unpaid';
  current_period_start: string;
  current_period_end: string;
  cancel_at_period_end: boolean;
  created_at: string;
  updated_at: string;
  plan?: SubscriptionPlan;
}

export interface PaymentMethod {
  id: string;
  type: 'card';
  card: {
    brand: string;
    last4: string;
    exp_month: number;
    exp_year: number;
  };
  is_default: boolean;
}

export interface UsageStats {
  current_period_identifications: number;
  total_identifications: number;
  remaining_identifications: number;
  usage_percentage: number;
  period_start: string;
  period_end: string;
}

export class PaymentService {
  private readonly STRIPE_PUBLISHABLE_KEY = process.env.EXPO_PUBLIC_STRIPE_PUBLISHABLE_KEY;

  constructor() {
    if (!this.STRIPE_PUBLISHABLE_KEY) {
      console.warn('Stripe publishable key not configured');
    }
  }

  /**
   * Get available subscription plans
   */
  async getSubscriptionPlans(): Promise<SubscriptionPlan[]> {
    try {
      const { data, error } = await supabase
        .from('subscription_plans')
        .select('*')
        .eq('is_active', true)
        .order('price_monthly', { ascending: true });

      if (error) {
        throw error;
      }

      return data || [];
    } catch (error) {
      console.error('Error fetching subscription plans:', error);
      throw error;
    }
  }

  /**
   * Get user's current subscription
   */
  async getCurrentSubscription(): Promise<UserSubscription | null> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        throw new Error('User not authenticated');
      }

      const { data, error } = await supabase
        .from('user_subscriptions')
        .select(`
          *,
          plan:plan_id (*)
        `)
        .eq('user_id', user.id)
        .eq('status', 'active')
        .single();

      if (error && error.code !== 'PGRST116') { // PGRST116 is "not found"
        throw error;
      }

      return data;
    } catch (error) {
      console.error('Error fetching current subscription:', error);
      return null;
    }
  }

  /**
   * Get user's usage statistics
   */
  async getUsageStats(): Promise<UsageStats> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        throw new Error('User not authenticated');
      }

      const subscription = await this.getCurrentSubscription();
      
      // Get current period dates
      const now = new Date();
      const periodStart = subscription?.current_period_start 
        ? new Date(subscription.current_period_start)
        : new Date(now.getFullYear(), now.getMonth(), 1);
      const periodEnd = subscription?.current_period_end
        ? new Date(subscription.current_period_end)
        : new Date(now.getFullYear(), now.getMonth() + 1, 0);

      // Count identifications in current period
      const { count: currentPeriodCount } = await supabase
        .from('species_identifications')
        .select('*', { count: 'exact', head: true })
        .eq('user_id', user.id)
        .gte('created_at', periodStart.toISOString())
        .lte('created_at', periodEnd.toISOString());

      // Count total identifications
      const { count: totalCount } = await supabase
        .from('species_identifications')
        .select('*', { count: 'exact', head: true })
        .eq('user_id', user.id);

      const maxIdentifications = subscription?.plan?.max_identifications_per_month || 150; // Free tier limit
      const currentPeriodIdentifications = currentPeriodCount || 0;
      const totalIdentifications = totalCount || 0;
      const remainingIdentifications = Math.max(0, maxIdentifications - currentPeriodIdentifications);
      const usagePercentage = maxIdentifications > 0 
        ? Math.min(100, (currentPeriodIdentifications / maxIdentifications) * 100)
        : 0;

      return {
        current_period_identifications: currentPeriodIdentifications,
        total_identifications: totalIdentifications,
        remaining_identifications: remainingIdentifications,
        usage_percentage: Math.round(usagePercentage),
        period_start: periodStart.toISOString(),
        period_end: periodEnd.toISOString(),
      };
    } catch (error) {
      console.error('Error fetching usage stats:', error);
      throw error;
    }
  }

  /**
   * Create Stripe checkout session with fraud detection
   */
  async createCheckoutSession(
    planId: string,
    billingPeriod: 'monthly' | 'yearly',
    promoCode?: string
  ): Promise<{ url: string }> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        throw new Error('User not authenticated');
      }

      // Check if payment is allowed
      const isAllowed = await FraudDetectionService.isPaymentAllowed();
      if (!isAllowed) {
        throw new Error('Payment temporarily blocked. Please try again later.');
      }

      // Call Supabase Edge Function to create checkout session
      const { data, error } = await supabase.functions.invoke('create-checkout-session', {
        body: {
          planId,
          billingPeriod,
          userId: user.id,
          userEmail: user.email,
          promoCode,
        },
      });

      if (error) {
        throw error;
      }

      return data;
    } catch (error) {
      console.error('Error creating checkout session:', error);
      throw error;
    }
  }

  /**
   * Validate payment method before processing
   */
  async validatePaymentMethod(
    paymentMethodId: string,
    amount: number,
    currency: string = 'USD'
  ): Promise<FraudDetectionResult> {
    try {
      return await FraudDetectionService.validatePayment({
        paymentMethodId,
        amount,
        currency
      });
    } catch (error) {
      console.error('Error validating payment method:', error);
      return {
        isBlocked: false,
        riskScore: 50,
        riskFactors: ['validation_error'],
        requiresVerification: true,
        message: 'Unable to validate payment method'
      };
    }
  }

  /**
   * Create Stripe customer portal session
   */
  async createCustomerPortalSession(): Promise<{ url: string }> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        throw new Error('User not authenticated');
      }

      // Call Supabase Edge Function to create customer portal session
      const { data, error } = await supabase.functions.invoke('create-customer-portal-session', {
        body: {
          userId: user.id,
        },
      });

      if (error) {
        throw error;
      }

      return data;
    } catch (error) {
      console.error('Error creating customer portal session:', error);
      throw error;
    }
  }

  /**
   * Cancel subscription
   */
  async cancelSubscription(): Promise<void> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        throw new Error('User not authenticated');
      }

      const subscription = await this.getCurrentSubscription();
      if (!subscription) {
        throw new Error('No active subscription found');
      }

      // Call Supabase Edge Function to cancel subscription
      const { error } = await supabase.functions.invoke('cancel-subscription', {
        body: {
          subscriptionId: subscription.stripe_subscription_id,
        },
      });

      if (error) {
        throw error;
      }

      Alert.alert(
        'Subscription Cancelled',
        'Your subscription has been cancelled. You can continue using premium features until the end of your current billing period.',
        [{ text: 'OK' }]
      );
    } catch (error) {
      console.error('Error cancelling subscription:', error);
      throw error;
    }
  }

  /**
   * Reactivate subscription
   */
  async reactivateSubscription(): Promise<void> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        throw new Error('User not authenticated');
      }

      const subscription = await this.getCurrentSubscription();
      if (!subscription) {
        throw new Error('No subscription found');
      }

      // Call Supabase Edge Function to reactivate subscription
      const { error } = await supabase.functions.invoke('reactivate-subscription', {
        body: {
          subscriptionId: subscription.stripe_subscription_id,
        },
      });

      if (error) {
        throw error;
      }

      Alert.alert(
        'Subscription Reactivated',
        'Your subscription has been reactivated and will continue at the end of your current billing period.',
        [{ text: 'OK' }]
      );
    } catch (error) {
      console.error('Error reactivating subscription:', error);
      throw error;
    }
  }

  /**
   * Check if user has access to premium features
   */
  async hasPremiumAccess(): Promise<boolean> {
    try {
      const subscription = await this.getCurrentSubscription();
      return subscription?.status === 'active' && subscription?.plan?.name !== 'Free';
    } catch (error) {
      console.error('Error checking premium access:', error);
      return false;
    }
  }

  /**
   * Check if user can perform more identifications
   */
  async canPerformIdentification(): Promise<{ allowed: boolean; reason?: string }> {
    try {
      const usageStats = await this.getUsageStats();
      
      if (usageStats.remaining_identifications > 0) {
        return { allowed: true };
      }

      const hasPremium = await this.hasPremiumAccess();
      if (hasPremium) {
        return { allowed: true };
      }

      return {
        allowed: false,
        reason: 'You have reached your monthly identification limit. Upgrade to Premium for unlimited identifications.',
      };
    } catch (error) {
      console.error('Error checking identification permission:', error);
      return {
        allowed: false,
        reason: 'Unable to verify identification limits. Please try again.',
      };
    }
  }

  /**
   * Get subscription history
   */
  async getSubscriptionHistory(): Promise<UserSubscription[]> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        throw new Error('User not authenticated');
      }

      const { data, error } = await supabase
        .from('user_subscriptions')
        .select(`
          *,
          plan:plan_id (*)
        `)
        .eq('user_id', user.id)
        .order('created_at', { ascending: false });

      if (error) {
        throw error;
      }

      return data || [];
    } catch (error) {
      console.error('Error fetching subscription history:', error);
      throw error;
    }
  }

  /**
   * Get fraud detection statistics for current user
   */
  async getFraudDetectionStats(): Promise<{
    deviceFingerprint: string;
    recentAttempts: number;
    lastValidation?: { timestamp: number; riskScore: number };
  }> {
    return await FraudDetectionService.getFraudStats();
  }

  /**
   * Clear fraud detection data (for testing or privacy)
   */
  async clearFraudDetectionData(): Promise<void> {
    await FraudDetectionService.clearFraudData();
  }

  /**
   * Get payment security notifications
   */
  async getSecurityNotifications(): Promise<any[]> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        throw new Error('User not authenticated');
      }

      const { data, error } = await supabase
        .from('notifications')
        .select('*')
        .eq('user_id', user.id)
        .eq('type', 'security')
        .order('created_at', { ascending: false })
        .limit(10);

      if (error) {
        throw error;
      }

      return data || [];
    } catch (error) {
      console.error('Error fetching security notifications:', error);
      return [];
    }
  }

  /**
   * Report suspicious activity
   */
  async reportSuspiciousActivity(description: string, metadata?: any): Promise<void> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        throw new Error('User not authenticated');
      }

      await supabase
        .from('fraud_detection_logs')
        .insert({
          user_id: user.id,
          risk_factors: {
            user_reported: true,
            description,
            metadata: metadata || {}
          },
          risk_score: 50,
          action_taken: 'review',
          notes: `User reported suspicious activity: ${description}`
        });

    } catch (error) {
      console.error('Error reporting suspicious activity:', error);
      throw error;
    }
  }

  /**
   * Validate subscription status
   */
  async validateSubscriptionStatus(): Promise<void> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return;

      // Call Supabase Edge Function to sync subscription status with Stripe
      await supabase.functions.invoke('sync-subscription-status', {
        body: { userId: user.id },
      });
    } catch (error) {
      console.error('Error validating subscription status:', error);
    }
  }

  /**
   * Format price for display
   */
  formatPrice(amount: number, currency: string = 'USD'): string {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency,
    }).format(amount);
  }

  /**
   * Calculate savings for yearly billing
   */
  calculateYearlySavings(monthlyPrice: number, yearlyPrice: number): {
    amount: number;
    percentage: number;
  } {
    const yearlyEquivalent = monthlyPrice * 12;
    const savings = yearlyEquivalent - yearlyPrice;
    const percentage = Math.round((savings / yearlyEquivalent) * 100);

    return {
      amount: savings,
      percentage,
    };
  }
}
