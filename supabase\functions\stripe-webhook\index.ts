import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import Stripe from 'https://esm.sh/stripe@12.0.0?target=deno'

const stripe = new Stripe(Deno.env.get('STRIPE_SECRET_KEY') || '', {
  apiVersion: '2023-10-16',
})

const supabaseClient = createClient(
  Deno.env.get('SUPABASE_URL') ?? '',
  Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
)

const cryptoProvider = Stripe.createSubtleCryptoProvider()

// Fraud detection configuration
const FRAUD_DETECTION_CONFIG = {
  HIGH_RISK_THRESHOLD: 70,
  MEDIUM_RISK_THRESHOLD: 40,
  MAX_FAILED_ATTEMPTS: 3,
  SUSPICIOUS_COUNTRIES: ['CN', 'RU', 'NG'], // Add more as needed
  VELOCITY_LIMITS: {
    MAX_TRANSACTIONS_PER_HOUR: 10,
    MAX_AMOUNT_PER_HOUR: 1000
  }
}

serve(async (req) => {
  const signature = req.headers.get('Stripe-Signature')
  const body = await req.text()
  const clientIP = req.headers.get('CF-Connecting-IP') || req.headers.get('X-Forwarded-For') || 'unknown'

  let receivedEvent
  try {
    receivedEvent = await stripe.webhooks.constructEventAsync(
      body,
      signature!,
      Deno.env.get('STRIPE_WEBHOOK_SECRET')!,
      undefined,
      cryptoProvider
    )
  } catch (err: any) {
    console.error('Webhook signature verification failed:', err.message)
    await logSecurityEvent('webhook_verification_failed', {
      error: err.message,
      ip: clientIP,
      signature: signature?.substring(0, 20) + '...'
    })
    return new Response(`Webhook Error: ${err.message}`, { status: 400 })
  }

  console.log(`🔔 Webhook received: ${receivedEvent.type}`)

  try {
    switch (receivedEvent.type) {
      case 'checkout.session.completed':
        await handleCheckoutSessionCompleted(receivedEvent.data.object)
        break

      case 'customer.subscription.created':
        await handleSubscriptionCreated(receivedEvent.data.object)
        break

      case 'customer.subscription.updated':
        await handleSubscriptionUpdated(receivedEvent.data.object)
        break

      case 'customer.subscription.deleted':
        await handleSubscriptionDeleted(receivedEvent.data.object)
        break

      case 'invoice.payment_succeeded':
        await handleInvoicePaymentSucceeded(receivedEvent.data.object)
        break

      case 'invoice.payment_failed':
        await handleInvoicePaymentFailed(receivedEvent.data.object)
        break

      case 'payment_intent.succeeded':
        await handlePaymentIntentSucceeded(receivedEvent.data.object)
        break

      case 'payment_intent.payment_failed':
        await handlePaymentIntentFailed(receivedEvent.data.object)
        break

      case 'radar.early_fraud_warning.created':
        await handleFraudWarning(receivedEvent.data.object)
        break

      default:
        console.log(`Unhandled event type: ${receivedEvent.type}`)
    }
  } catch (error) {
    console.error('Error processing webhook:', error)
    await logSecurityEvent('webhook_processing_error', {
      error: error.message,
      event_type: receivedEvent.type,
      event_id: receivedEvent.id
    })
    return new Response(`Webhook Error: ${error.message}`, { status: 400 })
  }

  return new Response(JSON.stringify({ received: true }), {
    headers: { 'Content-Type': 'application/json' },
    status: 200,
  })
})

async function handleCheckoutSessionCompleted(session: any) {
  console.log('Processing checkout session completed:', session.id)
  
  const userId = session.metadata?.user_id
  const planId = session.metadata?.plan_id
  
  if (!userId || !planId) {
    console.error('Missing metadata in checkout session')
    return
  }

  // Get the subscription
  const subscription = await stripe.subscriptions.retrieve(session.subscription)
  
  // Create or update subscription record
  await upsertSubscription(subscription, userId, planId)
  
  // Send welcome notification
  await supabaseClient
    .from('notifications')
    .insert({
      user_id: userId,
      type: 'system',
      title: 'Welcome to Premium!',
      message: 'Your subscription is now active. Enjoy unlimited species identifications!',
      data: { subscription_id: subscription.id }
    })
}

async function handleSubscriptionCreated(subscription: any) {
  console.log('Processing subscription created:', subscription.id)
  
  const userId = subscription.metadata?.user_id
  const planId = subscription.metadata?.plan_id
  
  if (!userId || !planId) {
    console.error('Missing metadata in subscription')
    return
  }
  
  await upsertSubscription(subscription, userId, planId)
}

async function handleSubscriptionUpdated(subscription: any) {
  console.log('Processing subscription updated:', subscription.id)
  
  // Update subscription status
  const { error } = await supabaseClient
    .from('user_subscriptions')
    .update({
      status: subscription.status,
      current_period_start: new Date(subscription.current_period_start * 1000).toISOString(),
      current_period_end: new Date(subscription.current_period_end * 1000).toISOString(),
      cancel_at_period_end: subscription.cancel_at_period_end,
      updated_at: new Date().toISOString(),
    })
    .eq('stripe_subscription_id', subscription.id)

  if (error) {
    console.error('Error updating subscription:', error)
  }
}

async function handleSubscriptionDeleted(subscription: any) {
  console.log('Processing subscription deleted:', subscription.id)
  
  // Update subscription status to canceled
  const { error } = await supabaseClient
    .from('user_subscriptions')
    .update({
      status: 'canceled',
      updated_at: new Date().toISOString(),
    })
    .eq('stripe_subscription_id', subscription.id)

  if (error) {
    console.error('Error updating subscription status:', error)
  }

  // Get user ID for notification
  const { data: userSub } = await supabaseClient
    .from('user_subscriptions')
    .select('user_id')
    .eq('stripe_subscription_id', subscription.id)
    .single()

  if (userSub) {
    // Send cancellation notification
    await supabaseClient
      .from('notifications')
      .insert({
        user_id: userSub.user_id,
        type: 'system',
        title: 'Subscription Cancelled',
        message: 'Your subscription has been cancelled. You can reactivate it anytime.',
        data: { subscription_id: subscription.id }
      })
  }
}

async function handleInvoicePaymentSucceeded(invoice: any) {
  console.log('Processing invoice payment succeeded:', invoice.id)
  
  // Update subscription status if needed
  if (invoice.subscription) {
    const subscription = await stripe.subscriptions.retrieve(invoice.subscription)
    await handleSubscriptionUpdated(subscription)
  }
}

async function handleInvoicePaymentFailed(invoice: any) {
  console.log('Processing invoice payment failed:', invoice.id)
  
  // Update subscription status
  if (invoice.subscription) {
    const { error } = await supabaseClient
      .from('user_subscriptions')
      .update({
        status: 'past_due',
        updated_at: new Date().toISOString(),
      })
      .eq('stripe_subscription_id', invoice.subscription)

    if (error) {
      console.error('Error updating subscription status:', error)
    }

    // Get user ID for notification
    const { data: userSub } = await supabaseClient
      .from('user_subscriptions')
      .select('user_id')
      .eq('stripe_subscription_id', invoice.subscription)
      .single()

    if (userSub) {
      // Send payment failed notification
      await supabaseClient
        .from('notifications')
        .insert({
          user_id: userSub.user_id,
          type: 'system',
          title: 'Payment Failed',
          message: 'Your payment failed. Please update your payment method to continue your subscription.',
          data: { invoice_id: invoice.id }
        })
    }
  }
}

async function upsertSubscription(subscription: any, userId: string, planId: string) {
  const subscriptionData = {
    user_id: userId,
    plan_id: planId,
    stripe_subscription_id: subscription.id,
    status: subscription.status,
    current_period_start: new Date(subscription.current_period_start * 1000).toISOString(),
    current_period_end: new Date(subscription.current_period_end * 1000).toISOString(),
    trial_start: subscription.trial_start ? new Date(subscription.trial_start * 1000).toISOString() : null,
    trial_end: subscription.trial_end ? new Date(subscription.trial_end * 1000).toISOString() : null,
    cancel_at_period_end: subscription.cancel_at_period_end,
    canceled_at: subscription.canceled_at ? new Date(subscription.canceled_at * 1000).toISOString() : null,
    updated_at: new Date().toISOString(),
  }

  // Try to update existing subscription first
  const { data: existing } = await supabaseClient
    .from('user_subscriptions')
    .select('id')
    .eq('stripe_subscription_id', subscription.id)
    .single()

  if (existing) {
    // Update existing subscription
    const { error } = await supabaseClient
      .from('user_subscriptions')
      .update(subscriptionData)
      .eq('id', existing.id)

    if (error) {
      console.error('Error updating subscription:', error)
    }
  } else {
    // Create new subscription
    const { error } = await supabaseClient
      .from('user_subscriptions')
      .insert(subscriptionData)

    if (error) {
      console.error('Error creating subscription:', error)
    }
  }
}

// Security and fraud detection functions
async function logSecurityEvent(eventType: string, data: any) {
  try {
    await supabaseClient
      .from('fraud_detection_logs')
      .insert({
        risk_factors: data,
        action_taken: 'review',
        notes: `Security event: ${eventType}`,
        created_at: new Date().toISOString()
      })
  } catch (error) {
    console.error('Error logging security event:', error)
  }
}

async function handlePaymentIntentSucceeded(paymentIntent: any) {
  console.log('Processing payment intent succeeded:', paymentIntent.id)

  try {
    // Get customer and user info
    const customer = await stripe.customers.retrieve(paymentIntent.customer)
    const userId = (customer as any).metadata?.supabase_user_id

    if (!userId) {
      console.error('No user ID found in customer metadata')
      return
    }

    // Perform fraud detection
    const riskScore = await calculateRiskScore(paymentIntent, userId)

    // Log transaction
    await supabaseClient
      .from('payment_transactions')
      .insert({
        user_id: userId,
        stripe_payment_intent_id: paymentIntent.id,
        amount: paymentIntent.amount / 100, // Convert from cents
        currency: paymentIntent.currency,
        status: 'succeeded',
        risk_score: riskScore,
        created_at: new Date().toISOString()
      })

    // Log fraud detection if high risk
    if (riskScore > FRAUD_DETECTION_CONFIG.HIGH_RISK_THRESHOLD) {
      await logFraudDetection(userId, paymentIntent, riskScore, 'review')
    }

  } catch (error) {
    console.error('Error handling payment intent succeeded:', error)
  }
}

async function handlePaymentIntentFailed(paymentIntent: any) {
  console.log('Processing payment intent failed:', paymentIntent.id)

  try {
    const customer = await stripe.customers.retrieve(paymentIntent.customer)
    const userId = (customer as any).metadata?.supabase_user_id

    if (!userId) {
      console.error('No user ID found in customer metadata')
      return
    }

    // Log failed transaction
    await supabaseClient
      .from('payment_transactions')
      .insert({
        user_id: userId,
        stripe_payment_intent_id: paymentIntent.id,
        amount: paymentIntent.amount / 100,
        currency: paymentIntent.currency,
        status: 'failed',
        failure_code: paymentIntent.last_payment_error?.code,
        failure_message: paymentIntent.last_payment_error?.message,
        created_at: new Date().toISOString()
      })

    // Check for suspicious failed payment patterns
    await checkFailedPaymentPatterns(userId)

  } catch (error) {
    console.error('Error handling payment intent failed:', error)
  }
}

async function handleFraudWarning(fraudWarning: any) {
  console.log('Processing fraud warning:', fraudWarning.id)

  try {
    const charge = await stripe.charges.retrieve(fraudWarning.charge)
    const customer = await stripe.customers.retrieve(charge.customer)
    const userId = (customer as any).metadata?.supabase_user_id

    if (userId) {
      await logFraudDetection(userId, fraudWarning, 100, 'block')

      // Send notification to user and admin
      await supabaseClient
        .from('notifications')
        .insert([
          {
            user_id: userId,
            type: 'security',
            title: 'Payment Security Alert',
            message: 'We detected suspicious activity on your payment. Please contact support.',
            data: { fraud_warning_id: fraudWarning.id }
          }
        ])
    }
  } catch (error) {
    console.error('Error handling fraud warning:', error)
  }
}

// Fraud detection helper functions
async function calculateRiskScore(paymentIntent: any, userId: string): Promise<number> {
  let riskScore = 0

  try {
    // Check payment amount (high amounts are riskier)
    const amount = paymentIntent.amount / 100
    if (amount > 100) riskScore += 20
    if (amount > 500) riskScore += 30

    // Check payment method country
    const paymentMethod = await stripe.paymentMethods.retrieve(paymentIntent.payment_method)
    const country = paymentMethod.card?.country
    if (country && FRAUD_DETECTION_CONFIG.SUSPICIOUS_COUNTRIES.includes(country)) {
      riskScore += 40
    }

    // Check user's payment history
    const { data: recentTransactions } = await supabaseClient
      .from('payment_transactions')
      .select('*')
      .eq('user_id', userId)
      .gte('created_at', new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()) // Last 24 hours

    if (recentTransactions && recentTransactions.length > 5) {
      riskScore += 25
    }

    // Check for failed payments in the last hour
    const { data: failedPayments } = await supabaseClient
      .from('payment_transactions')
      .select('*')
      .eq('user_id', userId)
      .eq('status', 'failed')
      .gte('created_at', new Date(Date.now() - 60 * 60 * 1000).toISOString()) // Last hour

    if (failedPayments && failedPayments.length >= FRAUD_DETECTION_CONFIG.MAX_FAILED_ATTEMPTS) {
      riskScore += 50
    }

    // Velocity check - too many transactions in short time
    const hourlyTransactionCount = recentTransactions?.filter((t: any) =>
      new Date(t.created_at) > new Date(Date.now() - 60 * 60 * 1000)
    ).length || 0

    if (hourlyTransactionCount > FRAUD_DETECTION_CONFIG.VELOCITY_LIMITS.MAX_TRANSACTIONS_PER_HOUR) {
      riskScore += 35
    }

  } catch (error) {
    console.error('Error calculating risk score:', error)
    riskScore = 50 // Default to medium risk if calculation fails
  }

  return Math.min(riskScore, 100) // Cap at 100
}

async function logFraudDetection(userId: string, transactionData: any, riskScore: number, action: string) {
  try {
    await supabaseClient
      .from('fraud_detection_logs')
      .insert({
        user_id: userId,
        risk_factors: {
          transaction_id: transactionData.id,
          amount: transactionData.amount,
          currency: transactionData.currency,
          payment_method: transactionData.payment_method,
          risk_indicators: transactionData.risk_indicators || {}
        },
        risk_score: riskScore,
        action_taken: action,
        notes: `Fraud detection triggered for transaction ${transactionData.id}`,
        created_at: new Date().toISOString()
      })
  } catch (error) {
    console.error('Error logging fraud detection:', error)
  }
}

async function checkFailedPaymentPatterns(userId: string) {
  try {
    // Check for multiple failed payments in short time
    const { data: failedPayments } = await supabaseClient
      .from('payment_transactions')
      .select('*')
      .eq('user_id', userId)
      .eq('status', 'failed')
      .gte('created_at', new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString())

    if (failedPayments && failedPayments.length >= FRAUD_DETECTION_CONFIG.MAX_FAILED_ATTEMPTS) {
      // Log suspicious pattern
      await logFraudDetection(userId, {
        id: 'pattern_detection',
        failed_count: failedPayments.length
      }, 80, 'review')

      // Send notification
      await supabaseClient
        .from('notifications')
        .insert({
          user_id: userId,
          type: 'security',
          title: 'Multiple Payment Failures Detected',
          message: 'We noticed multiple failed payment attempts. Please verify your payment method.',
          data: { failed_count: failedPayments.length }
        })
    }
  } catch (error) {
    console.error('Error checking failed payment patterns:', error)
  }
}
