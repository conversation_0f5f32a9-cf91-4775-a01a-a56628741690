-- Row Level Security Policies for Payment System
-- This migration creates comprehensive RLS policies to ensure data security

-- Enable RLS on all tables
ALTER TABLE subscription_plans ENABLE ROW LEVEL SECURITY;
ALTER TABLE stripe_customers ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_subscriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE payment_methods ENABLE ROW LEVEL SECURITY;
ALTER TABLE invoices ENABLE ROW LEVEL SECURITY;
ALTER TABLE payment_transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE fraud_detection_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE usage_tracking ENABLE ROW LEVEL SECURITY;
ALTER TABLE subscription_analytics ENABLE ROW LEVEL SECURITY;
ALTER TABLE promotional_codes ENABLE ROW LEVEL SECURITY;
ALTER TABLE promotional_code_usage ENABLE ROW LEVEL SECURITY;
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;

-- Subscription Plans Policies (Public read for active plans)
CREATE POLICY "Public can view active subscription plans" ON subscription_plans
    FOR SELECT USING (is_active = true);

CREATE POLICY "Service role can manage subscription plans" ON subscription_plans
    FOR ALL USING (auth.role() = 'service_role');

-- Stripe Customers Policies (Users can only access their own data)
CREATE POLICY "Users can view own stripe customer data" ON stripe_customers
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Service role can manage stripe customers" ON stripe_customers
    FOR ALL USING (auth.role() = 'service_role');

-- User Subscriptions Policies
CREATE POLICY "Users can view own subscriptions" ON user_subscriptions
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Service role can manage subscriptions" ON user_subscriptions
    FOR ALL USING (auth.role() = 'service_role');

-- Payment Methods Policies
CREATE POLICY "Users can view own payment methods" ON payment_methods
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own payment methods" ON payment_methods
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own payment methods" ON payment_methods
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own payment methods" ON payment_methods
    FOR DELETE USING (auth.uid() = user_id);

CREATE POLICY "Service role can manage payment methods" ON payment_methods
    FOR ALL USING (auth.role() = 'service_role');

-- Invoices Policies
CREATE POLICY "Users can view own invoices" ON invoices
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Service role can manage invoices" ON invoices
    FOR ALL USING (auth.role() = 'service_role');

-- Payment Transactions Policies
CREATE POLICY "Users can view own transactions" ON payment_transactions
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Service role can manage transactions" ON payment_transactions
    FOR ALL USING (auth.role() = 'service_role');

-- Fraud Detection Logs Policies (Admin only)
CREATE POLICY "Service role can manage fraud logs" ON fraud_detection_logs
    FOR ALL USING (auth.role() = 'service_role');

-- Usage Tracking Policies
CREATE POLICY "Users can view own usage" ON usage_tracking
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Service role can manage usage tracking" ON usage_tracking
    FOR ALL USING (auth.role() = 'service_role');

-- Subscription Analytics Policies (Admin only)
CREATE POLICY "Service role can manage analytics" ON subscription_analytics
    FOR ALL USING (auth.role() = 'service_role');

-- Promotional Codes Policies
CREATE POLICY "Public can view active promotional codes" ON promotional_codes
    FOR SELECT USING (is_active = true AND valid_from <= NOW() AND (valid_until IS NULL OR valid_until >= NOW()));

CREATE POLICY "Service role can manage promotional codes" ON promotional_codes
    FOR ALL USING (auth.role() = 'service_role');

-- Promotional Code Usage Policies
CREATE POLICY "Users can view own promo usage" ON promotional_code_usage
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own promo usage" ON promotional_code_usage
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Service role can manage promo usage" ON promotional_code_usage
    FOR ALL USING (auth.role() = 'service_role');

-- Notifications Policies
CREATE POLICY "Users can view own notifications" ON notifications
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can update own notifications" ON notifications
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Service role can manage notifications" ON notifications
    FOR ALL USING (auth.role() = 'service_role');

-- Create functions for subscription management
CREATE OR REPLACE FUNCTION get_user_subscription_status(user_uuid UUID)
RETURNS TABLE (
    has_active_subscription BOOLEAN,
    plan_name TEXT,
    status TEXT,
    current_period_end TIMESTAMP WITH TIME ZONE,
    max_identifications INTEGER
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        CASE WHEN us.status = 'active' THEN true ELSE false END as has_active_subscription,
        sp.name as plan_name,
        us.status,
        us.current_period_end,
        sp.max_identifications_per_month
    FROM user_subscriptions us
    JOIN subscription_plans sp ON us.plan_id = sp.id
    WHERE us.user_id = user_uuid
    AND us.status IN ('active', 'trialing')
    ORDER BY us.created_at DESC
    LIMIT 1;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check usage limits
CREATE OR REPLACE FUNCTION check_usage_limit(user_uuid UUID, feature_name TEXT)
RETURNS BOOLEAN AS $$
DECLARE
    user_limit INTEGER;
    current_usage INTEGER;
    period_start TIMESTAMP WITH TIME ZONE;
    period_end TIMESTAMP WITH TIME ZONE;
BEGIN
    -- Get user's subscription limit
    SELECT sp.max_identifications_per_month, us.current_period_start, us.current_period_end
    INTO user_limit, period_start, period_end
    FROM user_subscriptions us
    JOIN subscription_plans sp ON us.plan_id = sp.id
    WHERE us.user_id = user_uuid
    AND us.status IN ('active', 'trialing')
    ORDER BY us.created_at DESC
    LIMIT 1;
    
    -- If no subscription or unlimited (-1), allow
    IF user_limit IS NULL OR user_limit = -1 THEN
        RETURN true;
    END IF;
    
    -- Get current usage for this period
    SELECT COALESCE(SUM(usage_count), 0)
    INTO current_usage
    FROM usage_tracking
    WHERE user_id = user_uuid
    AND feature_type = feature_name
    AND period_start <= NOW()
    AND period_end >= NOW();
    
    -- Check if under limit
    RETURN current_usage < user_limit;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
