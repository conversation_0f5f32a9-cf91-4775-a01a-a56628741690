import { Request, Response, NextFunction } from 'express';
import { Counter, Histogram, register } from 'prom-client';

// Metrics collectors
const httpRequestsTotal = new Counter({
  name: 'http_requests_total',
  help: 'Total number of HTTP requests',
  labelNames: ['method', 'route', 'status_code', 'service']
});

const httpRequestDuration = new Histogram({
  name: 'http_request_duration_seconds',
  help: 'Duration of HTTP requests in seconds',
  labelNames: ['method', 'route', 'status_code', 'service'],
  buckets: [0.1, 0.3, 0.5, 0.7, 1, 3, 5, 7, 10]
});

const httpRequestSize = new Histogram({
  name: 'http_request_size_bytes',
  help: 'Size of HTTP requests in bytes',
  labelNames: ['method', 'route', 'service'],
  buckets: [100, 1000, 10000, 100000, 1000000, 10000000]
});

const httpResponseSize = new Histogram({
  name: 'http_response_size_bytes',
  help: 'Size of HTTP responses in bytes',
  labelNames: ['method', 'route', 'status_code', 'service'],
  buckets: [100, 1000, 10000, 100000, 1000000, 10000000]
});

const activeConnections = new Counter({
  name: 'http_active_connections',
  help: 'Number of active HTTP connections',
  labelNames: ['service']
});

// Register metrics
register.registerMetric(httpRequestsTotal);
register.registerMetric(httpRequestDuration);
register.registerMetric(httpRequestSize);
register.registerMetric(httpResponseSize);
register.registerMetric(activeConnections);

// Helper function to normalize route paths
const normalizeRoute = (path: string): string => {
  // Replace UUIDs and numeric IDs with placeholders
  return path
    .replace(/\/[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}/gi, '/:id')
    .replace(/\/\d+/g, '/:id')
    .replace(/\/[a-f0-9]{24}/g, '/:id'); // MongoDB ObjectIds
};

export const metricsMiddleware = (req: Request, res: Response, next: NextFunction): void => {
  const startTime = Date.now();
  const route = normalizeRoute(req.path);
  const service = 'api-gateway';

  // Track request size
  const requestSize = parseInt(req.get('content-length') || '0', 10);
  if (requestSize > 0) {
    httpRequestSize
      .labels(req.method, route, service)
      .observe(requestSize);
  }

  // Increment active connections
  activeConnections.labels(service).inc();

  // Override res.end to capture metrics when response is sent
  const originalEnd = res.end;
  res.end = function(chunk?: any, encoding?: any): Response {
    const duration = (Date.now() - startTime) / 1000;
    const statusCode = res.statusCode.toString();

    // Record metrics
    httpRequestsTotal
      .labels(req.method, route, statusCode, service)
      .inc();

    httpRequestDuration
      .labels(req.method, route, statusCode, service)
      .observe(duration);

    // Track response size
    const responseSize = parseInt(res.get('content-length') || '0', 10);
    if (responseSize > 0) {
      httpResponseSize
        .labels(req.method, route, statusCode, service)
        .observe(responseSize);
    }

    // Decrement active connections
    activeConnections.labels(service).dec();

    // Call original end method
    return originalEnd.call(this, chunk, encoding);
  };

  next();
};

// Custom metrics for business logic
export const businessMetrics = {
  // Authentication metrics
  authAttempts: new Counter({
    name: 'auth_attempts_total',
    help: 'Total number of authentication attempts',
    labelNames: ['method', 'status', 'service']
  }),

  // API usage metrics
  apiCalls: new Counter({
    name: 'api_calls_total',
    help: 'Total number of API calls by endpoint',
    labelNames: ['endpoint', 'user_id', 'service']
  }),

  // Error metrics
  errors: new Counter({
    name: 'application_errors_total',
    help: 'Total number of application errors',
    labelNames: ['error_type', 'service', 'endpoint']
  }),

  // Rate limiting metrics
  rateLimitHits: new Counter({
    name: 'rate_limit_hits_total',
    help: 'Total number of rate limit hits',
    labelNames: ['endpoint', 'ip', 'service']
  })
};

// Register business metrics
Object.values(businessMetrics).forEach(metric => {
  register.registerMetric(metric);
});

export { register };
