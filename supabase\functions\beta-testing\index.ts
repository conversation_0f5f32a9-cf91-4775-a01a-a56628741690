import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

interface BetaTestingRequest {
  action: 'apply_program' | 'submit_feedback' | 'get_analytics' | 'send_notification' | 'manage_release';
  data: any;
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_ANON_KEY') ?? '',
      {
        global: {
          headers: { Authorization: req.headers.get('Authorization')! },
        },
      }
    );

    const { action, data }: BetaTestingRequest = await req.json();

    let result;
    switch (action) {
      case 'apply_program':
        result = await applyToProgram(supabaseClient, data);
        break;
      case 'submit_feedback':
        result = await submitFeedback(supabaseClient, data);
        break;
      case 'get_analytics':
        result = await getBetaAnalytics(supabaseClient, data);
        break;
      case 'send_notification':
        result = await sendNotification(supabaseClient, data);
        break;
      case 'manage_release':
        result = await manageRelease(supabaseClient, data);
        break;
      default:
        throw new Error(`Unknown action: ${action}`);
    }

    return new Response(JSON.stringify(result), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });
  } catch (error) {
    console.error('Beta testing error:', error);
    return new Response(JSON.stringify({ error: error.message }), {
      status: 400,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });
  }
});

/**
 * Apply to join a beta program
 */
async function applyToProgram(supabaseClient: any, data: {
  program_id: string;
  user_id: string;
  application_reason: string;
  application_data?: any;
}) {
  const { program_id, user_id, application_reason, application_data = {} } = data;

  try {
    // Check if user is already a participant
    const { data: existing } = await supabaseClient
      .from('beta_participants')
      .select('id, status')
      .eq('user_id', user_id)
      .eq('program_id', program_id)
      .single();

    if (existing) {
      return { error: `Already ${existing.status} for this program` };
    }

    // Get program details
    const { data: program, error: programError } = await supabaseClient
      .from('beta_programs')
      .select('*')
      .eq('id', program_id)
      .single();

    if (programError || !program) {
      return { error: 'Program not found' };
    }

    // Check eligibility
    const eligibilityCheck = await checkEligibility(supabaseClient, program, user_id);
    if (!eligibilityCheck.eligible) {
      return { error: eligibilityCheck.reason };
    }

    // Create application
    const { data: participant, error } = await supabaseClient
      .from('beta_participants')
      .insert({
        user_id,
        program_id,
        status: program.auto_approve ? 'approved' : 'pending',
        participation_level: 'standard',
        application_reason,
        application_data,
        joined_at: program.auto_approve ? new Date().toISOString() : null,
      })
      .select('id')
      .single();

    if (error) throw error;

    // Update program participant count
    await supabaseClient
      .from('beta_programs')
      .update({
        current_participants: supabaseClient.sql`current_participants + 1`,
      })
      .eq('id', program_id);

    // Send welcome notification if auto-approved
    if (program.auto_approve) {
      await sendWelcomeNotification(supabaseClient, participant.id, program);
    }

    return {
      success: true,
      participant_id: participant.id,
      status: program.auto_approve ? 'approved' : 'pending',
    };
  } catch (error) {
    console.error('Error applying to program:', error);
    return { error: error.message };
  }
}

/**
 * Submit feedback for a beta program
 */
async function submitFeedback(supabaseClient: any, data: {
  program_id: string;
  user_id: string;
  feedback_type: string;
  title: string;
  description: string;
  priority?: string;
  category?: string;
  steps_to_reproduce?: string;
  expected_behavior?: string;
  actual_behavior?: string;
  screenshots?: string[];
  device_info?: any;
}) {
  const {
    program_id,
    user_id,
    feedback_type,
    title,
    description,
    priority = 'medium',
    category,
    steps_to_reproduce,
    expected_behavior,
    actual_behavior,
    screenshots = [],
    device_info = {},
  } = data;

  try {
    // Get participant info
    const { data: participant } = await supabaseClient
      .from('beta_participants')
      .select('id')
      .eq('user_id', user_id)
      .eq('program_id', program_id)
      .eq('status', 'active')
      .single();

    if (!participant) {
      return { error: 'Not an active participant in this program' };
    }

    // Create feedback
    const { data: feedback, error } = await supabaseClient
      .from('beta_feedback')
      .insert({
        participant_id: participant.id,
        program_id,
        user_id,
        feedback_type,
        category,
        priority,
        title,
        description,
        steps_to_reproduce,
        expected_behavior,
        actual_behavior,
        screenshots,
        device_info,
        status: 'open',
      })
      .select('id')
      .single();

    if (error) throw error;

    // Update participant feedback count
    await supabaseClient
      .from('beta_participants')
      .update({
        feedback_submissions: supabaseClient.sql`feedback_submissions + 1`,
        last_active_at: new Date().toISOString(),
      })
      .eq('id', participant.id);

    // Update specific counters based on feedback type
    if (feedback_type === 'bug_report') {
      await supabaseClient
        .from('beta_participants')
        .update({
          bug_reports: supabaseClient.sql`bug_reports + 1`,
        })
        .eq('id', participant.id);
    } else if (feedback_type === 'feature_request') {
      await supabaseClient
        .from('beta_participants')
        .update({
          feature_requests: supabaseClient.sql`feature_requests + 1`,
        })
        .eq('id', participant.id);
    }

    // Calculate and update engagement score
    await updateEngagementScore(supabaseClient, participant.id);

    return {
      success: true,
      feedback_id: feedback.id,
    };
  } catch (error) {
    console.error('Error submitting feedback:', error);
    return { error: error.message };
  }
}

/**
 * Get beta analytics for a program
 */
async function getBetaAnalytics(supabaseClient: any, data: {
  program_id: string;
  date_range?: { start: string; end: string };
}) {
  const { program_id, date_range } = data;

  try {
    // Get program overview
    const { data: program } = await supabaseClient
      .from('beta_programs')
      .select('*')
      .eq('id', program_id)
      .single();

    // Get participant statistics
    const { data: participantStats } = await supabaseClient
      .from('beta_participants')
      .select('status, participation_level, feedback_submissions, bug_reports, feature_requests, engagement_score')
      .eq('program_id', program_id);

    // Get feedback statistics
    const { data: feedbackStats } = await supabaseClient
      .from('beta_feedback')
      .select('feedback_type, priority, status, created_at')
      .eq('program_id', program_id);

    // Calculate analytics
    const analytics = {
      program_overview: program,
      participant_metrics: {
        total_participants: participantStats?.length || 0,
        active_participants: participantStats?.filter(p => p.status === 'active').length || 0,
        pending_participants: participantStats?.filter(p => p.status === 'pending').length || 0,
        average_engagement: participantStats?.reduce((sum, p) => sum + (p.engagement_score || 0), 0) / (participantStats?.length || 1),
      },
      feedback_metrics: {
        total_feedback: feedbackStats?.length || 0,
        bug_reports: feedbackStats?.filter(f => f.feedback_type === 'bug_report').length || 0,
        feature_requests: feedbackStats?.filter(f => f.feedback_type === 'feature_request').length || 0,
        critical_issues: feedbackStats?.filter(f => f.priority === 'critical').length || 0,
        resolved_issues: feedbackStats?.filter(f => f.status === 'resolved').length || 0,
      },
      engagement_trends: await getEngagementTrends(supabaseClient, program_id, date_range),
    };

    return analytics;
  } catch (error) {
    console.error('Error getting beta analytics:', error);
    return { error: error.message };
  }
}

/**
 * Send notification to beta participants
 */
async function sendNotification(supabaseClient: any, data: {
  program_id: string;
  notification_type: string;
  title: string;
  message: string;
  target_participants?: any;
  delivery_method?: string;
}) {
  const {
    program_id,
    notification_type,
    title,
    message,
    target_participants = {},
    delivery_method = 'push',
  } = data;

  try {
    // Create notification record
    const { data: notification, error } = await supabaseClient
      .from('beta_notifications')
      .insert({
        program_id,
        notification_type,
        title,
        message,
        target_participants,
        delivery_method,
        status: 'sending',
      })
      .select('id')
      .single();

    if (error) throw error;

    // Get target participants
    let participantQuery = supabaseClient
      .from('beta_participants')
      .select('user_id, email_notifications, push_notifications')
      .eq('program_id', program_id)
      .eq('status', 'active');

    // Apply targeting filters
    if (target_participants.participation_level) {
      participantQuery = participantQuery.eq('participation_level', target_participants.participation_level);
    }

    const { data: participants } = await participantQuery;

    if (!participants || participants.length === 0) {
      await supabaseClient
        .from('beta_notifications')
        .update({ status: 'failed', total_recipients: 0 })
        .eq('id', notification.id);

      return { error: 'No eligible participants found' };
    }

    // Filter participants based on notification preferences
    const eligibleParticipants = participants.filter(p => {
      if (delivery_method === 'email' && !p.email_notifications) return false;
      if (delivery_method === 'push' && !p.push_notifications) return false;
      return true;
    });

    // Update notification with recipient count
    await supabaseClient
      .from('beta_notifications')
      .update({
        total_recipients: eligibleParticipants.length,
        status: 'sent',
        delivered_count: eligibleParticipants.length, // Simplified - in real implementation would track actual delivery
      })
      .eq('id', notification.id);

    // In a real implementation, this would integrate with push notification and email services
    console.log(`Notification sent to ${eligibleParticipants.length} participants`);

    return {
      success: true,
      notification_id: notification.id,
      recipients_count: eligibleParticipants.length,
    };
  } catch (error) {
    console.error('Error sending notification:', error);
    return { error: error.message };
  }
}

/**
 * Manage beta release
 */
async function manageRelease(supabaseClient: any, data: {
  action: 'create' | 'deploy' | 'rollback';
  program_id: string;
  release_data?: any;
  release_id?: string;
}) {
  const { action, program_id, release_data, release_id } = data;

  try {
    switch (action) {
      case 'create':
        return await createRelease(supabaseClient, program_id, release_data);
      case 'deploy':
        return await deployRelease(supabaseClient, release_id);
      case 'rollback':
        return await rollbackRelease(supabaseClient, release_id, release_data?.reason);
      default:
        return { error: 'Invalid release action' };
    }
  } catch (error) {
    console.error('Error managing release:', error);
    return { error: error.message };
  }
}

// Helper functions

async function checkEligibility(supabaseClient: any, program: any, user_id: string) {
  // Check max participants
  if (program.max_participants && program.current_participants >= program.max_participants) {
    return { eligible: false, reason: 'Program is at maximum capacity' };
  }

  // Check platform restrictions
  // In a real implementation, this would check user's platform from their profile or device info

  // Check eligibility criteria
  // In a real implementation, this would evaluate complex eligibility rules

  return { eligible: true };
}

async function sendWelcomeNotification(supabaseClient: any, participant_id: string, program: any) {
  // In a real implementation, this would send welcome notifications
  console.log(`Sending welcome notification for program: ${program.program_name}`);
}

async function updateEngagementScore(supabaseClient: any, participant_id: string) {
  // Get participant data
  const { data: participant } = await supabaseClient
    .from('beta_participants')
    .select('feedback_submissions, bug_reports, feature_requests, joined_at')
    .eq('id', participant_id)
    .single();

  if (!participant) return;

  // Calculate engagement score based on activity
  const daysActive = Math.max(1, Math.floor((Date.now() - new Date(participant.joined_at).getTime()) / (1000 * 60 * 60 * 24)));
  const feedbackRate = participant.feedback_submissions / daysActive;
  const qualityBonus = (participant.bug_reports * 1.5 + participant.feature_requests * 1.2) / Math.max(1, participant.feedback_submissions);
  
  const engagementScore = Math.min(5.0, (feedbackRate * 10 + qualityBonus) * 0.5);

  await supabaseClient
    .from('beta_participants')
    .update({ engagement_score: engagementScore })
    .eq('id', participant_id);
}

async function getEngagementTrends(supabaseClient: any, program_id: string, date_range?: any) {
  // In a real implementation, this would calculate engagement trends over time
  return {
    daily_active_participants: [],
    feedback_volume: [],
    resolution_rate: [],
  };
}

async function createRelease(supabaseClient: any, program_id: string, release_data: any) {
  const { data: release, error } = await supabaseClient
    .from('beta_releases')
    .insert({
      program_id,
      ...release_data,
      status: 'draft',
    })
    .select('id')
    .single();

  if (error) throw error;

  return { success: true, release_id: release.id };
}

async function deployRelease(supabaseClient: any, release_id: string) {
  // Update release status
  await supabaseClient
    .from('beta_releases')
    .update({
      status: 'released',
      released_date: new Date().toISOString(),
    })
    .eq('id', release_id);

  // In a real implementation, this would trigger the actual deployment process

  return { success: true };
}

async function rollbackRelease(supabaseClient: any, release_id: string, reason?: string) {
  await supabaseClient
    .from('beta_releases')
    .update({
      status: 'rolled_back',
      rollback_reason: reason,
      rolled_back_at: new Date().toISOString(),
    })
    .eq('id', release_id);

  return { success: true };
}
