-- Beta Testing Platform Schema
-- This migration creates comprehensive beta testing infrastructure

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Beta Programs Table
CREATE TABLE IF NOT EXISTS beta_programs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Program identification
    program_key VARCHAR(100) NOT NULL UNIQUE,
    program_name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    description TEXT,
    
    -- Program configuration
    program_type VARCHAR(20) NOT NULL DEFAULT 'feature' CHECK (program_type IN ('feature', 'app_version', 'experiment', 'early_access')),
    status VARCHAR(20) NOT NULL DEFAULT 'draft' CHECK (status IN ('draft', 'recruiting', 'active', 'paused', 'completed', 'archived')),
    
    -- Capacity and targeting
    max_participants INTEGER,
    current_participants INTEGER DEFAULT 0,
    auto_approve BOOLEAN NOT NULL DEFAULT false,
    
    -- Eligibility criteria
    eligibility_criteria JSONB DEFAULT '{}',
    target_audience JSONB DEFAULT '{}',
    
    -- Program timeline
    recruitment_start_date TIMESTAMP WITH TIME ZONE,
    recruitment_end_date TIMESTAMP WITH TIME ZONE,
    program_start_date TIMESTAMP WITH TIME ZONE,
    program_end_date TIMESTAMP WITH TIME ZONE,
    
    -- Feature configuration
    feature_flags JSONB DEFAULT '{}',
    app_version_requirements JSONB DEFAULT '{}',
    platform_restrictions JSONB DEFAULT '[]',
    
    -- Feedback configuration
    feedback_enabled BOOLEAN NOT NULL DEFAULT true,
    feedback_frequency VARCHAR(20) DEFAULT 'weekly' CHECK (feedback_frequency IN ('daily', 'weekly', 'biweekly', 'monthly', 'on_demand')),
    required_feedback_types JSONB DEFAULT '[]',
    
    -- Communication
    welcome_message TEXT,
    instructions TEXT,
    contact_email VARCHAR(255),
    
    -- Metadata
    tags JSONB DEFAULT '[]',
    priority INTEGER DEFAULT 0,
    
    -- Audit
    created_by UUID REFERENCES auth.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Beta Participants Table
CREATE TABLE IF NOT EXISTS beta_participants (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    program_id UUID NOT NULL REFERENCES beta_programs(id) ON DELETE CASCADE,
    
    -- Participation status
    status VARCHAR(20) NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'active', 'paused', 'completed', 'removed', 'declined')),
    participation_level VARCHAR(20) DEFAULT 'standard' CHECK (participation_level IN ('observer', 'standard', 'power_user', 'moderator')),
    
    -- Application details
    application_reason TEXT,
    application_data JSONB DEFAULT '{}',
    
    -- Approval workflow
    approved_by UUID REFERENCES auth.users(id),
    approved_at TIMESTAMP WITH TIME ZONE,
    approval_notes TEXT,
    
    -- Participation tracking
    joined_at TIMESTAMP WITH TIME ZONE,
    last_active_at TIMESTAMP WITH TIME ZONE,
    completion_date TIMESTAMP WITH TIME ZONE,
    
    -- Engagement metrics
    feedback_submissions INTEGER DEFAULT 0,
    bug_reports INTEGER DEFAULT 0,
    feature_requests INTEGER DEFAULT 0,
    engagement_score DECIMAL(3,2) DEFAULT 0,
    
    -- Communication preferences
    email_notifications BOOLEAN NOT NULL DEFAULT true,
    push_notifications BOOLEAN NOT NULL DEFAULT true,
    weekly_digest BOOLEAN NOT NULL DEFAULT true,
    
    -- Exit information
    exit_reason VARCHAR(50),
    exit_feedback TEXT,
    exit_date TIMESTAMP WITH TIME ZONE,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(user_id, program_id)
);

-- Beta Feedback Table
CREATE TABLE IF NOT EXISTS beta_feedback (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    participant_id UUID NOT NULL REFERENCES beta_participants(id) ON DELETE CASCADE,
    program_id UUID NOT NULL REFERENCES beta_programs(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    
    -- Feedback classification
    feedback_type VARCHAR(30) NOT NULL CHECK (feedback_type IN ('bug_report', 'feature_request', 'usability', 'performance', 'general', 'survey_response')),
    category VARCHAR(50),
    priority VARCHAR(20) DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'critical')),
    
    -- Feedback content
    title VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    steps_to_reproduce TEXT,
    expected_behavior TEXT,
    actual_behavior TEXT,
    
    -- Technical details
    app_version VARCHAR(50),
    platform VARCHAR(20),
    device_info JSONB DEFAULT '{}',
    user_agent TEXT,
    
    -- Media attachments
    screenshots JSONB DEFAULT '[]',
    videos JSONB DEFAULT '[]',
    logs JSONB DEFAULT '[]',
    
    -- Status and workflow
    status VARCHAR(20) NOT NULL DEFAULT 'open' CHECK (status IN ('open', 'in_review', 'in_progress', 'resolved', 'closed', 'duplicate', 'wont_fix')),
    assigned_to UUID REFERENCES auth.users(id),
    
    -- Resolution tracking
    resolution_notes TEXT,
    resolved_at TIMESTAMP WITH TIME ZONE,
    resolved_by UUID REFERENCES auth.users(id),
    
    -- Engagement
    upvotes INTEGER DEFAULT 0,
    downvotes INTEGER DEFAULT 0,
    comments_count INTEGER DEFAULT 0,
    
    -- Internal tracking
    internal_notes TEXT,
    tags JSONB DEFAULT '[]',
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Beta Feedback Comments Table
CREATE TABLE IF NOT EXISTS beta_feedback_comments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    feedback_id UUID NOT NULL REFERENCES beta_feedback(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    
    -- Comment content
    comment_text TEXT NOT NULL,
    comment_type VARCHAR(20) DEFAULT 'comment' CHECK (comment_type IN ('comment', 'status_update', 'resolution', 'internal_note')),
    
    -- Visibility
    is_internal BOOLEAN NOT NULL DEFAULT false,
    is_public BOOLEAN NOT NULL DEFAULT true,
    
    -- Threading
    parent_comment_id UUID REFERENCES beta_feedback_comments(id),
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Beta Surveys Table
CREATE TABLE IF NOT EXISTS beta_surveys (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    program_id UUID NOT NULL REFERENCES beta_programs(id) ON DELETE CASCADE,
    
    -- Survey details
    survey_name VARCHAR(255) NOT NULL,
    description TEXT,
    survey_type VARCHAR(20) DEFAULT 'feedback' CHECK (survey_type IN ('onboarding', 'feedback', 'exit', 'feature_specific', 'satisfaction')),
    
    -- Survey configuration
    questions JSONB NOT NULL DEFAULT '[]',
    is_required BOOLEAN NOT NULL DEFAULT false,
    is_anonymous BOOLEAN NOT NULL DEFAULT false,
    
    -- Targeting
    target_participants JSONB DEFAULT '{}',
    
    -- Timeline
    start_date TIMESTAMP WITH TIME ZONE,
    end_date TIMESTAMP WITH TIME ZONE,
    
    -- Status
    status VARCHAR(20) NOT NULL DEFAULT 'draft' CHECK (status IN ('draft', 'active', 'paused', 'completed', 'archived')),
    
    -- Response tracking
    total_responses INTEGER DEFAULT 0,
    completion_rate DECIMAL(5,2) DEFAULT 0,
    
    created_by UUID REFERENCES auth.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Beta Survey Responses Table
CREATE TABLE IF NOT EXISTS beta_survey_responses (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    survey_id UUID NOT NULL REFERENCES beta_surveys(id) ON DELETE CASCADE,
    participant_id UUID NOT NULL REFERENCES beta_participants(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    
    -- Response data
    responses JSONB NOT NULL DEFAULT '{}',
    completion_status VARCHAR(20) DEFAULT 'in_progress' CHECK (completion_status IN ('in_progress', 'completed', 'abandoned')),
    completion_percentage DECIMAL(5,2) DEFAULT 0,
    
    -- Timing
    started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE,
    time_spent_seconds INTEGER,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(survey_id, participant_id)
);

-- Beta Releases Table
CREATE TABLE IF NOT EXISTS beta_releases (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    program_id UUID NOT NULL REFERENCES beta_programs(id) ON DELETE CASCADE,
    
    -- Release details
    release_name VARCHAR(255) NOT NULL,
    version VARCHAR(50) NOT NULL,
    description TEXT,
    release_notes TEXT,
    
    -- Release configuration
    release_type VARCHAR(20) DEFAULT 'feature' CHECK (release_type IN ('feature', 'bugfix', 'hotfix', 'major', 'minor', 'patch')),
    platform VARCHAR(20) DEFAULT 'all' CHECK (platform IN ('all', 'ios', 'android', 'web')),
    
    -- Rollout configuration
    rollout_strategy VARCHAR(20) DEFAULT 'immediate' CHECK (rollout_strategy IN ('immediate', 'gradual', 'scheduled')),
    rollout_percentage DECIMAL(5,2) DEFAULT 100,
    
    -- Targeting
    target_participants JSONB DEFAULT '{}',
    
    -- Timeline
    scheduled_date TIMESTAMP WITH TIME ZONE,
    released_date TIMESTAMP WITH TIME ZONE,
    
    -- Status
    status VARCHAR(20) NOT NULL DEFAULT 'draft' CHECK (status IN ('draft', 'scheduled', 'releasing', 'released', 'paused', 'rolled_back')),
    
    -- Tracking
    participants_notified INTEGER DEFAULT 0,
    download_count INTEGER DEFAULT 0,
    install_count INTEGER DEFAULT 0,
    
    -- Rollback information
    rollback_reason TEXT,
    rolled_back_at TIMESTAMP WITH TIME ZONE,
    rolled_back_by UUID REFERENCES auth.users(id),
    
    created_by UUID REFERENCES auth.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Beta Analytics Table
CREATE TABLE IF NOT EXISTS beta_analytics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    program_id UUID NOT NULL REFERENCES beta_programs(id) ON DELETE CASCADE,
    
    -- Analytics period
    date DATE NOT NULL,
    
    -- Participation metrics
    total_participants INTEGER DEFAULT 0,
    active_participants INTEGER DEFAULT 0,
    new_participants INTEGER DEFAULT 0,
    churned_participants INTEGER DEFAULT 0,
    
    -- Engagement metrics
    total_feedback_submissions INTEGER DEFAULT 0,
    total_bug_reports INTEGER DEFAULT 0,
    total_feature_requests INTEGER DEFAULT 0,
    average_engagement_score DECIMAL(3,2) DEFAULT 0,
    
    -- Quality metrics
    critical_bugs INTEGER DEFAULT 0,
    high_priority_feedback INTEGER DEFAULT 0,
    resolved_issues INTEGER DEFAULT 0,
    average_resolution_time_hours DECIMAL(8,2) DEFAULT 0,
    
    -- Survey metrics
    survey_responses INTEGER DEFAULT 0,
    average_satisfaction_score DECIMAL(3,2) DEFAULT 0,
    
    -- Release metrics
    releases_count INTEGER DEFAULT 0,
    downloads_count INTEGER DEFAULT 0,
    installs_count INTEGER DEFAULT 0,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(program_id, date)
);

-- Beta Notifications Table
CREATE TABLE IF NOT EXISTS beta_notifications (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    program_id UUID NOT NULL REFERENCES beta_programs(id) ON DELETE CASCADE,
    
    -- Notification details
    notification_type VARCHAR(30) NOT NULL CHECK (notification_type IN ('welcome', 'release', 'survey', 'feedback_request', 'program_update', 'reminder')),
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    
    -- Targeting
    target_participants JSONB DEFAULT '{}',
    participant_level VARCHAR(20),
    
    -- Delivery configuration
    delivery_method VARCHAR(20) DEFAULT 'push' CHECK (delivery_method IN ('push', 'email', 'in_app', 'all')),
    scheduled_date TIMESTAMP WITH TIME ZONE,
    
    -- Status
    status VARCHAR(20) NOT NULL DEFAULT 'draft' CHECK (status IN ('draft', 'scheduled', 'sending', 'sent', 'failed')),
    
    -- Delivery tracking
    total_recipients INTEGER DEFAULT 0,
    delivered_count INTEGER DEFAULT 0,
    opened_count INTEGER DEFAULT 0,
    clicked_count INTEGER DEFAULT 0,
    
    created_by UUID REFERENCES auth.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_beta_programs_key ON beta_programs(program_key);
CREATE INDEX IF NOT EXISTS idx_beta_programs_status ON beta_programs(status);
CREATE INDEX IF NOT EXISTS idx_beta_programs_type ON beta_programs(program_type);

CREATE INDEX IF NOT EXISTS idx_beta_participants_user_id ON beta_participants(user_id);
CREATE INDEX IF NOT EXISTS idx_beta_participants_program_id ON beta_participants(program_id);
CREATE INDEX IF NOT EXISTS idx_beta_participants_status ON beta_participants(status);

CREATE INDEX IF NOT EXISTS idx_beta_feedback_participant_id ON beta_feedback(participant_id);
CREATE INDEX IF NOT EXISTS idx_beta_feedback_program_id ON beta_feedback(program_id);
CREATE INDEX IF NOT EXISTS idx_beta_feedback_type ON beta_feedback(feedback_type);
CREATE INDEX IF NOT EXISTS idx_beta_feedback_status ON beta_feedback(status);
CREATE INDEX IF NOT EXISTS idx_beta_feedback_priority ON beta_feedback(priority);

CREATE INDEX IF NOT EXISTS idx_beta_feedback_comments_feedback_id ON beta_feedback_comments(feedback_id);
CREATE INDEX IF NOT EXISTS idx_beta_feedback_comments_user_id ON beta_feedback_comments(user_id);

CREATE INDEX IF NOT EXISTS idx_beta_surveys_program_id ON beta_surveys(program_id);
CREATE INDEX IF NOT EXISTS idx_beta_surveys_status ON beta_surveys(status);

CREATE INDEX IF NOT EXISTS idx_beta_survey_responses_survey_id ON beta_survey_responses(survey_id);
CREATE INDEX IF NOT EXISTS idx_beta_survey_responses_participant_id ON beta_survey_responses(participant_id);

CREATE INDEX IF NOT EXISTS idx_beta_releases_program_id ON beta_releases(program_id);
CREATE INDEX IF NOT EXISTS idx_beta_releases_status ON beta_releases(status);

CREATE INDEX IF NOT EXISTS idx_beta_analytics_program_date ON beta_analytics(program_id, date);

CREATE INDEX IF NOT EXISTS idx_beta_notifications_program_id ON beta_notifications(program_id);
CREATE INDEX IF NOT EXISTS idx_beta_notifications_status ON beta_notifications(status);

-- Create updated_at triggers
CREATE TRIGGER update_beta_programs_updated_at BEFORE UPDATE ON beta_programs FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_beta_participants_updated_at BEFORE UPDATE ON beta_participants FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_beta_feedback_updated_at BEFORE UPDATE ON beta_feedback FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_beta_feedback_comments_updated_at BEFORE UPDATE ON beta_feedback_comments FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_beta_surveys_updated_at BEFORE UPDATE ON beta_surveys FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_beta_survey_responses_updated_at BEFORE UPDATE ON beta_survey_responses FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_beta_releases_updated_at BEFORE UPDATE ON beta_releases FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_beta_notifications_updated_at BEFORE UPDATE ON beta_notifications FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Enable RLS on all beta testing tables
ALTER TABLE beta_programs ENABLE ROW LEVEL SECURITY;
ALTER TABLE beta_participants ENABLE ROW LEVEL SECURITY;
ALTER TABLE beta_feedback ENABLE ROW LEVEL SECURITY;
ALTER TABLE beta_feedback_comments ENABLE ROW LEVEL SECURITY;
ALTER TABLE beta_surveys ENABLE ROW LEVEL SECURITY;
ALTER TABLE beta_survey_responses ENABLE ROW LEVEL SECURITY;
ALTER TABLE beta_releases ENABLE ROW LEVEL SECURITY;
ALTER TABLE beta_analytics ENABLE ROW LEVEL SECURITY;
ALTER TABLE beta_notifications ENABLE ROW LEVEL SECURITY;

-- Beta Programs Policies
CREATE POLICY "Service role can manage beta programs" ON beta_programs
    FOR ALL USING (auth.role() = 'service_role');

CREATE POLICY "Admins can manage beta programs" ON beta_programs
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM user_profiles
            WHERE id = auth.uid()
            AND role IN ('admin', 'super_admin', 'beta_manager')
        )
    );

CREATE POLICY "Users can view active beta programs" ON beta_programs
    FOR SELECT USING (
        status IN ('recruiting', 'active')
        AND (recruitment_start_date IS NULL OR recruitment_start_date <= NOW())
        AND (recruitment_end_date IS NULL OR recruitment_end_date >= NOW())
    );

-- Beta Participants Policies
CREATE POLICY "Users can view own participation" ON beta_participants
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can apply to beta programs" ON beta_participants
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own participation" ON beta_participants
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Service role can manage participants" ON beta_participants
    FOR ALL USING (auth.role() = 'service_role');

CREATE POLICY "Beta managers can manage participants" ON beta_participants
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM user_profiles
            WHERE id = auth.uid()
            AND role IN ('admin', 'super_admin', 'beta_manager')
        )
    );

-- Beta Feedback Policies
CREATE POLICY "Participants can view feedback in their programs" ON beta_feedback
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM beta_participants
            WHERE user_id = auth.uid()
            AND program_id = beta_feedback.program_id
            AND status = 'active'
        )
    );

CREATE POLICY "Participants can submit feedback" ON beta_feedback
    FOR INSERT WITH CHECK (
        auth.uid() = user_id
        AND EXISTS (
            SELECT 1 FROM beta_participants
            WHERE user_id = auth.uid()
            AND program_id = beta_feedback.program_id
            AND status = 'active'
        )
    );

CREATE POLICY "Users can update own feedback" ON beta_feedback
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Service role can manage feedback" ON beta_feedback
    FOR ALL USING (auth.role() = 'service_role');

CREATE POLICY "Beta managers can manage feedback" ON beta_feedback
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM user_profiles
            WHERE id = auth.uid()
            AND role IN ('admin', 'super_admin', 'beta_manager')
        )
    );

-- Beta Feedback Comments Policies
CREATE POLICY "Participants can view comments on feedback" ON beta_feedback_comments
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM beta_feedback bf
            JOIN beta_participants bp ON bf.program_id = bp.program_id
            WHERE bf.id = beta_feedback_comments.feedback_id
            AND bp.user_id = auth.uid()
            AND bp.status = 'active'
        )
    );

CREATE POLICY "Participants can comment on feedback" ON beta_feedback_comments
    FOR INSERT WITH CHECK (
        auth.uid() = user_id
        AND EXISTS (
            SELECT 1 FROM beta_feedback bf
            JOIN beta_participants bp ON bf.program_id = bp.program_id
            WHERE bf.id = beta_feedback_comments.feedback_id
            AND bp.user_id = auth.uid()
            AND bp.status = 'active'
        )
    );

CREATE POLICY "Users can update own comments" ON beta_feedback_comments
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Service role can manage comments" ON beta_feedback_comments
    FOR ALL USING (auth.role() = 'service_role');

-- Beta Surveys Policies
CREATE POLICY "Participants can view surveys in their programs" ON beta_surveys
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM beta_participants
            WHERE user_id = auth.uid()
            AND program_id = beta_surveys.program_id
            AND status = 'active'
        )
    );

CREATE POLICY "Service role can manage surveys" ON beta_surveys
    FOR ALL USING (auth.role() = 'service_role');

CREATE POLICY "Beta managers can manage surveys" ON beta_surveys
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM user_profiles
            WHERE id = auth.uid()
            AND role IN ('admin', 'super_admin', 'beta_manager')
        )
    );

-- Beta Survey Responses Policies
CREATE POLICY "Users can view own survey responses" ON beta_survey_responses
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Participants can submit survey responses" ON beta_survey_responses
    FOR INSERT WITH CHECK (
        auth.uid() = user_id
        AND EXISTS (
            SELECT 1 FROM beta_participants
            WHERE user_id = auth.uid()
            AND id = beta_survey_responses.participant_id
            AND status = 'active'
        )
    );

CREATE POLICY "Users can update own survey responses" ON beta_survey_responses
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Service role can manage survey responses" ON beta_survey_responses
    FOR ALL USING (auth.role() = 'service_role');

-- Beta Releases Policies
CREATE POLICY "Participants can view releases in their programs" ON beta_releases
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM beta_participants
            WHERE user_id = auth.uid()
            AND program_id = beta_releases.program_id
            AND status = 'active'
        )
    );

CREATE POLICY "Service role can manage releases" ON beta_releases
    FOR ALL USING (auth.role() = 'service_role');

CREATE POLICY "Beta managers can manage releases" ON beta_releases
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM user_profiles
            WHERE id = auth.uid()
            AND role IN ('admin', 'super_admin', 'beta_manager')
        )
    );

-- Beta Analytics Policies
CREATE POLICY "Service role can manage analytics" ON beta_analytics
    FOR ALL USING (auth.role() = 'service_role');

CREATE POLICY "Beta managers can view analytics" ON beta_analytics
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM user_profiles
            WHERE id = auth.uid()
            AND role IN ('admin', 'super_admin', 'beta_manager')
        )
    );

-- Beta Notifications Policies
CREATE POLICY "Service role can manage notifications" ON beta_notifications
    FOR ALL USING (auth.role() = 'service_role');

CREATE POLICY "Beta managers can manage notifications" ON beta_notifications
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM user_profiles
            WHERE id = auth.uid()
            AND role IN ('admin', 'super_admin', 'beta_manager')
        )
    );
