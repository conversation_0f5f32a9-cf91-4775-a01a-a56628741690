import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import Stripe from 'https://esm.sh/stripe@12.0.0?target=deno'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

const stripe = new Stripe(Deno.env.get('STRIPE_SECRET_KEY') || '', {
  apiVersion: '2023-10-16',
})

const supabaseClient = createClient(
  Deno.env.get('SUPABASE_URL') ?? '',
  Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
)

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const authHeader = req.headers.get('Authorization')!
    const token = authHeader.replace('Bearer ', '')

    const { data: { user }, error: authError } = await supabaseClient.auth.getUser(token)
    if (authError || !user) {
      throw new Error('Unauthorized')
    }

    const { userId } = await req.json()

    // Get the most recent subscription that can be reactivated
    const { data: subscription, error: subError } = await supabaseClient
      .from('user_subscriptions')
      .select(`
        *,
        plan:subscription_plans(*)
      `)
      .eq('user_id', userId)
      .eq('cancel_at_period_end', true)
      .eq('status', 'active')
      .order('created_at', { ascending: false })
      .limit(1)
      .single()

    if (subError || !subscription) {
      throw new Error('No subscription found that can be reactivated')
    }

    // Check if subscription is still within the current period
    const currentPeriodEnd = new Date(subscription.current_period_end)
    const now = new Date()

    if (now > currentPeriodEnd) {
      throw new Error('Subscription period has already ended. Please create a new subscription.')
    }

    // Reactivate the subscription in Stripe
    const updatedSubscription = await stripe.subscriptions.update(subscription.stripe_subscription_id, {
      cancel_at_period_end: false,
      metadata: {
        ...subscription.metadata,
        reactivated_at: new Date().toISOString(),
        reactivated_by: userId
      }
    })

    // Update subscription in database
    const { error: updateError } = await supabaseClient
      .from('user_subscriptions')
      .update({
        cancel_at_period_end: false,
        canceled_at: null,
        updated_at: new Date().toISOString()
      })
      .eq('id', subscription.id)

    if (updateError) {
      console.error('Error updating subscription in database:', updateError)
      throw new Error('Failed to update subscription record')
    }

    // Send reactivation notification
    await supabaseClient
      .from('notifications')
      .insert({
        user_id: userId,
        type: 'subscription',
        title: 'Subscription Reactivated',
        message: `Your ${subscription.plan.name} subscription has been reactivated and will continue automatically.`,
        data: {
          subscription_id: subscription.id,
          plan_name: subscription.plan.name,
          reactivated_at: new Date().toISOString(),
          next_billing_date: subscription.current_period_end
        }
      })

    // Log the reactivation for analytics
    await supabaseClient
      .from('fraud_detection_logs')
      .insert({
        user_id: userId,
        risk_factors: {
          action: 'subscription_reactivated',
          subscription_id: subscription.stripe_subscription_id,
          plan_id: subscription.plan_id,
          days_before_period_end: Math.ceil((currentPeriodEnd.getTime() - now.getTime()) / (1000 * 60 * 60 * 24))
        },
        risk_score: 0,
        action_taken: 'allow',
        notes: `Subscription reactivated for plan ${subscription.plan.name}`
      })

    // Calculate days remaining in current period
    const daysRemaining = Math.ceil((currentPeriodEnd.getTime() - now.getTime()) / (1000 * 60 * 60 * 24))

    return new Response(
      JSON.stringify({
        success: true,
        message: 'Subscription reactivated successfully',
        subscription: {
          id: updatedSubscription.id,
          status: updatedSubscription.status,
          cancel_at_period_end: updatedSubscription.cancel_at_period_end,
          current_period_end: updatedSubscription.current_period_end,
          plan_name: subscription.plan.name,
          days_remaining: daysRemaining
        }
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      }
    )

  } catch (error: any) {
    console.error('Error reactivating subscription:', error)
    return new Response(
      JSON.stringify({ 
        success: false,
        error: error.message 
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400,
      }
    )
  }
})

// Helper function to check if subscription can be reactivated
async function canReactivateSubscription(subscription: any): Promise<{
  canReactivate: boolean;
  reason?: string;
}> {
  try {
    // Check if subscription is marked for cancellation
    if (!subscription.cancel_at_period_end) {
      return {
        canReactivate: false,
        reason: 'Subscription is not scheduled for cancellation'
      }
    }

    // Check if subscription is still active
    if (subscription.status !== 'active') {
      return {
        canReactivate: false,
        reason: 'Subscription is not currently active'
      }
    }

    // Check if current period has ended
    const currentPeriodEnd = new Date(subscription.current_period_end)
    const now = new Date()

    if (now > currentPeriodEnd) {
      return {
        canReactivate: false,
        reason: 'Current billing period has already ended'
      }
    }

    // Check if there's enough time left (at least 1 day)
    const timeRemaining = currentPeriodEnd.getTime() - now.getTime()
    const daysRemaining = timeRemaining / (1000 * 60 * 60 * 24)

    if (daysRemaining < 1) {
      return {
        canReactivate: false,
        reason: 'Less than 1 day remaining in current period'
      }
    }

    return {
      canReactivate: true
    }

  } catch (error) {
    console.error('Error checking reactivation eligibility:', error)
    return {
      canReactivate: false,
      reason: 'Unable to verify reactivation eligibility'
    }
  }
}

// Helper function to send reactivation confirmation email
async function sendReactivationEmail(userId: string, subscription: any): Promise<void> {
  try {
    // This would integrate with your email service
    // For now, we'll just log it
    console.log(`Reactivation email should be sent to user ${userId} for subscription ${subscription.id}`)
    
    // You could integrate with services like:
    // - SendGrid
    // - Mailgun  
    // - AWS SES
    // - Supabase Auth (if configured for transactional emails)
    
  } catch (error) {
    console.error('Error sending reactivation email:', error)
  }
}
