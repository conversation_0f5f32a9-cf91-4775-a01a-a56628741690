import { useState, useEffect, useCallback, useRef } from 'react';
import { FeatureFlagService } from '../services/FeatureFlagService';
import { ABTestingService } from '../services/ABTestingService';

interface UseFeatureFlagOptions {
  defaultValue?: any;
  refreshInterval?: number;
  trackConversions?: boolean;
}

interface UseFeatureFlagsOptions {
  refreshInterval?: number;
  preloadFlags?: string[];
}

/**
 * Hook for using a single feature flag
 */
export function useFeatureFlag(
  flagName: string,
  options: UseFeatureFlagOptions = {}
) {
  const {
    defaultValue = false,
    refreshInterval = 0,
    trackConversions = false
  } = options;

  const [value, setValue] = useState(defaultValue);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const flagService = FeatureFlagService.getInstance();
  const intervalRef = useRef<NodeJS.Timeout>();

  const evaluateFlag = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const flagValue = await flagService.getFlag(flagName, defaultValue);
      setValue(flagValue);
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Failed to evaluate flag'));
      setValue(defaultValue);
    } finally {
      setLoading(false);
    }
  }, [flagName, defaultValue, flagService]);

  const trackConversion = useCallback(async (conversionValue?: number) => {
    if (!trackConversions) return;
    
    try {
      await flagService.trackConversion(flagName, conversionValue);
    } catch (err) {
      console.error('Error tracking conversion:', err);
    }
  }, [flagName, trackConversions, flagService]);

  const override = useCallback(async (overrideValue: any) => {
    try {
      await flagService.overrideFlag(flagName, overrideValue);
      setValue(overrideValue);
    } catch (err) {
      console.error('Error overriding flag:', err);
    }
  }, [flagName, flagService]);

  const clearOverride = useCallback(async () => {
    try {
      await flagService.clearOverride(flagName);
      await evaluateFlag();
    } catch (err) {
      console.error('Error clearing override:', err);
    }
  }, [flagName, flagService, evaluateFlag]);

  useEffect(() => {
    evaluateFlag();

    // Set up refresh interval if specified
    if (refreshInterval > 0) {
      intervalRef.current = setInterval(evaluateFlag, refreshInterval);
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [evaluateFlag, refreshInterval]);

  return {
    value,
    loading,
    error,
    trackConversion,
    override,
    clearOverride,
    refresh: evaluateFlag
  };
}

/**
 * Hook for using multiple feature flags
 */
export function useFeatureFlags(
  flagNames: string[],
  options: UseFeatureFlagsOptions = {}
) {
  const {
    refreshInterval = 0,
    preloadFlags = []
  } = options;

  const [flags, setFlags] = useState<{ [key: string]: any }>({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const flagService = FeatureFlagService.getInstance();
  const intervalRef = useRef<NodeJS.Timeout>();

  const evaluateFlags = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      
      const allFlags = [...new Set([...flagNames, ...preloadFlags])];
      const flagValues = await flagService.evaluateFlags(allFlags);
      setFlags(flagValues);
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Failed to evaluate flags'));
    } finally {
      setLoading(false);
    }
  }, [flagNames, preloadFlags, flagService]);

  const getFlag = useCallback((flagName: string, defaultValue: any = false) => {
    return flags[flagName] ?? defaultValue;
  }, [flags]);

  const isEnabled = useCallback((flagName: string) => {
    return Boolean(flags[flagName]);
  }, [flags]);

  const trackConversion = useCallback(async (flagName: string, conversionValue?: number) => {
    try {
      await flagService.trackConversion(flagName, conversionValue);
    } catch (err) {
      console.error('Error tracking conversion:', err);
    }
  }, [flagService]);

  useEffect(() => {
    evaluateFlags();

    // Set up refresh interval if specified
    if (refreshInterval > 0) {
      intervalRef.current = setInterval(evaluateFlags, refreshInterval);
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [evaluateFlags, refreshInterval]);

  return {
    flags,
    loading,
    error,
    getFlag,
    isEnabled,
    trackConversion,
    refresh: evaluateFlags
  };
}

/**
 * Hook for boolean feature flags (most common use case)
 */
export function useFeatureEnabled(flagName: string, defaultValue: boolean = false) {
  const { value, loading, error, trackConversion, refresh } = useFeatureFlag(flagName, {
    defaultValue,
    trackConversions: true
  });

  return {
    enabled: Boolean(value),
    loading,
    error,
    trackConversion,
    refresh
  };
}

/**
 * Hook for configuration feature flags (string/number/json values)
 */
export function useFeatureConfig<T = any>(
  flagName: string,
  defaultValue: T
): {
  config: T;
  loading: boolean;
  error: Error | null;
  refresh: () => Promise<void>;
} {
  const { value, loading, error, refresh } = useFeatureFlag(flagName, {
    defaultValue
  });

  return {
    config: value as T,
    loading,
    error,
    refresh
  };
}

/**
 * Hook for A/B testing with automatic conversion tracking
 */
export function useABTest(
  flagName: string,
  variants: { [key: string]: any } = { control: false, variant: true }
) {
  const { value, loading, error, trackConversion, refresh } = useFeatureFlag(flagName, {
    defaultValue: variants.control || false,
    trackConversions: true
  });

  const getVariant = useCallback(() => {
    // Find which variant matches the current value
    for (const [variantName, variantValue] of Object.entries(variants)) {
      if (JSON.stringify(variantValue) === JSON.stringify(value)) {
        return variantName;
      }
    }
    return 'control';
  }, [value, variants]);

  const isVariant = useCallback((variantName: string) => {
    return getVariant() === variantName;
  }, [getVariant]);

  return {
    value,
    variant: getVariant(),
    loading,
    error,
    isVariant,
    trackConversion,
    refresh
  };
}

/**
 * Hook for feature flag overrides (useful for testing/debugging)
 */
export function useFeatureFlagOverride(flagName: string) {
  const [overrideValue, setOverrideValue] = useState<any>(null);
  const [hasOverride, setHasOverride] = useState(false);
  const flagService = FeatureFlagService.getInstance();

  const setOverride = useCallback(async (value: any) => {
    try {
      await flagService.overrideFlag(flagName, value);
      setOverrideValue(value);
      setHasOverride(true);
    } catch (err) {
      console.error('Error setting override:', err);
    }
  }, [flagName, flagService]);

  const clearOverride = useCallback(async () => {
    try {
      await flagService.clearOverride(flagName);
      setOverrideValue(null);
      setHasOverride(false);
    } catch (err) {
      console.error('Error clearing override:', err);
    }
  }, [flagName, flagService]);

  return {
    overrideValue,
    hasOverride,
    setOverride,
    clearOverride
  };
}

/**
 * Hook for getting all feature flags (useful for admin/debug screens)
 */
export function useAllFeatureFlags() {
  const [flags, setFlags] = useState<{ [key: string]: any }>({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const flagService = FeatureFlagService.getInstance();

  const loadAllFlags = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const allFlags = await flagService.getAllFlags();
      setFlags(allFlags);
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Failed to load flags'));
    } finally {
      setLoading(false);
    }
  }, [flagService]);

  const refreshCache = useCallback(async () => {
    try {
      await flagService.refreshCache();
      await loadAllFlags();
    } catch (err) {
      console.error('Error refreshing cache:', err);
    }
  }, [flagService, loadAllFlags]);

  useEffect(() => {
    loadAllFlags();
  }, [loadAllFlags]);

  return {
    flags,
    loading,
    error,
    refresh: loadAllFlags,
    refreshCache
  };
}

/**
 * Provider hook for initializing feature flags
 */
export function useFeatureFlagProvider() {
  const [initialized, setInitialized] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const flagService = FeatureFlagService.getInstance();

  useEffect(() => {
    const initializeService = async () => {
      try {
        await flagService.initialize();
        setInitialized(true);
      } catch (err) {
        setError(err instanceof Error ? err : new Error('Failed to initialize feature flags'));
      }
    };

    initializeService();
  }, [flagService]);

  return {
    initialized,
    error
  };
}
