// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id                String    @id @default(cuid())
  email             String    @unique
  emailVerified     Boolean   @default(false)
  emailVerificationToken String?
  emailVerificationExpires DateTime?
  
  // Password authentication
  passwordHash      String?
  passwordResetToken String?
  passwordResetExpires DateTime?
  passwordChangedAt DateTime?
  
  // Profile information
  firstName         String?
  lastName          String?
  avatar            String?
  dateOfBirth       DateTime?
  phoneNumber       String?
  phoneVerified     Boolean   @default(false)
  
  // Account status
  role              UserRole  @default(USER)
  status            UserStatus @default(ACTIVE)
  isBlocked         Boolean   @default(false)
  blockedReason     String?
  blockedAt         DateTime?
  
  // Two-factor authentication
  twoFactorEnabled  Boolean   @default(false)
  twoFactorSecret   String?
  twoFactorBackupCodes String[]
  
  // OAuth providers
  googleId          String?
  appleId           String?
  
  // Privacy settings
  privacySettings   Json?
  
  // Timestamps
  createdAt         DateTime  @default(now())
  updatedAt         DateTime  @updatedAt
  lastLoginAt       DateTime?
  
  // Relations
  sessions          Session[]
  refreshTokens     RefreshToken[]
  loginAttempts     LoginAttempt[]
  auditLogs         AuditLog[]
  
  @@map("users")
}

model Session {
  id            String    @id @default(cuid())
  userId        String
  sessionToken  String    @unique
  deviceInfo    String?
  ipAddress     String?
  userAgent     String?
  location      String?
  isActive      Boolean   @default(true)
  expiresAt     DateTime
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt
  
  user          User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  @@map("sessions")
}

model RefreshToken {
  id            String    @id @default(cuid())
  userId        String
  token         String    @unique
  deviceInfo    String?
  ipAddress     String?
  userAgent     String?
  isRevoked     Boolean   @default(false)
  revokedAt     DateTime?
  expiresAt     DateTime
  createdAt     DateTime  @default(now())
  
  user          User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  @@map("refresh_tokens")
}

model LoginAttempt {
  id            String    @id @default(cuid())
  userId        String?
  email         String
  ipAddress     String
  userAgent     String?
  success       Boolean
  failureReason String?
  createdAt     DateTime  @default(now())
  
  user          User?     @relation(fields: [userId], references: [id], onDelete: SetNull)
  
  @@map("login_attempts")
}

model AuditLog {
  id            String    @id @default(cuid())
  userId        String?
  action        String
  resource      String?
  resourceId    String?
  details       Json?
  ipAddress     String?
  userAgent     String?
  createdAt     DateTime  @default(now())
  
  user          User?     @relation(fields: [userId], references: [id], onDelete: SetNull)
  
  @@map("audit_logs")
}

model BlacklistedToken {
  id            String    @id @default(cuid())
  token         String    @unique
  tokenType     TokenType
  expiresAt     DateTime
  createdAt     DateTime  @default(now())
  
  @@map("blacklisted_tokens")
}

enum UserRole {
  USER
  PREMIUM
  ADMIN
  SUPER_ADMIN
}

enum UserStatus {
  ACTIVE
  INACTIVE
  SUSPENDED
  DELETED
}

enum TokenType {
  ACCESS
  REFRESH
}
