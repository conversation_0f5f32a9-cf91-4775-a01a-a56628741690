import { supabase } from '../lib/supabase';
import * as FileSystem from 'expo-file-system';
import * as ImagePicker from 'expo-image-picker';
import * as ImageManipulator from 'expo-image-manipulator';
import { decode } from 'base64-arraybuffer';

export interface MediaUpload {
  id: string;
  user_id: string;
  file_name: string;
  file_size: number | null;
  mime_type: string | null;
  media_type: 'image' | 'video' | 'audio';
  storage_path: string;
  public_url: string | null;
  thumbnail_url: string | null;
  metadata: any;
  upload_completed: boolean;
  created_at: string;
}

export interface UploadProgress {
  loaded: number;
  total: number;
  percentage: number;
}

export interface ImageProcessingOptions {
  maxWidth?: number;
  maxHeight?: number;
  quality?: number;
  format?: 'jpeg' | 'png' | 'webp';
  generateThumbnail?: boolean;
  thumbnailSize?: number;
}

export class MediaService {
  private readonly MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
  private readonly SUPPORTED_IMAGE_TYPES = ['image/jpeg', 'image/png', 'image/webp'];
  private readonly SUPPORTED_VIDEO_TYPES = ['video/mp4', 'video/quicktime'];

  /**
   * Request camera permissions
   */
  async requestCameraPermissions(): Promise<boolean> {
    try {
      const { status } = await ImagePicker.requestCameraPermissionsAsync();
      return status === 'granted';
    } catch (error) {
      console.error('Error requesting camera permissions:', error);
      return false;
    }
  }

  /**
   * Request media library permissions
   */
  async requestMediaLibraryPermissions(): Promise<boolean> {
    try {
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
      return status === 'granted';
    } catch (error) {
      console.error('Error requesting media library permissions:', error);
      return false;
    }
  }

  /**
   * Take photo with camera
   */
  async takePhoto(options: ImageProcessingOptions = {}): Promise<MediaUpload | null> {
    try {
      const hasPermission = await this.requestCameraPermissions();
      if (!hasPermission) {
        throw new Error('Camera permission denied');
      }

      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [4, 3],
        quality: options.quality || 0.8,
      });

      if (result.canceled || !result.assets[0]) {
        return null;
      }

      return await this.processAndUploadImage(result.assets[0], options);
    } catch (error) {
      console.error('Error taking photo:', error);
      throw error;
    }
  }

  /**
   * Pick image from gallery
   */
  async pickImage(options: ImageProcessingOptions = {}): Promise<MediaUpload | null> {
    try {
      const hasPermission = await this.requestMediaLibraryPermissions();
      if (!hasPermission) {
        throw new Error('Media library permission denied');
      }

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [4, 3],
        quality: options.quality || 0.8,
      });

      if (result.canceled || !result.assets[0]) {
        return null;
      }

      return await this.processAndUploadImage(result.assets[0], options);
    } catch (error) {
      console.error('Error picking image:', error);
      throw error;
    }
  }

  /**
   * Process and upload image
   */
  private async processAndUploadImage(
    asset: ImagePicker.ImagePickerAsset,
    options: ImageProcessingOptions = {}
  ): Promise<MediaUpload> {
    try {
      // Validate file size
      const fileInfo = await FileSystem.getInfoAsync(asset.uri);
      if (fileInfo.exists && fileInfo.size && fileInfo.size > this.MAX_FILE_SIZE) {
        throw new Error('File size exceeds 10MB limit');
      }

      // Process image (resize, compress, etc.)
      const processedImage = await this.processImage(asset, options);
      
      // Generate thumbnail if requested
      let thumbnailUri: string | null = null;
      if (options.generateThumbnail !== false) {
        thumbnailUri = await this.generateThumbnail(processedImage.uri, options.thumbnailSize || 200);
      }

      // Upload to Supabase Storage
      const uploadResult = await this.uploadToStorage(processedImage, thumbnailUri);

      return uploadResult;
    } catch (error) {
      console.error('Error processing and uploading image:', error);
      throw error;
    }
  }

  /**
   * Process image (resize, compress, format conversion)
   */
  private async processImage(
    asset: ImagePicker.ImagePickerAsset,
    options: ImageProcessingOptions
  ): Promise<ImageManipulator.ImageResult> {
    try {
      const manipulateOptions: ImageManipulator.Action[] = [];

      // Resize if dimensions are specified
      if (options.maxWidth || options.maxHeight) {
        manipulateOptions.push({
          resize: {
            width: options.maxWidth,
            height: options.maxHeight,
          },
        });
      }

      // Auto-compress large images
      if (asset.width && asset.height && (asset.width > 1920 || asset.height > 1920)) {
        manipulateOptions.push({
          resize: {
            width: Math.min(asset.width, 1920),
            height: Math.min(asset.height, 1920),
          },
        });
      }

      const result = await ImageManipulator.manipulateAsync(
        asset.uri,
        manipulateOptions,
        {
          compress: options.quality || 0.8,
          format: options.format === 'png' ? ImageManipulator.SaveFormat.PNG : ImageManipulator.SaveFormat.JPEG,
        }
      );

      return result;
    } catch (error) {
      console.error('Error processing image:', error);
      throw error;
    }
  }

  /**
   * Generate thumbnail
   */
  private async generateThumbnail(imageUri: string, size: number = 200): Promise<string> {
    try {
      const result = await ImageManipulator.manipulateAsync(
        imageUri,
        [
          {
            resize: {
              width: size,
              height: size,
            },
          },
        ],
        {
          compress: 0.7,
          format: ImageManipulator.SaveFormat.JPEG,
        }
      );

      return result.uri;
    } catch (error) {
      console.error('Error generating thumbnail:', error);
      throw error;
    }
  }

  /**
   * Upload file to Supabase Storage
   */
  private async uploadToStorage(
    processedImage: ImageManipulator.ImageResult,
    thumbnailUri: string | null
  ): Promise<MediaUpload> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        throw new Error('User not authenticated');
      }

      // Read file as base64
      const base64 = await FileSystem.readAsStringAsync(processedImage.uri, {
        encoding: FileSystem.EncodingType.Base64,
      });

      // Generate unique filename
      const timestamp = Date.now();
      const fileName = `${timestamp}.jpg`;
      const filePath = `${user.id}/${fileName}`;

      // Upload main image
      const { data: uploadData, error: uploadError } = await supabase.storage
        .from('user-uploads')
        .upload(filePath, decode(base64), {
          contentType: 'image/jpeg',
          upsert: false,
        });

      if (uploadError) {
        throw uploadError;
      }

      // Get public URL
      const { data: urlData } = supabase.storage
        .from('user-uploads')
        .getPublicUrl(filePath);

      let thumbnailUrl: string | null = null;

      // Upload thumbnail if generated
      if (thumbnailUri) {
        const thumbnailBase64 = await FileSystem.readAsStringAsync(thumbnailUri, {
          encoding: FileSystem.EncodingType.Base64,
        });

        const thumbnailPath = `${user.id}/thumb_${fileName}`;
        
        const { error: thumbnailError } = await supabase.storage
          .from('thumbnails')
          .upload(thumbnailPath, decode(thumbnailBase64), {
            contentType: 'image/jpeg',
            upsert: false,
          });

        if (!thumbnailError) {
          const { data: thumbnailUrlData } = supabase.storage
            .from('thumbnails')
            .getPublicUrl(thumbnailPath);
          thumbnailUrl = thumbnailUrlData.publicUrl;
        }
      }

      // Get file info
      const fileInfo = await FileSystem.getInfoAsync(processedImage.uri);
      const fileSize = fileInfo.exists ? fileInfo.size || 0 : 0;

      // Save media record to database
      const { data: mediaRecord, error: dbError } = await supabase
        .from('media_uploads')
        .insert({
          user_id: user.id,
          file_name: fileName,
          file_size: fileSize,
          mime_type: 'image/jpeg',
          media_type: 'image',
          storage_path: filePath,
          public_url: urlData.publicUrl,
          thumbnail_url: thumbnailUrl,
          metadata: {
            width: processedImage.width,
            height: processedImage.height,
            original_uri: processedImage.uri,
          },
          upload_completed: true,
        })
        .select()
        .single();

      if (dbError) {
        throw dbError;
      }

      return mediaRecord;
    } catch (error) {
      console.error('Error uploading to storage:', error);
      throw error;
    }
  }

  /**
   * Get user's media uploads
   */
  async getUserMedia(limit: number = 20, offset: number = 0): Promise<MediaUpload[]> {
    try {
      const { data, error } = await supabase
        .from('media_uploads')
        .select('*')
        .eq('upload_completed', true)
        .order('created_at', { ascending: false })
        .range(offset, offset + limit - 1);

      if (error) {
        throw error;
      }

      return data || [];
    } catch (error) {
      console.error('Error fetching user media:', error);
      throw error;
    }
  }

  /**
   * Delete media upload
   */
  async deleteMedia(mediaId: string): Promise<void> {
    try {
      // Get media record
      const { data: media, error: fetchError } = await supabase
        .from('media_uploads')
        .select('*')
        .eq('id', mediaId)
        .single();

      if (fetchError) {
        throw fetchError;
      }

      // Delete from storage
      const { error: storageError } = await supabase.storage
        .from('user-uploads')
        .remove([media.storage_path]);

      if (storageError) {
        console.error('Error deleting from storage:', storageError);
      }

      // Delete thumbnail if exists
      if (media.thumbnail_url) {
        const thumbnailPath = media.storage_path.replace(/^.*\//, '').replace(/^/, 'thumb_');
        await supabase.storage
          .from('thumbnails')
          .remove([`${media.user_id}/${thumbnailPath}`]);
      }

      // Delete database record
      const { error: dbError } = await supabase
        .from('media_uploads')
        .delete()
        .eq('id', mediaId);

      if (dbError) {
        throw dbError;
      }
    } catch (error) {
      console.error('Error deleting media:', error);
      throw error;
    }
  }
}
