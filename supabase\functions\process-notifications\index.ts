import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

const supabaseClient = createClient(
  Deno.env.get('SUPABASE_URL') ?? '',
  Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
)

const EXPO_ACCESS_TOKEN = Deno.env.get('EXPO_ACCESS_TOKEN')

interface NotificationPayload {
  to: string;
  title: string;
  body: string;
  data?: any;
  sound?: string;
  badge?: number;
  priority?: string;
  channelId?: string;
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const { action, data } = await req.json()

    switch (action) {
      case 'process_queue':
        return await processNotificationQueue()
      case 'send_daily_tips':
        return await sendDailyTips()
      case 'send_achievement':
        return await sendAchievementNotification(data)
      case 'send_reminder':
        return await sendReminderNotification(data)
      case 'update_analytics':
        return await updateNotificationAnalytics()
      default:
        throw new Error('Invalid action')
    }

  } catch (error: any) {
    console.error('Error processing notifications:', error)
    return new Response(
      JSON.stringify({ error: error.message }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400,
      }
    )
  }
})

async function processNotificationQueue() {
  try {
    // Get pending notifications that are ready to be sent
    const { data: notifications, error } = await supabaseClient
      .from('notification_queue')
      .select(`
        *,
        user:user_id (
          id,
          email
        ),
        template:template_id (
          category,
          ab_test_enabled,
          ab_test_group
        )
      `)
      .eq('status', 'pending')
      .lte('scheduled_for', new Date().toISOString())
      .limit(100)

    if (error) {
      throw error
    }

    if (!notifications || notifications.length === 0) {
      return new Response(
        JSON.stringify({ message: 'No notifications to process', count: 0 }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 200,
        }
      )
    }

    let processedCount = 0
    let failedCount = 0

    for (const notification of notifications) {
      try {
        // Check if notification has expired
        if (notification.expires_at && new Date(notification.expires_at) < new Date()) {
          await markNotificationExpired(notification.id)
          continue
        }

        // Check user preferences and quiet hours
        const canSend = await checkUserPreferences(notification.user_id, notification.template?.category)
        if (!canSend) {
          await markNotificationCancelled(notification.id, 'User preferences')
          continue
        }

        // Get user's push tokens
        const { data: tokens } = await supabaseClient
          .from('push_notification_tokens')
          .select('*')
          .eq('user_id', notification.user_id)
          .eq('is_active', true)

        if (!tokens || tokens.length === 0) {
          await markNotificationFailed(notification.id, 'No active push tokens')
          failedCount++
          continue
        }

        // Send to each device
        for (const token of tokens) {
          const success = await sendPushNotification(notification, token.token)
          
          // Log delivery attempt
          await logDeliveryAttempt(notification, token, success)
        }

        // Mark notification as sent
        await markNotificationSent(notification.id)
        processedCount++

      } catch (error) {
        console.error(`Error processing notification ${notification.id}:`, error)
        await markNotificationFailed(notification.id, error.message)
        failedCount++
      }
    }

    return new Response(
      JSON.stringify({ 
        message: 'Notifications processed',
        processed: processedCount,
        failed: failedCount,
        total: notifications.length
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      }
    )

  } catch (error: any) {
    console.error('Error in processNotificationQueue:', error)
    throw error
  }
}

async function sendPushNotification(notification: any, pushToken: string): Promise<boolean> {
  try {
    const payload: NotificationPayload = {
      to: pushToken,
      title: notification.title,
      body: notification.body,
      data: {
        ...notification.custom_data,
        notificationId: notification.id,
        actionUrl: notification.action_url
      },
      sound: 'default',
      priority: notification.priority || 'normal'
    }

    const response = await fetch('https://exp.host/--/api/v2/push/send', {
      method: 'POST',
      headers: {
        'Accept': 'application/json',
        'Accept-encoding': 'gzip, deflate',
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${EXPO_ACCESS_TOKEN}`
      },
      body: JSON.stringify(payload)
    })

    const result = await response.json()
    
    if (result.data && result.data.status === 'ok') {
      return true
    } else {
      console.error('Push notification failed:', result)
      return false
    }

  } catch (error) {
    console.error('Error sending push notification:', error)
    return false
  }
}

async function sendDailyTips() {
  try {
    // Get users who have daily tips enabled
    const { data: users, error } = await supabaseClient
      .from('user_notification_preferences')
      .select('user_id, timezone')
      .eq('notifications_enabled', true)
      .eq('push_enabled', true)
      .eq('daily_tips_enabled', true)

    if (error) {
      throw error
    }

    // Get daily tip template
    const { data: template } = await supabaseClient
      .from('notification_templates')
      .select('*')
      .eq('name', 'daily_nature_tip')
      .eq('is_active', true)
      .single()

    if (!template) {
      throw new Error('Daily tip template not found')
    }

    // Get random nature tip
    const natureTips = [
      "Many plants can communicate with each other through underground fungal networks called mycorrhizae!",
      "A single tree can be home to over 400 species of insects!",
      "Some flowers can change color to signal to pollinators whether they have nectar available.",
      "Birds can see ultraviolet light, revealing hidden patterns on flowers invisible to human eyes.",
      "Mushrooms are more closely related to animals than to plants!"
    ]

    const randomTip = natureTips[Math.floor(Math.random() * natureTips.length)]

    let sentCount = 0

    for (const user of users || []) {
      // Check if it's the right time for this user based on their timezone
      const userTime = new Date().toLocaleString('en-US', { timeZone: user.timezone || 'UTC' })
      const hour = new Date(userTime).getHours()

      // Send between 8 AM and 10 AM in user's timezone
      if (hour >= 8 && hour <= 10) {
        await supabaseClient
          .from('notification_queue')
          .insert({
            user_id: user.user_id,
            template_id: template.id,
            title: template.title_template,
            body: template.body_template.replace('{{tip_content}}', randomTip),
            custom_data: { tip_content: randomTip },
            delivery_method: 'push',
            priority: 'normal'
          })

        sentCount++
      }
    }

    return new Response(
      JSON.stringify({ 
        message: 'Daily tips queued',
        count: sentCount
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      }
    )

  } catch (error: any) {
    console.error('Error sending daily tips:', error)
    throw error
  }
}

async function sendAchievementNotification(data: any) {
  try {
    const { userId, achievementType, achievementData } = data

    // Get achievement template
    const templateName = achievementType === 'streak' ? 'identification_streak' : 'new_species_discovered'
    
    const { data: template } = await supabaseClient
      .from('notification_templates')
      .select('*')
      .eq('name', templateName)
      .eq('is_active', true)
      .single()

    if (!template) {
      throw new Error('Achievement template not found')
    }

    // Personalize content
    let title = template.title_template
    let body = template.body_template

    Object.keys(achievementData).forEach(key => {
      const placeholder = `{{${key}}}`
      title = title.replace(new RegExp(placeholder, 'g'), achievementData[key])
      body = body.replace(new RegExp(placeholder, 'g'), achievementData[key])
    })

    // Queue notification
    await supabaseClient
      .from('notification_queue')
      .insert({
        user_id: userId,
        template_id: template.id,
        title,
        body,
        custom_data: achievementData,
        delivery_method: 'push',
        priority: 'high'
      })

    return new Response(
      JSON.stringify({ message: 'Achievement notification queued' }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      }
    )

  } catch (error: any) {
    console.error('Error sending achievement notification:', error)
    throw error
  }
}

async function sendReminderNotification(data: any) {
  try {
    const { userId, reminderType, reminderData } = data

    const { data: template } = await supabaseClient
      .from('notification_templates')
      .select('*')
      .eq('name', 'location_reminder')
      .eq('is_active', true)
      .single()

    if (!template) {
      throw new Error('Reminder template not found')
    }

    // Personalize content
    let title = template.title_template
    let body = template.body_template

    Object.keys(reminderData).forEach(key => {
      const placeholder = `{{${key}}}`
      title = title.replace(new RegExp(placeholder, 'g'), reminderData[key])
      body = body.replace(new RegExp(placeholder, 'g'), reminderData[key])
    })

    await supabaseClient
      .from('notification_queue')
      .insert({
        user_id: userId,
        template_id: template.id,
        title,
        body,
        custom_data: reminderData,
        delivery_method: 'push',
        priority: 'normal'
      })

    return new Response(
      JSON.stringify({ message: 'Reminder notification queued' }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      }
    )

  } catch (error: any) {
    console.error('Error sending reminder notification:', error)
    throw error
  }
}

// Helper functions
async function checkUserPreferences(userId: string, category: string): Promise<boolean> {
  try {
    const { data: prefs } = await supabaseClient
      .from('user_notification_preferences')
      .select('*')
      .eq('user_id', userId)
      .single()

    if (!prefs || !prefs.notifications_enabled || !prefs.push_enabled) {
      return false
    }

    // Check category-specific preferences
    const categoryMap: { [key: string]: string } = {
      'daily_tip': 'daily_tips_enabled',
      'achievement': 'achievements_enabled',
      'reminder': 'reminders_enabled',
      'social': 'social_enabled',
      'system': 'system_enabled',
      'marketing': 'marketing_enabled',
      'educational': 'educational_enabled'
    }

    const prefKey = categoryMap[category]
    if (prefKey && !prefs[prefKey]) {
      return false
    }

    // Check quiet hours
    if (prefs.quiet_hours_enabled) {
      const now = new Date()
      const userTime = new Date().toLocaleString('en-US', { timeZone: prefs.timezone || 'UTC' })
      const hour = new Date(userTime).getHours()
      
      const quietStart = parseInt(prefs.quiet_hours_start.split(':')[0])
      const quietEnd = parseInt(prefs.quiet_hours_end.split(':')[0])
      
      if (quietStart > quietEnd) {
        // Quiet hours span midnight
        if (hour >= quietStart || hour < quietEnd) {
          return false
        }
      } else {
        // Normal quiet hours
        if (hour >= quietStart && hour < quietEnd) {
          return false
        }
      }
    }

    return true
  } catch (error) {
    console.error('Error checking user preferences:', error)
    return false
  }
}

async function markNotificationSent(notificationId: string) {
  await supabaseClient
    .from('notification_queue')
    .update({
      status: 'sent',
      sent_at: new Date().toISOString()
    })
    .eq('id', notificationId)
}

async function markNotificationFailed(notificationId: string, errorMessage: string) {
  await supabaseClient
    .from('notification_queue')
    .update({
      status: 'failed',
      error_message: errorMessage
    })
    .eq('id', notificationId)
}

async function markNotificationExpired(notificationId: string) {
  await supabaseClient
    .from('notification_queue')
    .update({ status: 'expired' })
    .eq('id', notificationId)
}

async function markNotificationCancelled(notificationId: string, reason: string) {
  await supabaseClient
    .from('notification_queue')
    .update({
      status: 'cancelled',
      error_message: reason
    })
    .eq('id', notificationId)
}

async function logDeliveryAttempt(notification: any, token: any, success: boolean) {
  await supabaseClient
    .from('notification_delivery_log')
    .insert({
      queue_id: notification.id,
      user_id: notification.user_id,
      template_id: notification.template_id,
      delivery_method: 'push',
      platform: token.platform,
      token: token.token,
      status: success ? 'sent' : 'failed'
    })
}

async function updateNotificationAnalytics() {
  // This would be called periodically to update analytics
  // Implementation would aggregate delivery logs into analytics table
  return new Response(
    JSON.stringify({ message: 'Analytics updated' }),
    {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 200,
    }
  )
}
