import { supabase } from '../lib/supabase';
import * as Location from 'expo-location';

export interface SpeciesIdentification {
  id: string;
  user_id: string;
  media_id: string;
  species_id: string | null;
  confidence_score: number | null;
  status: 'pending' | 'completed' | 'failed';
  ai_response: any | null;
  alternative_suggestions: any;
  location_data: any | null;
  weather_data: any | null;
  user_notes: string | null;
  is_verified: boolean;
  verified_by: string | null;
  verified_at: string | null;
  created_at: string;
  updated_at: string;
}

export interface IdentificationResult {
  species: {
    id: string;
    common_name: string;
    scientific_name: string;
    description: string;
    habitat: string;
    conservation_status: string;
    physical_traits: any;
    interesting_facts: string[];
    image_urls: string[];
  } | null;
  confidence_score: number;
  alternative_suggestions: Array<{
    species_id: string;
    common_name: string;
    scientific_name: string;
    confidence_score: number;
  }>;
  ai_explanation: string;
  identification_tips: string[];
}

export interface LocationData {
  latitude: number;
  longitude: number;
  accuracy: number | null;
  address: string | null;
  timestamp: string;
}

export class IdentificationService {
  private readonly GEMINI_API_KEY = process.env.EXPO_PUBLIC_GEMINI_API_KEY;
  private readonly GEMINI_API_URL = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-pro-vision:generateContent';

  /**
   * Identify species from image
   */
  async identifySpecies(
    mediaId: string,
    includeLocation: boolean = true,
    userNotes?: string
  ): Promise<SpeciesIdentification> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        throw new Error('User not authenticated');
      }

      // Get media upload details
      const { data: media, error: mediaError } = await supabase
        .from('media_uploads')
        .select('*')
        .eq('id', mediaId)
        .single();

      if (mediaError || !media) {
        throw new Error('Media not found');
      }

      // Get location data if requested
      let locationData: LocationData | null = null;
      if (includeLocation) {
        locationData = await this.getCurrentLocation();
      }

      // Create identification record
      const { data: identification, error: createError } = await supabase
        .from('species_identifications')
        .insert({
          user_id: user.id,
          media_id: mediaId,
          status: 'pending',
          location_data: locationData,
          user_notes: userNotes,
        })
        .select()
        .single();

      if (createError) {
        throw createError;
      }

      // Process identification in background
      this.processIdentification(identification.id, media.public_url);

      return identification;
    } catch (error) {
      console.error('Error starting species identification:', error);
      throw error;
    }
  }

  /**
   * Process identification using AI
   */
  private async processIdentification(identificationId: string, imageUrl: string): Promise<void> {
    try {
      // Call Gemini API for species identification
      const aiResult = await this.callGeminiAPI(imageUrl);
      
      // Find matching species in database
      const matchedSpecies = await this.findMatchingSpecies(aiResult);
      
      // Update identification record
      const { error: updateError } = await supabase
        .from('species_identifications')
        .update({
          species_id: matchedSpecies?.species_id || null,
          confidence_score: aiResult.confidence_score,
          status: 'completed',
          ai_response: aiResult,
          alternative_suggestions: aiResult.alternative_suggestions,
        })
        .eq('id', identificationId);

      if (updateError) {
        throw updateError;
      }

      // Create notification for user
      await this.createIdentificationNotification(identificationId, matchedSpecies !== null);

    } catch (error) {
      console.error('Error processing identification:', error);
      
      // Mark identification as failed
      await supabase
        .from('species_identifications')
        .update({
          status: 'failed',
          ai_response: { error: error.message },
        })
        .eq('id', identificationId);
    }
  }

  /**
   * Call Gemini API for species identification
   */
  private async callGeminiAPI(imageUrl: string): Promise<any> {
    try {
      if (!this.GEMINI_API_KEY) {
        throw new Error('Gemini API key not configured');
      }

      const prompt = `
        Analyze this image and identify the species shown. Please provide:
        1. The most likely species (common name and scientific name)
        2. Confidence level (0-100)
        3. Key identifying features visible in the image
        4. Up to 3 alternative species possibilities with confidence levels
        5. Brief explanation of the identification
        6. Tips for distinguishing this species from similar ones

        Format your response as JSON with the following structure:
        {
          "primary_identification": {
            "common_name": "string",
            "scientific_name": "string",
            "confidence_score": number
          },
          "key_features": ["string"],
          "alternative_suggestions": [
            {
              "common_name": "string",
              "scientific_name": "string",
              "confidence_score": number
            }
          ],
          "explanation": "string",
          "identification_tips": ["string"]
        }
      `;

      const response = await fetch(`${this.GEMINI_API_URL}?key=${this.GEMINI_API_KEY}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          contents: [
            {
              parts: [
                {
                  text: prompt,
                },
                {
                  inline_data: {
                    mime_type: 'image/jpeg',
                    data: await this.getImageBase64(imageUrl),
                  },
                },
              ],
            },
          ],
        }),
      });

      if (!response.ok) {
        throw new Error(`Gemini API error: ${response.statusText}`);
      }

      const data = await response.json();
      const aiText = data.candidates[0]?.content?.parts[0]?.text;
      
      if (!aiText) {
        throw new Error('No response from Gemini API');
      }

      // Parse JSON response
      const jsonMatch = aiText.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('Invalid JSON response from Gemini API');
      }

      return JSON.parse(jsonMatch[0]);
    } catch (error) {
      console.error('Error calling Gemini API:', error);
      throw error;
    }
  }

  /**
   * Get image as base64 string
   */
  private async getImageBase64(imageUrl: string): Promise<string> {
    try {
      const response = await fetch(imageUrl);
      const blob = await response.blob();
      
      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onloadend = () => {
          const base64 = (reader.result as string).split(',')[1];
          resolve(base64);
        };
        reader.onerror = reject;
        reader.readAsDataURL(blob);
      });
    } catch (error) {
      console.error('Error converting image to base64:', error);
      throw error;
    }
  }

  /**
   * Find matching species in database
   */
  private async findMatchingSpecies(aiResult: any): Promise<{ species_id: string } | null> {
    try {
      const primaryId = aiResult.primary_identification;
      if (!primaryId) return null;

      // Search by scientific name first (most accurate)
      let { data: species } = await supabase
        .from('species')
        .select('id')
        .ilike('scientific_name', primaryId.scientific_name)
        .limit(1);

      if (species && species.length > 0) {
        return { species_id: species[0].id };
      }

      // Search by common name
      ({ data: species } = await supabase
        .from('species')
        .select('id')
        .ilike('common_name', primaryId.common_name)
        .limit(1));

      if (species && species.length > 0) {
        return { species_id: species[0].id };
      }

      return null;
    } catch (error) {
      console.error('Error finding matching species:', error);
      return null;
    }
  }

  /**
   * Get current location
   */
  private async getCurrentLocation(): Promise<LocationData | null> {
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status !== 'granted') {
        console.log('Location permission denied');
        return null;
      }

      const location = await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.Balanced,
      });

      // Get address from coordinates
      let address: string | null = null;
      try {
        const reverseGeocode = await Location.reverseGeocodeAsync({
          latitude: location.coords.latitude,
          longitude: location.coords.longitude,
        });

        if (reverseGeocode.length > 0) {
          const addr = reverseGeocode[0];
          address = `${addr.city}, ${addr.region}, ${addr.country}`;
        }
      } catch (geocodeError) {
        console.log('Reverse geocoding failed:', geocodeError);
      }

      return {
        latitude: location.coords.latitude,
        longitude: location.coords.longitude,
        accuracy: location.coords.accuracy,
        address,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      console.error('Error getting location:', error);
      return null;
    }
  }

  /**
   * Create notification for completed identification
   */
  private async createIdentificationNotification(
    identificationId: string,
    wasSuccessful: boolean
  ): Promise<void> {
    try {
      const { data: identification } = await supabase
        .from('species_identifications')
        .select('user_id')
        .eq('id', identificationId)
        .single();

      if (!identification) return;

      const title = wasSuccessful 
        ? 'Species Identified!' 
        : 'Identification Complete';
      
      const message = wasSuccessful
        ? 'We\'ve successfully identified the species in your photo. Tap to view details!'
        : 'We\'ve analyzed your photo. Tap to view the results.';

      await supabase
        .from('notifications')
        .insert({
          user_id: identification.user_id,
          type: 'identification_complete',
          title,
          message,
          data: { identification_id: identificationId },
        });
    } catch (error) {
      console.error('Error creating notification:', error);
    }
  }

  /**
   * Get user's identification history
   */
  async getIdentificationHistory(limit: number = 20, offset: number = 0): Promise<SpeciesIdentification[]> {
    try {
      const { data, error } = await supabase
        .from('species_identifications')
        .select(`
          *,
          species:species_id (
            id,
            common_name,
            scientific_name,
            description,
            image_urls
          ),
          media:media_id (
            id,
            public_url,
            thumbnail_url
          )
        `)
        .order('created_at', { ascending: false })
        .range(offset, offset + limit - 1);

      if (error) {
        throw error;
      }

      return data || [];
    } catch (error) {
      console.error('Error fetching identification history:', error);
      throw error;
    }
  }

  /**
   * Get identification by ID
   */
  async getIdentification(identificationId: string): Promise<SpeciesIdentification | null> {
    try {
      const { data, error } = await supabase
        .from('species_identifications')
        .select(`
          *,
          species:species_id (
            *,
            category:category_id (
              name,
              color_hex
            )
          ),
          media:media_id (
            *
          )
        `)
        .eq('id', identificationId)
        .single();

      if (error) {
        throw error;
      }

      return data;
    } catch (error) {
      console.error('Error fetching identification:', error);
      throw error;
    }
  }

  /**
   * Add user notes to identification
   */
  async addUserNotes(identificationId: string, notes: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('species_identifications')
        .update({ user_notes: notes })
        .eq('id', identificationId);

      if (error) {
        throw error;
      }
    } catch (error) {
      console.error('Error adding user notes:', error);
      throw error;
    }
  }
}
