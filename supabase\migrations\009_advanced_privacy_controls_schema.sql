-- Advanced Privacy Controls Schema
-- This migration creates comprehensive privacy controls beyond basic GDPR compliance

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Privacy Consent Management Table
CREATE TABLE IF NOT EXISTS privacy_consents (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    
    -- Consent details
    consent_type VARCHAR(50) NOT NULL CHECK (consent_type IN (
        'data_collection', 'data_processing', 'data_sharing', 'marketing', 
        'analytics', 'location_tracking', 'biometric_data', 'research_participation',
        'third_party_sharing', 'automated_decision_making', 'profiling'
    )),
    consent_status BOOLEAN NOT NULL,
    consent_version VARCHAR(20) NOT NULL DEFAULT '1.0',
    
    -- Legal basis
    legal_basis VARCHAR(50) NOT NULL CHECK (legal_basis IN (
        'consent', 'contract', 'legal_obligation', 'vital_interests', 
        'public_task', 'legitimate_interests'
    )),
    
    -- Consent context
    consent_source VARCHAR(50) NOT NULL CHECK (consent_source IN (
        'registration', 'settings', 'feature_prompt', 'privacy_center', 'api'
    )),
    consent_method VARCHAR(30) NOT NULL CHECK (consent_method IN (
        'explicit_opt_in', 'implicit_consent', 'pre_checked', 'granular_choice'
    )),
    
    -- Metadata
    ip_address INET,
    user_agent TEXT,
    device_fingerprint VARCHAR(255),
    geolocation JSONB,
    
    -- Expiry and withdrawal
    expires_at TIMESTAMP WITH TIME ZONE,
    withdrawn_at TIMESTAMP WITH TIME ZONE,
    withdrawal_reason TEXT,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Data Processing Activities Table
CREATE TABLE IF NOT EXISTS data_processing_activities (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    
    -- Processing details
    activity_type VARCHAR(50) NOT NULL CHECK (activity_type IN (
        'data_collection', 'data_analysis', 'profiling', 'automated_decision',
        'data_sharing', 'data_transfer', 'data_enrichment', 'ml_training'
    )),
    data_categories TEXT[] NOT NULL,
    processing_purpose TEXT NOT NULL,
    
    -- Legal and technical details
    legal_basis VARCHAR(50) NOT NULL,
    retention_period INTERVAL,
    data_source VARCHAR(100),
    data_destination VARCHAR(100),
    
    -- Security and compliance
    encryption_status BOOLEAN DEFAULT true,
    anonymization_level VARCHAR(20) CHECK (anonymization_level IN ('none', 'pseudonymized', 'anonymized')),
    compliance_frameworks TEXT[] DEFAULT '{}',
    
    -- Tracking
    processing_start TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    processing_end TIMESTAMP WITH TIME ZONE,
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'paused', 'completed', 'cancelled')),
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Privacy Preferences Table (Advanced)
CREATE TABLE IF NOT EXISTS advanced_privacy_preferences (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE UNIQUE,
    
    -- Data minimization preferences
    data_minimization_enabled BOOLEAN NOT NULL DEFAULT true,
    auto_delete_enabled BOOLEAN NOT NULL DEFAULT false,
    auto_delete_period INTERVAL DEFAULT '2 years',
    
    -- Sharing and third-party preferences
    allow_third_party_sharing BOOLEAN NOT NULL DEFAULT false,
    allowed_third_parties TEXT[] DEFAULT '{}',
    data_sharing_purposes TEXT[] DEFAULT '{}',
    
    -- Analytics and tracking preferences
    allow_behavioral_analytics BOOLEAN NOT NULL DEFAULT false,
    allow_cross_device_tracking BOOLEAN NOT NULL DEFAULT false,
    allow_fingerprinting BOOLEAN NOT NULL DEFAULT false,
    allow_profiling BOOLEAN NOT NULL DEFAULT false,
    
    -- Biometric and sensitive data
    allow_biometric_processing BOOLEAN NOT NULL DEFAULT false,
    biometric_retention_period INTERVAL DEFAULT '30 days',
    allow_health_data_inference BOOLEAN NOT NULL DEFAULT false,
    
    -- Location privacy (granular)
    location_precision_level VARCHAR(20) DEFAULT 'city' CHECK (location_precision_level IN ('exact', 'neighborhood', 'city', 'region', 'country', 'none')),
    location_history_retention INTERVAL DEFAULT '90 days',
    allow_location_inference BOOLEAN NOT NULL DEFAULT false,
    
    -- Communication preferences
    preferred_contact_method VARCHAR(20) DEFAULT 'email' CHECK (preferred_contact_method IN ('email', 'sms', 'push', 'in_app', 'none')),
    communication_frequency VARCHAR(20) DEFAULT 'normal' CHECK (communication_frequency IN ('minimal', 'normal', 'frequent')),
    
    -- Data portability preferences
    auto_export_enabled BOOLEAN NOT NULL DEFAULT false,
    export_frequency INTERVAL DEFAULT '1 month',
    export_format VARCHAR(10) DEFAULT 'json' CHECK (export_format IN ('json', 'csv', 'xml')),
    
    -- Advanced security preferences
    require_2fa_for_sensitive BOOLEAN NOT NULL DEFAULT true,
    session_timeout INTERVAL DEFAULT '24 hours',
    require_reauth_for_deletion BOOLEAN NOT NULL DEFAULT true,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Data Access Log Table
CREATE TABLE IF NOT EXISTS data_access_log (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    
    -- Access details
    access_type VARCHAR(30) NOT NULL CHECK (access_type IN (
        'read', 'write', 'update', 'delete', 'export', 'share', 'process'
    )),
    data_category VARCHAR(50) NOT NULL,
    data_fields TEXT[],
    
    -- Context
    accessor_type VARCHAR(20) NOT NULL CHECK (accessor_type IN ('user', 'system', 'admin', 'third_party', 'api')),
    accessor_id VARCHAR(255),
    access_reason TEXT,
    
    -- Technical details
    ip_address INET,
    user_agent TEXT,
    api_endpoint VARCHAR(255),
    request_id VARCHAR(255),
    
    -- Results
    access_granted BOOLEAN NOT NULL,
    denial_reason TEXT,
    data_volume INTEGER DEFAULT 0,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Privacy Risk Assessment Table
CREATE TABLE IF NOT EXISTS privacy_risk_assessments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    
    -- Risk assessment details
    assessment_type VARCHAR(30) NOT NULL CHECK (assessment_type IN (
        'data_collection', 'data_sharing', 'automated_decision', 'profiling', 'transfer'
    )),
    risk_level VARCHAR(10) NOT NULL CHECK (risk_level IN ('low', 'medium', 'high', 'critical')),
    risk_score DECIMAL(3,2) CHECK (risk_score >= 0 AND risk_score <= 1),
    
    -- Risk factors
    data_sensitivity_level VARCHAR(20) NOT NULL,
    data_volume_category VARCHAR(20) NOT NULL,
    processing_complexity VARCHAR(20) NOT NULL,
    third_party_involvement BOOLEAN DEFAULT false,
    cross_border_transfer BOOLEAN DEFAULT false,
    
    -- Mitigation measures
    mitigation_measures TEXT[],
    residual_risk_level VARCHAR(10),
    
    -- Assessment metadata
    assessor_id UUID REFERENCES auth.users(id),
    assessment_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    review_date TIMESTAMP WITH TIME ZONE,
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'under_review', 'approved', 'rejected')),
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Data Retention Policies Table
CREATE TABLE IF NOT EXISTS data_retention_policies (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Policy details
    policy_name VARCHAR(100) NOT NULL UNIQUE,
    data_category VARCHAR(50) NOT NULL,
    retention_period INTERVAL NOT NULL,
    
    -- Policy rules
    legal_basis VARCHAR(50) NOT NULL,
    business_justification TEXT,
    auto_delete_enabled BOOLEAN NOT NULL DEFAULT true,
    
    -- Exceptions and conditions
    retention_exceptions JSONB DEFAULT '{}',
    deletion_conditions JSONB DEFAULT '{}',
    
    -- Compliance
    regulatory_requirements TEXT[],
    compliance_frameworks TEXT[],
    
    -- Status
    is_active BOOLEAN NOT NULL DEFAULT true,
    effective_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expiry_date TIMESTAMP WITH TIME ZONE,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Privacy Breach Incidents Table
CREATE TABLE IF NOT EXISTS privacy_breach_incidents (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Incident details
    incident_type VARCHAR(30) NOT NULL CHECK (incident_type IN (
        'unauthorized_access', 'data_leak', 'system_breach', 'human_error', 
        'malicious_attack', 'third_party_breach', 'accidental_disclosure'
    )),
    severity_level VARCHAR(10) NOT NULL CHECK (severity_level IN ('low', 'medium', 'high', 'critical')),
    
    -- Affected data
    affected_users_count INTEGER DEFAULT 0,
    affected_data_categories TEXT[],
    data_volume_affected INTEGER DEFAULT 0,
    
    -- Timeline
    incident_discovered_at TIMESTAMP WITH TIME ZONE NOT NULL,
    incident_occurred_at TIMESTAMP WITH TIME ZONE,
    incident_contained_at TIMESTAMP WITH TIME ZONE,
    incident_resolved_at TIMESTAMP WITH TIME ZONE,
    
    -- Response
    notification_required BOOLEAN DEFAULT false,
    authorities_notified BOOLEAN DEFAULT false,
    users_notified BOOLEAN DEFAULT false,
    notification_sent_at TIMESTAMP WITH TIME ZONE,
    
    -- Details
    incident_description TEXT,
    root_cause TEXT,
    remediation_actions TEXT[],
    lessons_learned TEXT,
    
    -- Compliance
    regulatory_reporting_required BOOLEAN DEFAULT false,
    regulatory_report_submitted BOOLEAN DEFAULT false,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_privacy_consents_user_id ON privacy_consents(user_id);
CREATE INDEX IF NOT EXISTS idx_privacy_consents_type ON privacy_consents(consent_type);
CREATE INDEX IF NOT EXISTS idx_privacy_consents_status ON privacy_consents(consent_status);
CREATE INDEX IF NOT EXISTS idx_privacy_consents_created_at ON privacy_consents(created_at);

CREATE INDEX IF NOT EXISTS idx_data_processing_user_id ON data_processing_activities(user_id);
CREATE INDEX IF NOT EXISTS idx_data_processing_type ON data_processing_activities(activity_type);
CREATE INDEX IF NOT EXISTS idx_data_processing_status ON data_processing_activities(status);

CREATE INDEX IF NOT EXISTS idx_advanced_privacy_user_id ON advanced_privacy_preferences(user_id);

CREATE INDEX IF NOT EXISTS idx_data_access_log_user_id ON data_access_log(user_id);
CREATE INDEX IF NOT EXISTS idx_data_access_log_type ON data_access_log(access_type);
CREATE INDEX IF NOT EXISTS idx_data_access_log_created_at ON data_access_log(created_at);

CREATE INDEX IF NOT EXISTS idx_privacy_risk_user_id ON privacy_risk_assessments(user_id);
CREATE INDEX IF NOT EXISTS idx_privacy_risk_level ON privacy_risk_assessments(risk_level);

CREATE INDEX IF NOT EXISTS idx_retention_policies_category ON data_retention_policies(data_category);
CREATE INDEX IF NOT EXISTS idx_retention_policies_active ON data_retention_policies(is_active);

CREATE INDEX IF NOT EXISTS idx_breach_incidents_severity ON privacy_breach_incidents(severity_level);
CREATE INDEX IF NOT EXISTS idx_breach_incidents_discovered ON privacy_breach_incidents(incident_discovered_at);

-- Create updated_at triggers
CREATE TRIGGER update_privacy_consents_updated_at BEFORE UPDATE ON privacy_consents FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_advanced_privacy_preferences_updated_at BEFORE UPDATE ON advanced_privacy_preferences FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_data_retention_policies_updated_at BEFORE UPDATE ON data_retention_policies FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_privacy_breach_incidents_updated_at BEFORE UPDATE ON privacy_breach_incidents FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Enable RLS on all advanced privacy tables
ALTER TABLE privacy_consents ENABLE ROW LEVEL SECURITY;
ALTER TABLE data_processing_activities ENABLE ROW LEVEL SECURITY;
ALTER TABLE advanced_privacy_preferences ENABLE ROW LEVEL SECURITY;
ALTER TABLE data_access_log ENABLE ROW LEVEL SECURITY;
ALTER TABLE privacy_risk_assessments ENABLE ROW LEVEL SECURITY;
ALTER TABLE data_retention_policies ENABLE ROW LEVEL SECURITY;
ALTER TABLE privacy_breach_incidents ENABLE ROW LEVEL SECURITY;

-- Privacy Consents Policies
CREATE POLICY "Users can view own privacy consents" ON privacy_consents
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own privacy consents" ON privacy_consents
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own privacy consents" ON privacy_consents
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Service role can manage privacy consents" ON privacy_consents
    FOR ALL USING (auth.role() = 'service_role');

CREATE POLICY "Admins can view privacy consents" ON privacy_consents
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM user_profiles
            WHERE id = auth.uid()
            AND role IN ('admin', 'super_admin', 'privacy_officer')
        )
    );

-- Data Processing Activities Policies
CREATE POLICY "Users can view own processing activities" ON data_processing_activities
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Service role can manage processing activities" ON data_processing_activities
    FOR ALL USING (auth.role() = 'service_role');

CREATE POLICY "Privacy officers can view processing activities" ON data_processing_activities
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM user_profiles
            WHERE id = auth.uid()
            AND role IN ('admin', 'super_admin', 'privacy_officer')
        )
    );

-- Advanced Privacy Preferences Policies
CREATE POLICY "Users can view own advanced privacy preferences" ON advanced_privacy_preferences
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own advanced privacy preferences" ON advanced_privacy_preferences
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own advanced privacy preferences" ON advanced_privacy_preferences
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Service role can manage advanced privacy preferences" ON advanced_privacy_preferences
    FOR ALL USING (auth.role() = 'service_role');

-- Data Access Log Policies
CREATE POLICY "Users can view own data access logs" ON data_access_log
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Service role can manage data access logs" ON data_access_log
    FOR ALL USING (auth.role() = 'service_role');

CREATE POLICY "Privacy officers can view data access logs" ON data_access_log
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM user_profiles
            WHERE id = auth.uid()
            AND role IN ('admin', 'super_admin', 'privacy_officer')
        )
    );

-- Privacy Risk Assessments Policies
CREATE POLICY "Users can view own risk assessments" ON privacy_risk_assessments
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Service role can manage risk assessments" ON privacy_risk_assessments
    FOR ALL USING (auth.role() = 'service_role');

CREATE POLICY "Privacy officers can manage risk assessments" ON privacy_risk_assessments
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM user_profiles
            WHERE id = auth.uid()
            AND role IN ('admin', 'super_admin', 'privacy_officer')
        )
    );

-- Data Retention Policies (Admin only)
CREATE POLICY "Service role can manage retention policies" ON data_retention_policies
    FOR ALL USING (auth.role() = 'service_role');

CREATE POLICY "Privacy officers can manage retention policies" ON data_retention_policies
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM user_profiles
            WHERE id = auth.uid()
            AND role IN ('admin', 'super_admin', 'privacy_officer')
        )
    );

CREATE POLICY "Users can view active retention policies" ON data_retention_policies
    FOR SELECT USING (is_active = true);

-- Privacy Breach Incidents Policies (Admin only)
CREATE POLICY "Service role can manage breach incidents" ON privacy_breach_incidents
    FOR ALL USING (auth.role() = 'service_role');

CREATE POLICY "Privacy officers can manage breach incidents" ON privacy_breach_incidents
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM user_profiles
            WHERE id = auth.uid()
            AND role IN ('admin', 'super_admin', 'privacy_officer')
        )
    );
