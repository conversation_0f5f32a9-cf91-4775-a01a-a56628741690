import { supabase } from '../lib/supabase';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Platform } from 'react-native';
import Constants from 'expo-constants';

export interface BetaProgram {
  id: string;
  program_key: string;
  program_name: string;
  description?: string;
  program_type: 'feature' | 'app_version' | 'experiment' | 'early_access';
  status: 'draft' | 'recruiting' | 'active' | 'paused' | 'completed' | 'archived';
  max_participants?: number;
  current_participants: number;
  auto_approve: boolean;
  eligibility_criteria: any;
  target_audience: any;
  recruitment_start_date?: string;
  recruitment_end_date?: string;
  program_start_date?: string;
  program_end_date?: string;
  feature_flags: any;
  app_version_requirements: any;
  platform_restrictions: string[];
  feedback_enabled: boolean;
  feedback_frequency: 'daily' | 'weekly' | 'biweekly' | 'monthly' | 'on_demand';
  required_feedback_types: string[];
  welcome_message?: string;
  instructions?: string;
  contact_email?: string;
  tags: string[];
  priority: number;
}

export interface BetaParticipant {
  id: string;
  user_id: string;
  program_id: string;
  status: 'pending' | 'approved' | 'active' | 'paused' | 'completed' | 'removed' | 'declined';
  participation_level: 'observer' | 'standard' | 'power_user' | 'moderator';
  application_reason?: string;
  application_data: any;
  approved_by?: string;
  approved_at?: string;
  approval_notes?: string;
  joined_at?: string;
  last_active_at?: string;
  completion_date?: string;
  feedback_submissions: number;
  bug_reports: number;
  feature_requests: number;
  engagement_score: number;
  email_notifications: boolean;
  push_notifications: boolean;
  weekly_digest: boolean;
  exit_reason?: string;
  exit_feedback?: string;
  exit_date?: string;
}

export interface BetaFeedback {
  id: string;
  participant_id: string;
  program_id: string;
  user_id: string;
  feedback_type: 'bug_report' | 'feature_request' | 'usability' | 'performance' | 'general' | 'survey_response';
  category?: string;
  priority: 'low' | 'medium' | 'high' | 'critical';
  title: string;
  description: string;
  steps_to_reproduce?: string;
  expected_behavior?: string;
  actual_behavior?: string;
  app_version?: string;
  platform?: string;
  device_info: any;
  user_agent?: string;
  screenshots: string[];
  videos: string[];
  logs: any[];
  status: 'open' | 'in_review' | 'in_progress' | 'resolved' | 'closed' | 'duplicate' | 'wont_fix';
  assigned_to?: string;
  resolution_notes?: string;
  resolved_at?: string;
  resolved_by?: string;
  upvotes: number;
  downvotes: number;
  comments_count: number;
  internal_notes?: string;
  tags: string[];
  created_at: string;
  updated_at: string;
}

export interface BetaSurvey {
  id: string;
  program_id: string;
  survey_name: string;
  description?: string;
  survey_type: 'onboarding' | 'feedback' | 'exit' | 'feature_specific' | 'satisfaction';
  questions: any[];
  is_required: boolean;
  is_anonymous: boolean;
  target_participants: any;
  start_date?: string;
  end_date?: string;
  status: 'draft' | 'active' | 'paused' | 'completed' | 'archived';
  total_responses: number;
  completion_rate: number;
}

export interface BetaRelease {
  id: string;
  program_id: string;
  release_name: string;
  version: string;
  description?: string;
  release_notes?: string;
  release_type: 'feature' | 'bugfix' | 'hotfix' | 'major' | 'minor' | 'patch';
  platform: 'all' | 'ios' | 'android' | 'web';
  rollout_strategy: 'immediate' | 'gradual' | 'scheduled';
  rollout_percentage: number;
  target_participants: any;
  scheduled_date?: string;
  released_date?: string;
  status: 'draft' | 'scheduled' | 'releasing' | 'released' | 'paused' | 'rolled_back';
  participants_notified: number;
  download_count: number;
  install_count: number;
  rollback_reason?: string;
  rolled_back_at?: string;
  rolled_back_by?: string;
}

export class BetaTestingService {
  private static instance: BetaTestingService;
  private userParticipations: Map<string, BetaParticipant> = new Map();
  private cacheExpiry: number = 0;
  private readonly CACHE_DURATION = 10 * 60 * 1000; // 10 minutes

  static getInstance(): BetaTestingService {
    if (!BetaTestingService.instance) {
      BetaTestingService.instance = new BetaTestingService();
    }
    return BetaTestingService.instance;
  }

  /**
   * Initialize the beta testing service
   */
  async initialize(): Promise<void> {
    try {
      await this.loadUserParticipations();
      console.log('BetaTestingService initialized successfully');
    } catch (error) {
      console.error('Error initializing BetaTestingService:', error);
    }
  }

  /**
   * Get available beta programs for user
   */
  async getAvailablePrograms(): Promise<BetaProgram[]> {
    try {
      const { data: programs, error } = await supabase
        .from('beta_programs')
        .select('*')
        .in('status', ['recruiting', 'active'])
        .or(`recruitment_start_date.is.null,recruitment_start_date.lte.${new Date().toISOString()}`)
        .or(`recruitment_end_date.is.null,recruitment_end_date.gte.${new Date().toISOString()}`)
        .order('priority', { ascending: false });

      if (error) throw error;

      // Filter programs based on eligibility
      const eligiblePrograms = await this.filterEligiblePrograms(programs || []);
      
      return eligiblePrograms;
    } catch (error) {
      console.error('Error getting available programs:', error);
      return [];
    }
  }

  /**
   * Apply to join a beta program
   */
  async applyToProgram(
    programId: string,
    applicationReason: string,
    applicationData: any = {}
  ): Promise<string | null> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      // Check if user is already a participant
      const { data: existing } = await supabase
        .from('beta_participants')
        .select('id, status')
        .eq('user_id', user.id)
        .eq('program_id', programId)
        .single();

      if (existing) {
        throw new Error(`Already ${existing.status} for this program`);
      }

      // Get program details
      const { data: program } = await supabase
        .from('beta_programs')
        .select('*')
        .eq('id', programId)
        .single();

      if (!program) throw new Error('Program not found');

      // Check eligibility
      const isEligible = await this.checkEligibility(program);
      if (!isEligible) {
        throw new Error('Not eligible for this program');
      }

      // Create application
      const { data: participant, error } = await supabase
        .from('beta_participants')
        .insert({
          user_id: user.id,
          program_id: programId,
          status: program.auto_approve ? 'approved' : 'pending',
          participation_level: 'standard',
          application_reason: applicationReason,
          application_data: {
            ...applicationData,
            device_info: await this.getDeviceInfo(),
            app_version: Constants.expoConfig?.version,
            platform: Platform.OS,
          },
          joined_at: program.auto_approve ? new Date().toISOString() : null,
        })
        .select('id')
        .single();

      if (error) throw error;

      // Update program participant count
      await supabase.rpc('increment_program_participants', {
        program_id: programId,
      });

      // Send welcome notification if auto-approved
      if (program.auto_approve) {
        await this.sendWelcomeNotification(participant.id, program);
      }

      // Clear cache
      this.cacheExpiry = 0;

      return participant.id;
    } catch (error) {
      console.error('Error applying to program:', error);
      throw error;
    }
  }

  /**
   * Get user's beta participations
   */
  async getUserParticipations(): Promise<BetaParticipant[]> {
    try {
      if (this.isCacheValid()) {
        return Array.from(this.userParticipations.values());
      }

      await this.loadUserParticipations();
      return Array.from(this.userParticipations.values());
    } catch (error) {
      console.error('Error getting user participations:', error);
      return [];
    }
  }

  /**
   * Submit feedback for a beta program
   */
  async submitFeedback(
    programId: string,
    feedbackData: {
      feedback_type: string;
      category?: string;
      priority?: string;
      title: string;
      description: string;
      steps_to_reproduce?: string;
      expected_behavior?: string;
      actual_behavior?: string;
      screenshots?: string[];
      videos?: string[];
      logs?: any[];
    }
  ): Promise<string | null> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      // Get participant info
      const participant = await this.getUserParticipation(programId);
      if (!participant || participant.status !== 'active') {
        throw new Error('Not an active participant in this program');
      }

      // Create feedback
      const { data: feedback, error } = await supabase
        .from('beta_feedback')
        .insert({
          participant_id: participant.id,
          program_id: programId,
          user_id: user.id,
          feedback_type: feedbackData.feedback_type,
          category: feedbackData.category,
          priority: feedbackData.priority || 'medium',
          title: feedbackData.title,
          description: feedbackData.description,
          steps_to_reproduce: feedbackData.steps_to_reproduce,
          expected_behavior: feedbackData.expected_behavior,
          actual_behavior: feedbackData.actual_behavior,
          app_version: Constants.expoConfig?.version,
          platform: Platform.OS,
          device_info: await this.getDeviceInfo(),
          screenshots: feedbackData.screenshots || [],
          videos: feedbackData.videos || [],
          logs: feedbackData.logs || [],
          status: 'open',
        })
        .select('id')
        .single();

      if (error) throw error;

      // Update participant feedback count
      await supabase
        .from('beta_participants')
        .update({
          feedback_submissions: supabase.sql`feedback_submissions + 1`,
          last_active_at: new Date().toISOString(),
        })
        .eq('id', participant.id);

      return feedback.id;
    } catch (error) {
      console.error('Error submitting feedback:', error);
      throw error;
    }
  }

  /**
   * Get feedback for a program
   */
  async getProgramFeedback(programId: string, filters: any = {}): Promise<BetaFeedback[]> {
    try {
      let query = supabase
        .from('beta_feedback')
        .select('*')
        .eq('program_id', programId)
        .order('created_at', { ascending: false });

      // Apply filters
      if (filters.feedback_type) {
        query = query.eq('feedback_type', filters.feedback_type);
      }
      if (filters.status) {
        query = query.eq('status', filters.status);
      }
      if (filters.priority) {
        query = query.eq('priority', filters.priority);
      }

      const { data: feedback, error } = await query;
      if (error) throw error;

      return feedback || [];
    } catch (error) {
      console.error('Error getting program feedback:', error);
      return [];
    }
  }

  /**
   * Vote on feedback
   */
  async voteFeedback(feedbackId: string, voteType: 'up' | 'down'): Promise<void> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      // Check if user already voted
      const { data: existingVote } = await supabase
        .from('beta_feedback_votes')
        .select('vote_type')
        .eq('feedback_id', feedbackId)
        .eq('user_id', user.id)
        .single();

      if (existingVote) {
        if (existingVote.vote_type === voteType) {
          // Remove vote
          await supabase
            .from('beta_feedback_votes')
            .delete()
            .eq('feedback_id', feedbackId)
            .eq('user_id', user.id);

          // Update feedback count
          await supabase
            .from('beta_feedback')
            .update({
              [voteType === 'up' ? 'upvotes' : 'downvotes']: supabase.sql`${voteType === 'up' ? 'upvotes' : 'downvotes'} - 1`,
            })
            .eq('id', feedbackId);
        } else {
          // Change vote
          await supabase
            .from('beta_feedback_votes')
            .update({ vote_type: voteType })
            .eq('feedback_id', feedbackId)
            .eq('user_id', user.id);

          // Update feedback counts
          await supabase
            .from('beta_feedback')
            .update({
              upvotes: supabase.sql`upvotes ${voteType === 'up' ? '+ 1' : '- 1'}`,
              downvotes: supabase.sql`downvotes ${voteType === 'down' ? '+ 1' : '- 1'}`,
            })
            .eq('id', feedbackId);
        }
      } else {
        // Add new vote
        await supabase
          .from('beta_feedback_votes')
          .insert({
            feedback_id: feedbackId,
            user_id: user.id,
            vote_type: voteType,
          });

        // Update feedback count
        await supabase
          .from('beta_feedback')
          .update({
            [voteType === 'up' ? 'upvotes' : 'downvotes']: supabase.sql`${voteType === 'up' ? 'upvotes' : 'downvotes'} + 1`,
          })
          .eq('id', feedbackId);
      }
    } catch (error) {
      console.error('Error voting on feedback:', error);
      throw error;
    }
  }

  /**
   * Get available surveys for user
   */
  async getAvailableSurveys(): Promise<BetaSurvey[]> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return [];

      const { data: surveys, error } = await supabase
        .from('beta_surveys')
        .select(`
          *,
          beta_programs!inner(id)
        `)
        .eq('status', 'active')
        .in('beta_programs.id', 
          supabase
            .from('beta_participants')
            .select('program_id')
            .eq('user_id', user.id)
            .eq('status', 'active')
        );

      if (error) throw error;
      return surveys || [];
    } catch (error) {
      console.error('Error getting available surveys:', error);
      return [];
    }
  }

  /**
   * Submit survey response
   */
  async submitSurveyResponse(
    surveyId: string,
    responses: any,
    completionStatus: 'in_progress' | 'completed' = 'completed'
  ): Promise<void> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      // Get participant info
      const { data: survey } = await supabase
        .from('beta_surveys')
        .select('program_id')
        .eq('id', surveyId)
        .single();

      if (!survey) throw new Error('Survey not found');

      const participant = await this.getUserParticipation(survey.program_id);
      if (!participant) throw new Error('Not a participant in this program');

      // Calculate completion percentage
      const totalQuestions = Object.keys(responses).length;
      const answeredQuestions = Object.values(responses).filter(answer => answer !== null && answer !== '').length;
      const completionPercentage = totalQuestions > 0 ? (answeredQuestions / totalQuestions) * 100 : 0;

      // Upsert survey response
      await supabase
        .from('beta_survey_responses')
        .upsert({
          survey_id: surveyId,
          participant_id: participant.id,
          user_id: user.id,
          responses,
          completion_status: completionStatus,
          completion_percentage: completionPercentage,
          completed_at: completionStatus === 'completed' ? new Date().toISOString() : null,
        });

      // Update survey response count if completed
      if (completionStatus === 'completed') {
        await supabase.rpc('update_survey_completion', {
          survey_id: surveyId,
        });
      }
    } catch (error) {
      console.error('Error submitting survey response:', error);
      throw error;
    }
  }

  /**
   * Leave a beta program
   */
  async leaveProgram(programId: string, exitReason?: string, exitFeedback?: string): Promise<void> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      const participant = await this.getUserParticipation(programId);
      if (!participant) throw new Error('Not a participant in this program');

      // Update participant status
      await supabase
        .from('beta_participants')
        .update({
          status: 'completed',
          exit_reason: exitReason,
          exit_feedback: exitFeedback,
          exit_date: new Date().toISOString(),
          completion_date: new Date().toISOString(),
        })
        .eq('id', participant.id);

      // Update program participant count
      await supabase.rpc('decrement_program_participants', {
        program_id: programId,
      });

      // Clear cache
      this.cacheExpiry = 0;
    } catch (error) {
      console.error('Error leaving program:', error);
      throw error;
    }
  }

  // Private helper methods

  private async loadUserParticipations(): Promise<void> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return;

      const { data: participations, error } = await supabase
        .from('beta_participants')
        .select('*')
        .eq('user_id', user.id)
        .in('status', ['pending', 'approved', 'active']);

      if (error) throw error;

      this.userParticipations.clear();
      participations?.forEach(participation => {
        this.userParticipations.set(participation.program_id, participation);
      });

      this.cacheExpiry = Date.now() + this.CACHE_DURATION;
    } catch (error) {
      console.error('Error loading user participations:', error);
    }
  }

  private async filterEligiblePrograms(programs: BetaProgram[]): Promise<BetaProgram[]> {
    const eligiblePrograms: BetaProgram[] = [];

    for (const program of programs) {
      const isEligible = await this.checkEligibility(program);
      if (isEligible) {
        eligiblePrograms.push(program);
      }
    }

    return eligiblePrograms;
  }

  private async checkEligibility(program: BetaProgram): Promise<boolean> {
    try {
      // Check platform restrictions
      if (program.platform_restrictions.length > 0 && 
          !program.platform_restrictions.includes(Platform.OS)) {
        return false;
      }

      // Check app version requirements
      if (program.app_version_requirements && Object.keys(program.app_version_requirements).length > 0) {
        const currentVersion = Constants.expoConfig?.version;
        // Implement version comparison logic here
      }

      // Check max participants
      if (program.max_participants && program.current_participants >= program.max_participants) {
        return false;
      }

      // Check eligibility criteria
      if (program.eligibility_criteria && Object.keys(program.eligibility_criteria).length > 0) {
        // Implement eligibility criteria evaluation
        // This would check user profile, subscription status, etc.
      }

      return true;
    } catch (error) {
      console.error('Error checking eligibility:', error);
      return false;
    }
  }

  private async getUserParticipation(programId: string): Promise<BetaParticipant | null> {
    if (this.isCacheValid()) {
      return this.userParticipations.get(programId) || null;
    }

    await this.loadUserParticipations();
    return this.userParticipations.get(programId) || null;
  }

  private async getDeviceInfo(): Promise<any> {
    return {
      platform: Platform.OS,
      version: Platform.Version,
      app_version: Constants.expoConfig?.version,
      expo_version: Constants.expoVersion,
      device_name: Constants.deviceName,
      device_year_class: Constants.deviceYearClass,
    };
  }

  private async sendWelcomeNotification(participantId: string, program: BetaProgram): Promise<void> {
    try {
      // This would integrate with the notification service
      console.log(`Sending welcome notification for program: ${program.program_name}`);
    } catch (error) {
      console.error('Error sending welcome notification:', error);
    }
  }

  private isCacheValid(): boolean {
    return Date.now() < this.cacheExpiry;
  }
}
